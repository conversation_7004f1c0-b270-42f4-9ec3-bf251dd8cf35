import layoutMenu from "@/components/layoutMenu/index.vue";

export default [
  {
    path: "/talkEducation",
    name: "talkEducation",
    meta: {
      title: "谈话教育",
    },
    component: layoutMenu,
    children: [
      {
        meta: {
          title: "个别谈话",
        },
        path: "individualTalk",
        name: "individualTalk",
        sider: true,
        bread: true,
        redirect: "/talkEducation/individualTalk/education/index",
        component: () => import("@/components/empty-router-view/index.vue"),
        children: [
          {
            path: "education",
            name: "individualTalkEducation",
            meta: {
              title: "个别谈话教育",
              menu: true,
              bread: true,
            },
            redirect: "/talkEducation/individualTalk/education/index",
            component: () => import("@/components/empty-router-view/index.vue"),
            children: [
              {
                path: "index",
                name: "individualTalkEducationList",
                meta: {
                  title: "个别谈话教育",
                  menu: true,
                  bread: true,
                },
                component: () =>
                  import(
                    "@/view/talkEducation/individualTalk/education/index.vue"
                  ),
              },
              {
                path: "add",
                name: "individualTalkEducationListAdd",
                meta: {
                  title: "个别谈话教育-新增谈话",
                  menu: true,
                  bread: true,
                },
                component: () =>
                  import(
                    "@/view/talkEducation/individualTalk/education/add.vue"
                  ),
              },
              {
                path: "update",
                name: "individualTalkEducationListUpdate",
                meta: {
                  title: "个别谈话教育-详情",
                  menu: true,
                  bread: true,
                },
                component: () =>
                  import(
                    "@/view/talkEducation/individualTalk/education/add.vue"
                  ),
              },
            ],
          },
          {
            meta: {
              title: "业务台账",
            },
            path: "business",
            name: "individualTalkBusiness",
            sider: true,
            bread: true,
            component: () => import("@/components/empty-router-view/index.vue"),
            children: [
              {
                path: "index",
                name: "individualTalkBusinessList",
                meta: {
                  title: "业务台账",
                  menu: true,
                  bread: true,
                },
                component: () =>
                  import(
                    "@/view/talkEducation/individualTalk/business/index.vue"
                  ),
              },
              {
                path: "detail",
                name: "individualTalkEducationListDetail",
                meta: {
                  title: "业务台账-详情",
                  menu: true,
                  bread: true,
                },
                component: () =>
                  import(
                    "@/view/talkEducation/individualTalk/business/detail.vue"
                  ),
              },
            ],
          },
        ],
      },
      {
        meta: {
          title: "谈话模板",
        },
        path: "talkTemplate",
        name: "talkTemplate",
        sider: true,
        bread: true,
        component: () =>
          import("@/view/windowBusiness/provideSolutions/index.vue"),
      },
      {
        meta: {
          title: "集体谈话",
        },
        path: "groupTalk",
        name: "groupTalk",
        sider: true,
        bread: true,
        component: () =>
          import("@/view/windowBusiness/provideSolutionsJls/index.vue"),
      },
      {
        meta: {
          title: "谈话人员标签",
        },
        path: "talkTag",
        name: "talkTag",
        sider: true,
        bread: true,
        component: () =>
          import("@/view/windowBusiness/deliveryService/index.vue"),
      },
    ],
  },
];
