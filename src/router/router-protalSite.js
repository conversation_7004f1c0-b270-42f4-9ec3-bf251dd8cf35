import layoutMenu from '@/components/layoutMenu/index.vue'

export default [

  {
    path: '/common',
    name: 'common',
    meta: {
      title: '实战系统'
    },
    sider: false,
    bread: true,
    component: layoutMenu, //menuMode == 'side' ? mainNew : main,
    children: [{
        meta: {
          title: '首页',
          hideInMenu: true
        },
        path: '/homePage',
        name: 'homePage',
        sider: false,
        bread: true,
        component: () => import('@/components/portalSite/index.vue')
      },
      {
        path: '/agencyNews',
        name: 'agencyNews',
        meta: {
          title: '领导审批',
          hideInMenu: true
        },
        component: () => import('@/view/agencyNews/index.vue')
      },
      {
        path: '/messageNotification',
        name: 'messageNotification',
        meta: {
          title: '消息通知',
          hideInMenu: true
        },
        component: () => import('@/components/mhPageComponents/messageNotification/index.vue')
      },
      {
        path: '/drillDown',
        name: 'drillDown',
        meta: {
          title: '详情',
          hideInMenu: true
        },
        component: () => import('@/components/drillDown/index.vue')

      },
    ]
  },

]
