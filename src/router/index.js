import Vue from 'vue'
import Router from 'vue-router'
import routes from './routers'
import ihcRoutes from './ihc/routers'
import temRoutes from './tem/routers'
import ptmRoutes from './ptm/routers'
import damRoutes from "./dam/routers"
import store from '@/store'
import iView from 'view-design'
import {
  getToken,
  setTitle
} from '@/libs/util'
import config from '@/config'
const {
  homeName
} = config
console.log(routes, 'routes')
console.log(ihcRoutes, 'ihcRoutes')
console.log(temRoutes, 'temRoutes')
// 获取原型对象push函数
const originalPush = Router.prototype.push;
// 获取原型对象replace函数
const originalReplace = Router.prototype.replace;

// 修改原型对象中的push函数
Router.prototype.push = function push(location) {
  return originalPush.call(this, location).catch((err) => err);
};

// 修改原型对象中的replace函数
Router.prototype.replace = function replace(location) {
  return originalReplace.call(this, location).catch((err) => err);
};
Vue.use(Router)

/**
 * 通用路由过滤函数，过滤掉错误页面路由避免冲突
 * @param {Array} routeArray - 需要过滤的路由数组
 * @param {string} routeName - 路由数组名称，用于日志输出
 * @returns {Array} 过滤后的路由数组
 */
function filterErrorRoutes(routeArray, routeName = 'routes') {
  // 定义需要过滤的错误页面路径和名称
  const errorPaths = ['/401', '/500', '*']
  const errorNames = ['error_401', 'error_500', 'error_404']

  const filteredRoutes = routeArray.filter(route => {
    return !errorPaths.includes(route.path) && !errorNames.includes(route.name)
  })
  // console.log(filteredRoutes, `processed${routeName}`)
  return filteredRoutes
}

// 使用通用函数过滤各个路由数组
const processedIhcRoutes = filterErrorRoutes(ihcRoutes, 'IhcRoutes')
const processedTemRoutes = filterErrorRoutes(temRoutes, 'TemRoutes')
const processedPtmRoutes = filterErrorRoutes(ptmRoutes, 'PtmRoutes')
const processedDamRoutes = filterErrorRoutes(damRoutes, 'DamRoutes')
const router = new Router({
  routes: [...routes, ...processedIhcRoutes, ...processedTemRoutes, ...processedPtmRoutes, ...processedDamRoutes],
  mode: 'hash'
})
const LOGIN_PAGE_NAME = 'login'

router.beforeEach((to, from, next) => {
  iView.LoadingBar.start()
  const token = getToken()

  const ssoToken = to.query.singleSignOnToken || to.query.token

  if (ssoToken) {
    // 有单点登录token但没有本地token，进行单点登录验证
    console.log('检测到单点登录token，开始验证:', ssoToken)
    store.dispatch('handleSingleSignOn', {
        token: ssoToken
      })
      .then(() => {
        console.log('单点登录验证成功，跳转到目标页面')
        // 移除URL中的token参数
        const query = {
          ...to.query
        }
        delete query.singleSignOnToken
        delete query.token

        next({
          path: to.path,
          query: query,
          replace: true
        })
      })
      .catch(error => {
        console.error('单点登录验证失败:', error)
        // 单点登录失败，跳转到登录页
        next({
          name: LOGIN_PAGE_NAME,
          query: {
            redirect: to.path,
            error: '单点登录验证失败'
          }
        })
      })
    return
  }

  if (!token && to.name !== LOGIN_PAGE_NAME) {
    // 未登录且要跳转的页面不是登录页
    router.replace({
      name: LOGIN_PAGE_NAME,
      query: {
        redirect: to.path
      }
    })
  } else if (!token && to.name === LOGIN_PAGE_NAME) {
    // 未登陆且要跳转的页面是登录页
    next() // 跳转
  } else if (token && to.name === LOGIN_PAGE_NAME) {
    // 已登录且要跳转的页面是登录页
    next({
      name: homeName // 跳转到homeName页
    })
  } else {
    // console.log(to,from,'homeName')
    if (to.name == 'error_404' && to.path.indexOf('/home') > -1) {
      next({
        name: from.name // home 404重新处理
      })
    } else {
      next()
    }
  }
})

router.afterEach(to => {
  setTitle(to, router.app)
  iView.LoadingBar.finish()
  window.scrollTo(0, 0)
})

export default router
