<!-- 
--- 组件使用说明：
--- 本组件使用 video.js 封装
--- 官方文档地址： https://videojs.com/
---------------------------------------------
--- 接收参数
--- containerStyleObj 父元素样式对象
--- playerStyleObj 播放器样式对象
--- options 传入配置参数
---------------------------------------------
--- 内置方法【在外层使用使用 this.$refs.funName 进行调用 】
--- createPlayer 创建播放器
--- initWebRtcPlayer 初始化 WebRtc 播放器
--- initRegularPlayer 初始化普通流播放器
--- destroyPlayer 销毁播放器
--- onEvent 监听事件
--- handleZoom 监听全屏点击事件
----------------------------------------------
--- 因为增加了对 webRTC 视频流的支持，所以在public/index.html中增加了
--- adapter-7.4.0.min.js
--- srs.sdk.js
-->

<template>
  <!-- 因为调用 videojs.dispose 方法会彻底销毁含有 data-vjs-player 属性以内的所有 DOM, 所以嵌套了一层结构 -->
  <div :key="key" :style="containerStyleObj" class="video-container">
    <div :style="containerStyleObj" data-vjs-player>
      <video class="video-js" :style="playerStyleObj" ref="videoPlayer" />
    </div>
    <!-- 如果显示原生的controls bar 则隐藏自定义 controls bar -->
    <div v-show="options?.controls === 2" class="custom-control-bar">
      <button
        v-show="typeof options.zoom !== 'undefined'"
        class="custom-fullscreen-btn"
        @click="handleZoom"
      >
        {{ options.zoom ? "移动到左侧" : "移动到右侧" }}
      </button>
    </div>
  </div>
</template>

<script>
export default {
  name: "VideoPlayer",
  props: {
    containerStyleObj: {
      type: Object,
      default: () => ({}),
    },
    playerStyleObj: {
      type: Object,
      default: () => ({}),
    },
    // options 属性说明
    // options.controls  0：不显示任何控制栏  1仅显示原生控制栏 2仅显示自定义控制栏
    options: {
      type: Object,
      default: () => ({}),
    },
    zoom: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      player: {},
      url: "",
      defalutOptions: {
        autoplay: true,
        controls: true,
        language: "zh-CN",
        bigPlayButton: false,
      },
      key: 0,
      sdk: null,
      loading: false,
      zoomState: false,
    };
  },
  created() {},
  beforeDestroy() {
    console.log("video component will be destroy.");
    this.destroyPlayer();
  },
  methods: {
    /**
     * 创建播放器
     * @param {String | Promise } url 两种参数
     * 1、因为新增加了webRTC功能的路径，所以增加了相应判断。
     * * 1.1、路径地址 --- 如 http(s)://domain(:port)/path/videoName.[videoType]
     * * 1.2、webRTC路径 --- 目前的设备的路径包含 /rtc/v1/whep 随着设备的升级或厂家的更换，该地址链接也会发生改变
     * 2、stream流地址，类型为blob或promise
     */
    createPlayer(url) {
      if (Object.keys(this.player).length && url !== this.url) {
        this.destroyPlayer();
      }
      if (url) {
        if (url === this.url) {
          return this.$Message.error("视频设备已连接，请勿重复操作。");
        }
        this.loading = true;
        const combineOptions = { ...this.defalutOptions, ...this.options };
        if (typeof url === "string") {
          // 坑点，不是每个webRTC的视频地址都包含/rtc/v1/whep，
          // 但是目前没有去做预先请求后，去判断视频协议，再根据视频协议动态切换的功能
          if (url.includes("/rtc/v1/whep")) {
            this.initWebRtcPlayer(url, combineOptions);
          } else {
            this.initRegularPlayer(url, combineOptions);
          }
        } else {
          this.player = this.videojs(this.$refs.videoPlayer, {});
          const player = this.player.tech(true).el();
          player.srcObject = url;
          player.play();
        }
        this.onEvent();
      } else {
        this.$Message.error("视频创建失败，未获取到视频地址。");
      }
    },
    destroyPlayer() {
      if (this.sdk) {
        this.sdk.close();
        this.sdk = null;
      }
      if (this.$refs.videoPlayer.srcObject) {
        this.$refs.videoPlayer.srcObject
          .getTracks()
          .forEach((track) => track.stop());
        this.$refs.videoPlayer.srcObject = null;
      }
      this.player?.dispose();
      this.key = Math.random();
    },
    // 初始化 webRTC 播放器
    async initWebRtcPlayer(url, combineOptions) {
      try {
        this.sdk = new SrsRtcWhipWhepAsync();
        this.player = this.videojs(this.$refs.videoPlayer, {
          ...combineOptions,
          controls: combineOptions.controls === 1
        });
        const player = this.player.tech(true).el();
        player.srcObject = this.sdk.stream;

        // 开始播放
        const session = await this.sdk.play(url);
        console.log("WebRtc播放会话建立:", session);
      } catch (error) {
        console.error("WebRtc播放失败:", error);
        this.destroyPlayer();
      }
    },
    // 初始化 普通地址流 播放器
    initRegularPlayer(url, combineOptions) {
      this.url = url;
      this.player = this.videojs(this.$refs.videoPlayer, {
        ...combineOptions,
        controls: combineOptions.controls === 1,
        sources: [
          {
            src: url,
            type: "video/" + url.split(".").reverse()[0],
          },
        ],
      });
    },
    onEvent() {
      const self = this;
      this.player?.on("ready", function () {
        this.loading = false;
        console.log("The video player is already!");
      });

      this.player?.on("abort", function (e) {
        console.log("The video player is abort!", e);
      });

      this.player?.on("error", function (e) {
        this.loading = false;
        console.log("The video player had something wrong!", e);
      });
    },
    handleZoom() {
      this.$emit("handleZoom", !this.zoomState);
    },
  },
};
</script>

<style lang="less" scoped>
.video-container {
  position: relative;
  .custom-control-bar {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    padding: 10px;
    z-index: 1;
    justify-content: flex-end;
  }

  .custom-fullscreen-btn {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    margin: 0 5px;
    opacity: 0.8;
    font-size: 12px;
    font-weight: bold;
  }
}
</style>
