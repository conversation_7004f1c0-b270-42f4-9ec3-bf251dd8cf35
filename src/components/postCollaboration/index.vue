<template>
    <div class="post-ollaboration">
        <FormItem label="是否岗位协同" prop="isPostCoordination">
            <RadioGroup v-model="formData.isPostCoordination" @on-change="changeIsPostCoordination">
                <Radio label="1">是</Radio>
                <Radio label="0">否</Radio>
            </RadioGroup>
        </FormItem>
        <FormItem label="岗位协同" v-if="formData.isPostCoordination == '1'">
            <CheckboxGroup v-model="formData.coordinationPosts" @on-change="changecoordinationPosts">
                <Checkbox v-for="(item, index) in coordinationPostsList" :key="item.title" :label="item.title">{{
                    item.title }}</Checkbox>
            </CheckboxGroup>
        </FormItem>
        <FormItem label="推送对象" v-if="formData.isPostCoordination == '1'">
            <user-selector v-model="formData.pushTargetIdCards" tit="用户选择" @onSelect="onSelectUs"
                :text.sync="formData.pushTarget" returnField="idCard" :selectPost="true">
            </user-selector>
        </FormItem>
        <FormItem label="推送内容" v-if="formData.isPostCoordination == '1'">
            <Input placeholder="请输入推送内容" v-model="formData.pushContent"></Input>
        </FormItem>
    </div>
</template>


<script>
import { userSelector } from 'gs-user-selector'
export default {
    components: {
        userSelector
    },

    data() {
        return {
            formData: {
                isPostCoordination: '0'
            },
            coordinationPostsList: []
        }
    },
    mounted() {
        this.getData()
    },
    methods: {
        getData() {
            this.$store.dispatch('getRequest', { url: this.$path.com_listByDutySingleDateIndex, params: {} }).then(resp => {
                if (resp.success) {
                    this.coordinationPostsList = resp.data
                    this.coordinationPostsList.forEach(item => {
                        for (let i in item) {
                            this.$set(item, 'title', i)
                            this.$set(item, 'arrRy', item[i])
                        }

                    })
                }
            }
            )
        },
        changeIsPostCoordination(val) {
            this.formData.coordinationPosts = []
            this.formData.pushTarget = ''
            this.formData.pushTargetIdCard = ''
            this.formData.pushContent = ''
        },
        onSelectUs(data) {
            const res = data.map(item => {
                return {
                    orgCode: item.orgCode,
                    idCard: item.idCard
                }
            }
            )
            this.formData.pushTargetIdCard = res;
            this.pushText()
        },
        changecoordinationPosts() {

        }
    }
}



</script>