.prison-select{
    display: flex;
    width: 100%;
    font-family: <PERSON><PERSON><PERSON><PERSON><PERSON>, MicrosoftYaHei;
    font-weight: normal;
    font-size: 16px;
    color: #00244A;
    .prison-select-left{
        width:17%;
        max-height: 500px;
        overflow-y: overlay;
        .roomNameWrap{
            .roomName{
                color: rgb(37, 37, 37);
                font-size: 16px;
                text-indent: 60px;
                line-height: 32px;
                cursor: pointer;
                &:hover{
                    background: #f1f9fa;
                }
            }
            .roomName:last-of-type{
                border-bottom: 1px solid #f1f9fa;
            }}
            .prison-select-left-child.active{
                color:#027bff !important ;
            }
        .prison-select-left-child{
            &:hover{
                color:#027bff ;
            }
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;
            // border-right: 1px solid #f1f9fa;
            border-bottom: 1px solid #f1f9fa;
            padding: 7px 0;
            cursor: pointer;
            // color: rgb(37, 37, 37);
            font-size: 17px;
            .prison-select-left-childDiv{
                height: 50px;
                width: 50px;
                background: #f1f9fa;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 6px;
                margin-right: 10px;
                
            }
            .jsName{
                // color: rgb(37, 37, 37);
                // font-size: 17px;
                // &:hover{
                //     color:#027bff ;
                // }
            }


           }
          
        }
        .prison-select-left-child:last-of-type{
            border-bottom: none;
        }
    .prison-select-center{
        width: 70%;
        border-right: 1px solid #f1f9fa;
        border-left: 1px solid #f1f9fa;
        .use-list{
            border-top: 1px solid #f1f9fa;
            // position: relative;
        }
    }
    .prison-select-right{
        width: 13%;
        border-left: 1px solid #f1f9fa;
        .prison-select-right-title{
          border-left: 4px solid #2b5fda;
          padding-left: 10px;
          margin: 10px 0;
        //   height: 20px;
        //   line-height: 20px;
        //   border-bottom: 1px solid #f1f9fa;

        }
        .selectedUseWrap{
            text-align: center;
            width: 100%;
            height:580px;
            overflow-y: visible;
            overflow-x: hidden;
            padding-right: 10px;
            .selectedUse{
                width: 100%;
                padding: 6px 1px;
                background: #73b4f8;
                font-size: 14px;
                margin:0 4% 10px;
                border-radius: 4px;
           }
        }

    }
}

.use-form-title{
 display: flex;
 min-width: 46px;
 font-size: 16px;
//  margin-left: 16px;
}
.use-list{
    width: 100%;
    display: flex;
    padding: 16px 0 0px 10px;
    // align-items: center;
    flex-wrap: wrap;
    font-family: MicrosoftYaHei, MicrosoftYaHei;
    font-weight: normal;
    font-size: 16px;
    color: #00244A;
    height: 525px;
    overflow-y: auto;
    .use-list-box{
        width: 32%;
        height: 210px;
        border: 1px solid  #c9ddf0;
        border-radius: 6px;
        margin:0 10px 10px 0;
        padding: 10px;
        box-shadow: 0px 6px 14px 1px rgba(7,31,88,0.1);
        cursor: pointer;
        position: relative;
        &:hover{
            border: 1px solid #2390FF;
            
        }
    }
}
.user-flex{
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.user-flex-sm{
    display: flex;
    align-items: center;
    position: relative;
    width: 100%;
}
.ivu-btn {
    font-family: MicrosoftYaHei, MicrosoftYaHei;
    font-weight: normal;
    font-size: 16px;
    // color: #00244A;
}
.grayBg{
    background: #daebfa;
    padding:3px 6px;
    border-radius: 4px;
    // margin-top: -6px;
}
.fontType{
    font-style: normal;
    font-size: 18px;
}
.redColor{
    color: red;
}
.blueColor{
    color: #3473ca;
}
.ZS{
    background-color: #2b5fda;
    color: #f1f9fa;
    padding:2px 6px;
    border-radius: 4px;
    position: absolute;
    right: 0;
    // font-size: 14px;
}
.user-flex .rybh{
    font-size: 14px;
    display: flex;
    align-items: center;
    width: 100%;
}
.userName{
    font-size:18px;
    &01 {
        color: @redColor;
    }
    &02 {
        color: @yellowColor;
    }
    &03 {
        color: @greenColor;
    }
    &04 {
        color: @blueColor;
    }

}
@redColor: #ed4014;      // 衣服颜色
@yellowColor: #ff9900;   
@greenColor: #19be6b;   
@blueColor: #2db7f5;   
.user-number {
    font-size: 30px;
    font-weight: bold;
    width: 52px;
    height: 51px;
    
    position: relative;
    text-align: center;
    line-height: 60px;
    .circle {
        position:absolute;
			left:9px;
			top:-1px;
			width:31px;
			height:18px;
			border-radius:0px 0px 40px 40px;/*设置左下角的圆角
			边框*/
			background-color:white;
    }
    &01 {
        border: 1px solid @redColor;
        .circle {
            border-right:1px solid @redColor;
			border-bottom:1px solid @redColor;
            border-left:1px solid @redColor;
        }
    }
    &02 {
         border: 1px solid @yellowColor;
        .circle {
            border-right:1px solid @yellowColor;
			border-bottom:1px solid @yellowColor;
            border-left:1px solid @yellowColor;
        }
    }
    &03 {
        border: 1px solid @greenColor;
        .circle {
            border-right:1px solid @greenColor;
			border-bottom:1px solid @greenColor;
            border-left:1px solid @greenColor;
        }
    }
    &04 {
        border: 1px solid @blueColor;
        .circle {
            border-right:1px solid @blueColor;
			border-bottom:1px solid @blueColor;
            border-left:1px solid @blueColor;
        }
    }

}

.zgWrap{
    margin-top: 3px;
    border-top: 1px dashed #f1f9fa;
    padding-top: 8px;
}
.use-list-box.checked{
  border-color: #027bff !important;
}
.checkedIcon{
    position: absolute;
    left: -4px;
    top: -4px;
}
.pageWrap{
    display: flex;
    justify-content: flex-end;
    padding: 0 16px 6px 0;
    margin-top: 6px;
    // border-top: 1px solid #f1f9fa;
    width: 100%;
}
.prison-select-left-child:first-of-type .jsName{
    font-size:18px;
}
.userXb{
    position: absolute;
    right: 10px;
}
.noData{
    text-align: center;
    font-family: MicrosoftYaHei, MicrosoftYaHei;
    font-weight: normal;
    font-size: 16px;
    color: #A2ACC6;
    margin: 40% 0;
}
.noUseData{
    text-align: center;
    font-size: 20px;
    margin: 10% auto;
    color: #A2ACC6;
    font-family: MicrosoftYaHei, MicrosoftYaHei;
    font-weight: normal;

}
.rybhZs{
    width: 65%;
    display: inline-block;
    overflow: hidden;
    text-overflow:ellipsis;overflow:hidden;word-break:break-all;white-space:nowrap
}
.roomName.active{
    color:#027bff !important ;
}