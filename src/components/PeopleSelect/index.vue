<!--
 --- 人员选择组件 V1.0.0
 --- 本组件的开发初衷是为了完成所情处置中多类型人员选择
 --- 目前仅支持 worker 工作人员 | outer 外来人员 | prisoner 被监管人员
 -------------------------------------------------------------------
 --- props 说明
 --- options.limit 0为不限制，否则为限制指定的数
 --- options.title 自定义弹窗标题，如不设置则根据初始化数据映射
 --- type 仅支持 worker 工作人员 | outer 外来人员 | prisoner 被监管人员
 --------------------------------------------------------------------
 --- emit 事件
 --- select 返回所有选中数据
-->
<template>
  <Modal v-model="visible" class-name="modal" :mask-closable="false" :closable="true" width="1360"
    :title="options.title || requestObj[type].title">
    <Row class="peopleSelect-container">
      <Col class="cate-select-box" v-loading="cateSelectLoading" :span="4">
      <ul class="cate-list" v-if="cateList.length > 0">
        <li @click.stop="handleCateClick()" :class="{ 'cate-list-level_1': true, 'active': !formData.ryType }">
          <div class="title">全部</div>
        </li>
        <li v-for="item in cateList" :class="{ 'cate-list-level_1': true, 'active': formData.ryType === item.code }"
          @click.stop="handleCateClick(item.code)" :key="item.id">
          <div class="title">{{ item.name }}</div>
          <!-- 因为只有监室的数据下有children，所以暂时写死 -->
          <ul v-if="item.children" :style="{ height: formData.ryType === item.code ? '100%' : '0', overflow: 'hidden', transition: 'height 0.15s linear' }">
            <li v-for="i in item.children"
              :class="{ 'cate-list-level_2': true, 'active': formData.roomCode === i.roomCode }" @click.stop="
                handleSubCateClick(i.roomCode, item.code)" :key="i.roomCode">
              {{
                i.roomName }}</li>
          </ul>
        </li>
      </ul>
      <div v-else style="text-align: center;margin-top: 50px;">未获取到分类数据</div>
      </Col>
      <Col class="person-select-box" :span="16">
      <Form :model="formData" ref="form">
        <div class="search-box">
          <Input style="width: unset;flex: 1;" v-model="formData.xm" placeholder="请输入姓名"></Input>
          <div style="margin-left: 10px;">
            <Button type="primary" @click="handleSearch">搜索</Button>
            <Button style="margin-left: 10px;" @click="handleReset">重置</Button>
          </div>
        </div>
      </Form>
      <div class="people-box" v-loading.body="peopleSelectLoading">
        <template v-if="peopleList.length">
          <div :class="{ 'person-box': true, 'selected': selectedMap.has(item.id) }" v-for="item in peopleList"
            :key="item.id" @click="handlePersonSelect(item)">
            <div class="img-box">
              <img :src="item.zpUrl || defaultImg" alt="">
              <!-- 功能埋点-将来使用图标+背景色的方式 -->
              <!-- <span class="gender">{{ item.xbName }}</span> -->
            </div>
            <span class="name">{{ item.xm || '未找到姓名' }}</span>
          </div>
        </template>
        <noData v-else></noData>
      </div>
      <div style="display: flex; justify-content: flex-end;">
        <Page :total="formData.total" size="small" @on-change="handlePageNoChange" show-total
          :page-size="formData.pageSize" />
      </div>
      </Col>
      <Col class="people-chose-box" :span="4">
      <h3>已选择人数（{{ choseList.length || 0 }}{{ options.limit > 0 ? "/" + options.limit : "" }}）</h3>
      <template v-if="choseList.length">
        <Tag v-for="(item, index) in choseList" :key="item.id" color="default" closable
          @on-close="handleRemoveSelect(index, item.id)">{{
            item.xm }}</Tag>
      </template>
      <div v-else style="margin-top: 10px;">
        暂未选择任何人员。
      </div>
      </Col>
    </Row>
    <div slot="footer">
      <Button type="primary" @click="handleSubmit" class="save">确 定</Button>
      <Button @click="handleClose">关 闭</Button>
    </div>
  </Modal>
</template>

<script>
import api from "./api.js";
import { mapActions } from "vuex";
import noData from "@/components/noData";
import defaultImg from "@/assets/images/detentionEnter/person.png";
export default {
  name: "PeopleSelect",
  components: {
    noData
  },
  props: {
    options: {
      type: Object,
      default: () => {
        return {
          limit: 0, // 0表示不限制
        }
      }
    },
    type: {
      type: String,
      default: "prisoner",
      validator: (value) => {
        // worker 工作人员 | outer 外来人员 | prisoner 被监管人员
        // 后期需扩展监管人员类别
        const result = !value || ["worker", "outer", "prisoner"].includes(value);
        result || console.error("人员选择组件，type参数错误，只能是worker 或 outer 、prisoner")
        return result
      }
    }
  },
  data() {
    return {
      defaultImg,
      visible: false,
      formData: {
        xm: "",
        total: 0,
        pageNo: 1,
        pageSize: 18,
        ryType: "",
        roomCode: ""
      },
      cateList: [],
      peopleList: [],
      choseList: [],
      inputChoseList: [],
      selectedMap: new Map(),
      cateSelectLoading: false,
      peopleSelectLoading: false,
      // 后端没有统一请求与返回数据类型····
      requestObj: {
        "worker": {
          method: "postRequest",
          api: api.getList,
          dictName: "ZD_GZRYLX",
          title: "工作人员选择",
          params: {
            modelId: serverConfig.APP_CODE + ":rycxzj-gzrylb",
          }
        },
        "outer": {
          method: "authPostRequest",
          api: api.workerList,
          dictName: "ZD_WLRYLX",
          title: "外来人员选择",
          params: {
          }
        },
        "prisoner": {
          method: "authPostRequest",
          api: api.prisonerList,
          dictName: "", // 特殊项
          title: "监管人员选择",
          params: {
            ryzt: "ZS"
          }
        }
      }
    };
  },
  created() {},
  methods: {
    ...mapActions([
      "authGetRequest",
      "authPostRequest",
    ]),
    // 打开该组件
    open(list) {
      this.visible = true;
      this.getCateDict();
      this.choseList = [].concat(list);
      this.inputChoseList = [].concat(list);
      list.foreach(item => {
        this.selectedMap.set(item.id, item);
      })
    },
    // 获取左侧分类数据
    getCateDict() {
      this.cateSelectLoading = true;
      const requestObj = this.requestObj[this.type];
      const url = this.type !== 'prisoner' ? api.getDict : api.getJailArea;
      const params = this.type !== 'prisoner' ? { dicName: requestObj.dictName } : { orgCode: this.$store.state.common.orgCode }
      this.authGetRequest({
        url,
        params,
      }).then((res) => {
        this.cateSelectLoading = false;
        if (res.success) {
          const convertedList = res.data || [];
          this.cateList = convertedList.map(item => {
            if (item.areaCode) {
              item.id = item.areaCode;
              item.name = item.areaName;
              item.ryType = item.areaCode;
              item.code = item.areaCode
            }
            return item;
          });
          // this.formData.ryType = this.cateList[0].ryType;
          this.getPeopleList();
        } else {
          this.$Message.error("获取类型字典失败");
        }
      });
    },
    // 获取所属分类下人员信息【分页】
    getPeopleList() {
      this.peopleSelectLoading = true;
      const requestObj = this.requestObj[this.type];
      if (this.type === "worker") {
        const condisArray = [
          { "name": "xm", "op": "like", "value": this.formData.xm, "valueType": "string" },
        ];
        if (this.formData.ryType) {
          condisArray.push({ "name": "ry_type", "op": "=", "value": this.formData.ryType, "valueType": "string" })
        }
        requestObj.params.condis = JSON.stringify(condisArray);
      }

      if (this.type === 'prisoner') {
        this.formData.orgCode = this.$store.state.common.orgCode;
      }

      this.$store
        .dispatch(requestObj.method, {
          url: requestObj.api,
          params: { ...requestObj.params, ...this.formData }
        })
        .then((res) => {
          this.peopleSelectLoading = false;
          if (res.success) {
            if (this.type === 'worker') {
              const convertedList = res?.rows || [];
              this.peopleList = convertedList.map(item => {
                item.zpUrl = item.zp_url;
                item.ryType = item.ry_type;
                return item;
              });
              this.formData.total = res.total;
            } else if (this.type === 'prisoner') {
              this.peopleList = res.data.list.map(item => {
                item.zpUrl = item.frontPhoto;
                return item;
              });
              this.formData.total = res.data.total;
            } else {
              this.peopleList = res.data.list;
              this.formData.total = res.data.total;
            }
          } else {
            this.$Message.error("人员列表获取失败")
          }
        });
    },
    // 点击一级分类
    handleCateClick(code) {
      this.formData.ryType = code;
      if (this.type === 'prisoner') {
        this.formData.areaId = code;
        this.formData.roomCode = "";
        this.formData.jsh = "";
      }
      this.getPeopleList();
    },
    // 点击二级分类
    handleSubCateClick(code, areaId) {
      this.formData.areaId = areaId;
      this.formData.roomCode = code;
      this.formData.jsh = code;
      this.getPeopleList();
    },
    // 重置搜索条件
    handleReset() {
      this.$refs.form.resetFields();
      this.formData.xm = "";
      this.formData.pageNo = 1;
      this.getPeopleList();
    },
    // 搜索
    handleSearch() {
      this.getPeopleList();
    },
    // 选中人员
    handlePersonSelect(item) {
      const limit = this.options.limit;
      if (limit > 0 && this.choseList.length >= limit) {
        return this.$Message.error(`选择人数不能超过${limit}人`);
      }
      if (!this.selectedMap.has(item.id)) {
        this.selectedMap.set(item.id, item);
        this.choseList.push(item);
      } else {
        const index = Array.from(this.selectedMap.entries()).findIndex(([k]) => k === item.id);
        this.handleRemoveSelect(index, item.id)
      }
    },
    // 页码发生变化
    handlePageNoChange(pageNo) {
      this.formData.pageNo = pageNo;
      this.getPeopleList();
    },
    // 删除选中项
    handleRemoveSelect(index, id) {
      this.choseList.splice(index, 1);
      this.selectedMap.delete(id);
    },
    // 确认后，返回所有已选中数据。
    handleSubmit() {
      this.$emit("select", this.choseList);
      this.handleClose();
    },
    handleClose() {
      this.visible = false;
      this.$refs.form.resetFields();
      this.formData = {
        xm: "",
        total: 0,
        pageNo: 1,
        pageSize: 18,
        ryType: "",
        roomCode: ""
      }
      this.selectedMap.clear();
      this.choseList = this.inputChoseList;
    },
  }
};
</script>

<style scoped lang="less">
.modal {
  border-radius: 0.25em;
}

/deep/ .ivu-modal-body {
  padding: 0 !important;
}

/deep/ .ivu-modal-close .ivu-icon-ios-close {
  color: #fff;
}

/deep/ .people-chose-box .ivu-tag {
  display: block;
  line-height: 32px;
  font-size: 15px;
  height: 32px;
  display: flex;
  justify-content: space-between;
  background-color: #efefef;
  color: #333;

  &:not(:last-of-type) {
    margin-bottom: 5px;
  }

  .ivu-icon {
    color: #333 !important;
    font-size: 15px;
    line-height: 30px;
    opacity: 1;
  }
}

.peopleSelect-container {
  display: flex;
  height: 680px;

  .cate-select-box,
  .person-select-box,
  .people-chose-box {
    height: 100%;
    overflow-y: auto;
  }

  .cate-select-box {
    .cate-list {
      li {
        font-size: 15px;
        user-select: none;
        cursor: pointer;
      }

      .cate-list-level_1 {
        border-bottom: 1px solid #eaeaea;

        .title {
          padding: 0 10px;
          line-height: 3.2;
          font-weight: bold;
          border-left: 0 solid #3491fa;
          transition: border-left-width 0.18s ease-in;
          border-bottom: 1px solid #eaeaea;
        }

        &.active .title,
        &:hover .title {
          border-left-width: 4px;
        }

        ul {
          font-size: 14px;

          li {
            padding-left: 20px;
            line-height: 2.6;
            border-bottom: 1px solid #eaeaea;

            &.active,
            &:hover {
              color: #097cff;
            }
          }
        }
      }

    }
  }

  .person-select-box {
    display: flex;
    flex-direction: column;
    border-left: 1px solid #eaeaea;
    border-right: 1px solid #eaeaea;

    .search-box {
      display: flex;
      flex-direction: row;
      padding: 10px;
    }

    .people-box {
      padding: 10px;
      height: 582px;
      margin-bottom: 10px;
      zoom: 1;

      &::after {
        content: "";
        display: table;
        clear: both;
      }

      .person-box {
        display: flex;
        flex-direction: column;
        width: 14.8%;
        height: 184px;
        cursor: pointer;
        border: 2px solid #eaeaea;
        position: relative;
        float: left;
        margin-left: 15px;
        margin-bottom: 12px;

        &:nth-of-type(6n+1) {
          margin-left: 0;
        }

        &:nth-of-type(7+n) {
          margin-bottom: 0;
        }

        &:hover {
          border-color: #429aff;
        }

        &.selected {
          border-color: #097cff;

          &::before {
            content: "";
            position: absolute;
            border: 30px solid #097cffcc;
            border-left-color: transparent;
            border-top-color: transparent;
            border-right-color: transparent;
            width: 0;
            height: 0;
            right: -30px;
            top: -30px;
            z-index: 1;
            transform: rotate(45deg);
          }

          &::after {
            content: "✓";
            position: absolute;
            display: inline-block;
            right: 4px;
            top: 3px;
            color: #fff;
            line-height: 20px;
            text-align: center;
            z-index: 1;
            font-size: 18px;
          }

          .name {
            font-weight: bold;
          }
        }

        .img-box {
          position: relative;
          height: 150px;
        }

        img {
          display: block;
          width: 100%;
          height: 100%;
        }

        /**功能埋点-将来使用图标+背景色的方式 */
        .gender {
          position: absolute;
          right: 0;
          bottom: 0;
          display: inline-block;
          line-height: 20px;
          width: 20px;
          background-color: #3491fa;
          text-align: center;
        }

        .name {
          text-align: center;
          line-height: 2;
          font-size: 15px;
        }
      }
    }
  }

  .people-chose-box {
    padding: 10px;
    font-size: 15px;

    h3 {
      padding: 0 5px;
      border-left: 3px solid #3491fa;
      margin-bottom: 10px;
    }
  }
}
</style>
