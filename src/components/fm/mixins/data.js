import tem from 'template_js'
export default {
    props: {
      typeWs:{
        type:String,
        default: 'noWssp'
      },
      canSubmit:{
        type:Boolean,
        default:true
      },
      showSave:{
        type:Boolean,
        default:true
      },
      showCancel:{
        type:Boolean,
        default:false
      },
      formMountId: {
        type: String,
        default: ''
      },
      fmParams: {
        type: Object,
        default: function () {
          return null
        }
      },
      parameter: {
        type: Object,
        default: function () {
          return null
        }
      },
      flwsMc: {
        type:String,
        default: ''
      },
      wszl: {
        type:String,
        default: ''
      },
      // 主文书数据
      mainData: {
        type: Object,
        default: function () {
          return null
        }
      },
      cqyw: {
        type: Object,
        default: () => ( {
          spzt:''
        })
      },
      flws: {
        type: Object,
        default: () => ( {})
      },
      ajxx: {
        type: Object,
        default: function () {
          return {}
        }
      },
      xyrxx: {
        type: Object,
        default: function () {
          return {}
        }
      },
      peopleList: {
        type: Array,
        default: function () {
          return []
        }
      },
      customFunc: false,
      // 返回fomData
      formDataCallBack: {
        type: Function,
        default: null
      },
      // 纠错文书临时数据
      wsDraft: {
        type: Object,
        default: function () {
          return null
        }
      },
      content:{
        type:String,
        default: ''
      },
      // 主文书数据(呈请报告书用)
      mainFormData: {
        type: Array,
        default: () => []
      },
      EvidenceObj:{
        type: Object,
        default: () => ( {
          isLose:false,
        })
       
      },
    },
    data () {
      return {
        hiddenSave:false,
        businessId: '',
        isDisplayForm: true,
        saveLoading: false,
        dbId: '',
        subRes: [],
        saveBtnTxt: '保  存',
        code: {
            template: '',
            script: '',
            style: '',
            uuid: 'bsp-form-uuid',
            component: null,
            //模板数据
            templateData:{}
        },
        childVue: null,
        fmMountElId: '',
        mountEl: '',
        templateData: {},
        mountData:{},
        templateContent:'',
        formData:{},
        refreshDate: ''
      }
    },
    methods: {
      template (tpl, data) {
        tem.config({ sTag: '{{', eTag: '}}', escape: true })
        return tem(tpl, data)
      }
    }
}
