import Vue from 'vue'
import store from '@/store'
import tem from 'template_js'
import {
  mapActions
} from 'vuex'
import router from '@/router/index.js'

import {
  errorTip
} from '@/components/form/error-tip'
import {
  fQuery,
  sSelect
} from '@/components/form/query'
// import {
//   fVar
// } from '@/components/form/var'
import {
  fYear
} from '@/components/form/year'
// import {
//   fXyr,
//   sXyr
// } from '@/components/form/xyr'
import {
  userSelector
} from 'sd-user-selector'
import {
  orgSelector
} from 'sd-org-selector'
// import {
//   caseReason
// } from '@/components/case-reason'
// import {sDialog} from '@/components/custom-dialog'
import {
  buttonDialog
} from '@/components/form/dialog'
import {
  sDialog
} from 'sd-custom-dialog'
import data from './data'
import {
  formCommonMethods,
  formDefaultData,
  getSource,
} from '@/components/form/libs/form-common.js'

import {
  fCqbgsQuote,
  fArea
} from '@/components/form/cqbgs-quote'
import { sImageUpload } from "@/components/upload/image";
// import pymAddGoods from '@/components/sacw/pym-add-goods.vue'
// import XzcfModal from '@/components/xzcf/xzcf-modal.vue'
import {
  fileUpload
} from 'sd-minio-upfile' //附件上传

export default {
  mixins: [data],
  methods: {
    handleRun(templateCode, formData) {
      this.parseCode(templateCode)
      this.unMountTemplateCode()
      this.mountTemplateCode(formData)

    },
    //解析代码
    parseCode(content) {
      // console.log(content,'parseCode')
      this.code.template = getSource(content, 'template')
      this.code.style = getSource(content, 'style')
      this.code.script = getSource(content, 'script').replace(
        /export default/,
        'return '
      )
    },
    isObject(data) {
      return Object.prototype.toString.call(data) === '[object Object]';
    },
    isString(data) {
      return Object.prototype.toString.call(data) === '[object String]';
    },
    tempData(resp) {
      for (let i in resp.formData) {
        if (i && resp.formData[i]) {
          // 对象
          if (this.isObject(resp.formData[i])) {
            for (let j in resp.formData[i]) {
              if (j && resp.formData[i][j] && this.isString(resp.formData[i][j])) {
                if (resp.formData[i][j].indexOf('{{:=') > -1) {
                  this.$set(resp.formData[i], j, '')
                }
              }
            }
            // 字符串
          } else if (this.isString(resp.formData[i])) {
            if (resp.formData[i].indexOf('{{:=') > -1) {
              this.$set(resp.formData, i, '')
            }
          }
        }
      }
    },
    unMountTemplateCode() {
      // 销毁vue组件
      if (this.childVue) {
        this.mountEl.removeChild(this.childVue.$el)
        this.childVue.$destroy
        this.childVue = null
      }
      // 清楚style样式
      let userStyle = document.getElementById(this.code.uuid)
      if (userStyle) {
        document.head.removeChild(userStyle)
      }
    },
    template(tpl, data) {
      tem.config({
        sTag: '{{',
        eTag: '}}',
        escape: true
      })
      return tem(tpl, data)
    },
    drupZjyj() {
      if (serverConfig && serverConfig.zjckUrl) {
        let temValue = {
          ajbh: this.ajxx.ajbh,
          ajlx: this.ajxx.ajlx,
          token: this.$store.getters.sessionUser.token,
          ajmc: this.ajxx.ajmc,
          aybm: this.ajxx.ajab,
          ay: this.ajxx.ajabmc

        }
        let url = this.template(serverConfig.zjckUrl, temValue)
        // console.log(this.EvidenceObj,'this.EvidenceObj','/icp/xzaj/zjcl')
        // let url = '/#/icp/xzaj/zjcl?ywbh=' + this.ajxx.ajbh + '&ztbs=ajzt&type=inspect&ajlx=' + this.ajxx.ajlx + '&hj=' + this.EvidenceObj.hj + '&rybh=' + this.EvidenceObj.rybh
        window.open(url) // 打开新窗口

      }
    },
    mountTemplateCode(formData) {
      // 英文字符""转成中文
      for(let i in this.ajxx){
        if(i && this.ajxx[i] && this.isString(this.ajxx[i])){
          let nr =this.ajxx[i].replace(/"([^"]*)"/g ,"“$1”");
          this.$set(this.ajxx,i,nr)
        }
      }
      // 生成vue组件挂载
      const vueExtendObj = new Function(this.code.script)()
      let defaultData = Object.assign({}, formDefaultData)
      Object.assign(defaultData, vueExtendObj.data())
      if (formData) {
        defaultData = Object.assign(defaultData, formData)
      }
      if (this.parameter.operType === '0') {
        try {
          let formDataStr = this.template(JSON.stringify(defaultData.formData), {
            aj: this.ajxx,
            ry: this.xyrxx,
            main: this.mainFormData
          })
          formDataStr = this.formatContent(formDataStr)
          defaultData.formData = JSON.parse(formDataStr)
        } catch (e) {
          try {
            defaultData.formData = JSON.parse(this.template(JSON.stringify(defaultData.formData), {
              aj: this.ajxx,
              ry: this.xyrxx
            }))
          } catch (e) {}
        }
        // if (this.wszl === '') {
        //   this.warnSwal('《' + this.flwsMc + '》未设置文书种类')
        //   // 设置回调处理异常
        //   return
        // } else {
        //   defaultData.formData.wsbh = this.wszl
        // }

      }
      defaultData.peopleList = this.peopleList
      defaultData.parameter = this.parameter

      if (this.mainFormData) {
        defaultData.mainFormData = this.mainFormData
        vueExtendObj.parent = this
      }

      if (this.mainData && this.mainData.param.operType === '1') {
        defaultData.formData.mainKey = this.mainData.param.businessId
        defaultData.parameter.mainKey = this.mainData.param.businessId
      }
      console.log(this.fmParams,'this.fmParamsthis.fmParams',vueExtendObj)
      // this.$set(this.ajxx,'ajbh',this.ajxx.ajbh?this.ajxx.ajbh:this.ajxx.jqbh)
      // this.$set(this.ajxx,'ajmc',this.ajxx.ajmc?this.ajxx.ajmc:this.ajxx.jqbh)
      defaultData.ajxx = this.ajxx
      defaultData.xyrxx = this.xyrxx
      this.tempData(defaultData)
      defaultData.hisFormData = JSON.parse(JSON.stringify(defaultData.formData))
      vueExtendObj.data = function () {
        return defaultData
      }
      // 克隆vue的methods方法
      Object.assign(vueExtendObj.methods, {
        ...mapActions(['postRequest','getRequest'])
      })
      Object.assign(vueExtendObj.methods, formCommonMethods)
      // 这种模版
      vueExtendObj.template = this.splicingTemplate(this.code.template)
      // 设置vuex
      vueExtendObj.store = store
      vueExtendObj.router = router
      // 设置组件
      vueExtendObj.components = {
        errorTip,
        fQuery,
        sSelect,
        userSelector,
        sDialog,
        buttonDialog,
        // fVar,
        fYear,
        // fXyr,
        // sXyr,
        fCqbgsQuote,
        fArea,
        sImageUpload,
        orgSelector,
        // caseReason,
        // pymAddGoods,
        // XzcfModal,
        fileUpload,
      };
      let childVueComponent = Vue.extend(vueExtendObj)
      this.childVue = new childVueComponent().$mount()
      console.log(this.childVue,'this.childVue')
      this.mountEl.appendChild(this.childVue.$el)
      if (this.code.css !== '') {
        let styleEl = document.createElement('style')
        styleEl.type = 'text/css'
        styleEl.id = this.code.uuid
        styleEl.innerHTML = this.code.style
        document.head.appendChild(styleEl)
      }
      this.getSubDatas(this.dbId, this.businessId, this.subRes)
      /*

      parseStrToObj.template = this.userCode.template
      parseStrToObj.store = store
      parseStrToObj.components = { errorTip }
      console.log(parseStrToObj)
      let userCodeComponent = Vue.extend(parseStrToObj)
      this.userCode.component = new userCodeComponent().$mount()
      this.mountEl.appendChild(this.userCode.component.$el)
      // 挂载样式
      if (this.userCode.css !== '') {
        let styleEl = document.createElement('style')
        styleEl.type = 'text/css'
        styleEl.id = this.userCode.uuid
        styleEl.innerHTML = this.userCode.style
        document.head.appendChild(styleEl)
      }*/
      // this.setDisabled()

    },
    splicingTemplate(template) {
      let errorTipComponent = '<error-tip ref="errorTip" @focus="focus" :tips="errorTips"></error-tip>'
      return `<div>${template}${errorTipComponent}</div>`
    },
    getSubDatas(dbId, businessId, subRes) {
      let hasDraft = !!this.wsDraft
      subRes.forEach(element => {
        this.getSubData(dbId, element['tableName'], element['fkField'], businessId, hasDraft)
      })
    },
    getSubData(dbId, tableName, fkField, businessId, hasDraft) {
      let params = {
        dbId: dbId,
        tableName: tableName,
        fkField: fkField,
        businessId: businessId
      }
      this.$store.dispatch('postRequest', {
        url: '/bsp-com/com/form/handle/getSubData',
        params: params
      }).then(resp => {
        if (resp.success) {
          if (!hasDraft) { // 没有文书草稿才渲染查询出来的子表值，否则还是渲染草稿里面的子表数据
            this.childVue.formData[tableName].data = resp.data
          }
          this.formData[tableName].data = JSON.parse(JSON.stringify(resp.data))
          // 设置子表数据
          let hisFormData = this.getHisFormData();
          hisFormData[tableName].data = JSON.parse(JSON.stringify(resp.data))
          this.setHisFormData(hisFormData)
          //this.execFormDataCallBack()
          // resp.data.forEach(item => {
          // let d = this.childVue.buildSubData(tableName)
          // Object.assign(d, item)
          // this.childVue.formData[tableName].data.push(item)
          // })
          // this.childVue.formData[tableName].data = resp.data
        } else {
          this.warnSwal('子表数据查询失败。')
        }
      })
    },
    handleSubmit() {
      // this.show()
      console.log(this.childVue,'this.childVue')
      this.childVue.$refs['formData'].validate((valid) => {
        if (valid) {
          let formData = Object.assign({}, this.childVue.formData)
          if (!formData.wszi) {
            formData.wszi = this.childVue.wszi
          }
          this.saveFormData(formData)
          //this.$Message.success('Success!');
        }
      })
    },
    getFormData() {
      return new Promise((resolve, reject) => {
        this.childVue.$refs['formData'].validate((valid) => {
          if (valid) {
            let formData = Object.assign({}, this.childVue.formData)
            resolve(formData)
          } else {
            reject('输入不完整或格式有误')
          }
        })
      })
    },
    getHisFormData() {
      // console.log(this.childVue,'this.childVue')
      return this.childVue ? this.childVue.hisFormData : ''
    },
    getMataDicField() {
      return this.childVue.dicMetaFiled
    },
    setHisFormData(formData) {
      this.childVue.hisFormData = JSON.parse(JSON.stringify(formData))
    },
    getFormWithOutValidData() {
      return new Promise((resolve, reject) => {
        let formData = Object.assign({}, this.childVue.formData)
        if (!formData.wszi) {
          formData.wszi = this.childVue.wszi
        }
        resolve(formData)
      })
    },
    getTemplateId() {
      return this.templateData.id
    },
    saveFormData(data) {
      this.saveLoading = true
      this.showSpin()
      let params = Object.assign({}, this.$route.query)
      params.businessId = data.businessId
      params.formId=this.parameter.formId
      // params.operType = 1//this.parameter.operType
      params.operType = this.parameter.operType

      params['formData'] = JSON.stringify(data)
      console.log(params,'paramssaveFormData')
      this.$store.dispatch('postRequest', {
        url: this.$path.com_from_save_form_data,//icp_flws_save_cqyw_data,//this.$path.com_model_fm_save,
        params: params
      }).then(resp => {
        if (resp.success) {
          // this.successSwal('数据保存成功！')
          this.$Notice.success({
            title: "提示",
            desc: "保存成功",
        });
          this.saveLoading = false
          let query = Object.assign({}, this.$route.query)
          query.operType = '1'
          query.businessId = data.businessId
          // this.$router.replace({
          //   path: '/handler/openForm',
          //   query: query
          // })
          this.$emit('saveForm',params,data)
        } else {
          this.warnSwal('文数据保存出错。原因:' + resp.msg)
          this.warnSwal(resp.msg)
        }
        this.saveLoading = false
        this.$Spin.hide()
      })
    },
    cancal(){
      this.$emit('cancal',this.childVue.formData)
    },
    successSwal(msg, callback) {
      this.$Modal.success({
        content: msg
      })
    },
    warnSwal(msg, callback) {
      this.$Modal.warning({
        content: msg
      })
    },
    showSpin() {
      this.$Spin.show({
        render: (h) => {
          return h('div', [
            h('Icon', {
              'class': 'fm-spin-icon-load',
              props: {
                type: 'ios-loading',
                size: 100
              }
            }),
            h('div', {
              'class': 'fm-spin-load-text'
            }, '数据保存中...')
          ])
        }
      })
    },
    formatContent(str) {
      // str=str.replace(/\\/g,"")
      str = str.replace(/\n/g, "")
      str = str.replace(/\r/g, "")
      str = str.replace(/\t/g, "")
      // str=str.replace(/\'/g,"")
      // str=str.replace(/ /g,"")
      // str=str.replace(/</g,"")
      // str=str.replace(/>/g,"")
      return str
    },
    getMainFormData() {
      let result = []
      this.$emit("getAllMainWsFormData", data => {
        result = data
      })
      return result
    },
    // 附件上传处理
    fileUploadDeal(fileList) {
      // console.log(fileList,this.childVue.formData,'fileUploadDeal')
      this.childVue.formData.prop.scfj = fileList ? JSON.stringify(fileList) : ''
    },
    // setDisabled(editFields) {
    //   // console.log(this.canSubmit,this.mainData,'canSubmit')
    //   if (editFields && editFields.length > 0) {
    //     let form = document.getElementsByClassName("fm-content")
    //     let editArr = []

    //     Array.from(form).forEach(formItem => {
    //       let FormItemArr = Array.from(formItem.getElementsByClassName('ivu-form-item'))
    //       editArr = FormItemArr.filter((item) => {
    //         return !editFields.some(ele => ele == item.__vue__._props.prop)
    //       });
    //       this.forData(editArr)
    //     })
    //   } else {
    //     // 全部不可编辑
    //     let form = document.getElementsByClassName("fm-content")
    //     Array.from(form).forEach(formItem => {
    //       let FormItem = Array.from(formItem.getElementsByClassName('ivu-form-item'))
    //       this.forData(FormItem)


    //     })
    //   }
    // },
    // forData(arr){
    //   arr.forEach(item => {
    //     let input = null
    //     let date = null
    //     let select = null
    //     let elInput = null
    //     let searchBtn = null
    //     let vSelect = null
    //     let raido = null
    //     if (item.getElementsByClassName('ivu-input')[0]) {
    //       item.getElementsByClassName('ivu-input')[0].setAttribute('disabled', true)
    //       item.getElementsByClassName('ivu-input')[0].classList.add('status-disabled')
    //     }

    //     if (item.getElementsByTagName('input')[0]) {
    //       input = item.getElementsByTagName('input')[0]
    //     }
    //     if (item.getElementsByClassName('ivu-radio-group')[0]) {
    //       raido = item.getElementsByClassName('ivu-radio-group')[0]
    //     }
    //     if (item.getElementsByClassName('el-input__inner')[0]) {
    //       elInput = item.getElementsByClassName('el-input__inner')[0]
    //     }
    //     if (item.getElementsByClassName('ivu-input-search')[0]) {
    //       searchBtn = item.getElementsByClassName('ivu-input-search')[0]
    //     }
    //     if (item.getElementsByClassName('el-date-editor')[0]) {
    //       date = item.getElementsByClassName('el-date-editor')[0]
    //     }
    //     if (item.getElementsByClassName('sp-input-container')[0]) {
    //       select = item.getElementsByClassName('sp-input-container')[0]
    //     }
    //     // ivu-select-disabled
    //     let selectArr = item.getElementsByClassName('ivu-select')
    //     // if(item.getElementsByClassName('ivu-select')[0]){
    //     //     select=item.getElementsByClassName('ivu-select')[0]
    //     //     select=item.getElementsByClassName('ivu-select-selection')[0]
    //     // }
    //     if (item.getElementsByClassName('v-dropdown-caller')[0]) {
    //       vSelect = item.getElementsByClassName('v-dropdown-caller')[0]
    //     }

    //     if (item.getElementsByTagName('textarea')[0]) {
    //       input = item.getElementsByTagName('textarea')[0]
    //     }
    //     if (item) {
    //       item.setAttribute('disabled', true)

    //     }
    //     if (elInput) {
    //       elInput.setAttribute('disabled', true)
    //       elInput.classList.add('status-disabled')
    //     }
    //     if (input) {
    //       input.setAttribute('disabled', true)
    //     }
    //     if (date) {
    //       date.setAttribute('disabled', true)
    //       date.classList.add('status-disabled')
    //     }
    //     if (select) {
    //       select.classList.add('sp-disabled')
    //       select.setAttribute('disabled', true)
    //       select.classList.add('status-disabled')
    //     }
    //     if (vSelect) {
    //       vSelect.classList.add('status-disabled')
    //     }
    //     if (searchBtn) {
    //       searchBtn.classList.add('status-disabled')
    //     }
    //     if (raido) {
    //       raido.setAttribute('disabled', true)
    //       raido.classList.add('status-disabled')
    //     }

    //     let inputArr = item.getElementsByTagName('input')
    //     Array.from(inputArr).forEach(ele => {
    //       ele.setAttribute('disabled', true)
    //       // ele.classList.add('status-disabled')
    //     })
    //     let radioArr = item.getElementsByClassName('ivu-radio')
    //     Array.from(radioArr).forEach(ele => {
    //       ele.classList.add('ivu-radio-disabled')
    //     })
    //     let checkbox = item.getElementsByClassName('ivu-checkbox')

    //     let orgInput = item.getElementsByClassName('ivu-input')
    //     let btn = item.getElementsByClassName('ivu-btn')
    //     setInterval(() => {
    //       Array.from(orgInput).forEach(ele => {
    //         ele.setAttribute('disabled', true)
    //         ele.classList.add('status-disabled')
    //       })
    //       Array.from(btn).forEach(ele => {
    //         ele.setAttribute('disabled', true)
    //         ele.classList.add('status-disabled')
    //       })
    //       Array.from(checkbox).forEach(ele => {
    //         ele.classList.add('ivu-checkbox-disabled')
    //       })
    //       Array.from(selectArr).forEach(ele => {
    //         ele.classList.add('ivu-select-disabled')
    //         ele.classList.add('sp-disabled')
    //         ele.setAttribute('disabled', true)
    //         ele.classList.add('status-disabled')
    //       })
    //     }, 300)

    //   })

    // }
  },
  watch: {
    content(val) {
      if (val) {
        this.mountData.formData = JSON.parse(val)
        this.handleRun(this.templateContent, this.mountData);
        this.$nextTick(() => {
          this.$emit("resetWscg");
        });
      }
    },
  },
}
