<template>
    <div>
        <rs-DataGrid ref="grid" :funcMark="funcMark" v-if="funcMark" :params="params" :customFunc="false">
            <!-- <template v-slot:access:type:add="{ oper }">
                <Button type="primary" class="header-button" @click="handleAdd">
                    <Icon :type="oper.iconPath ? oper.iconPath : 'md-add'" size="20" />
                    {{ oper.name }}
                </Button>
            </template>
<template v-slot:access:type:edit="{ oper, row, index }">
                <Button type="primary" class="row-button" @click="handleEdit(row)">
                    <Icon v-if="oper.iconPath" :type="oper.iconPath" />
                    {{ oper.name }}
                </Button>&nbsp;&nbsp;
            </template>
<template v-slot:access:type:del="{ oper, row, index }">
                <Button type="error" size="small" class="row-button" @click="handleDelete(row.id)">
                    <Icon v-if="oper.iconPath" :type="oper.iconPath" />
                    {{ oper.name }}
                </Button>&nbsp;&nbsp;
            </template>
<template v-slot:access:type:permi="{ oper, row, index }">
                <Button type="primary" class="row-button" @click="handlePermissions(row)">
                    <Icon v-if="oper.iconPath" :type="oper.iconPath" />
                    {{ oper.name }}
                </Button>
            </template> -->
        </rs-DataGrid>
    </div>
</template>
<script>
import { formatDateparseTime } from "@/libs/util";
import dayjs from 'dayjs';
export default {
    data() {
        return {
            funcMark: this.$route.query && this.$route.query.modelId ? this.$route.query.modelId : '',
            query: this.$route.query && this.$route.query ? this.$route.query : {},
            params: {}
        }
    },
    created() {
        this.serachTime(this.query.type)
    },
    methods: {
        getCurrentWeekRange() {
            const today = dayjs();
            const dayOfWeek = today.day(); // 0 (周日) 到 6 (周六)
            // 计算本周一
            const startOfWeek = today.subtract(dayOfWeek - 1, 'day').startOf('day');
            // 计算本周日
            const endOfWeek = today.add(6 - dayOfWeek, 'day').endOf('day');

            return {
                start: startOfWeek,
                end: endOfWeek
            };
        },
        serachTime(title) {
            switch (title) {
                case '今日':
                    this.$set(this.params, 'startTime', formatDateparseTime(new Date(), '{y}-{m}-{d}') + ' 00:00:00')
                    this.$set(this.params, 'endTime', formatDateparseTime(new Date(), '{y}-{m}-{d}') + ' 23:59:59')
                    break;
                case '昨日':
                    this.$set(this.params, 'startTime', dayjs().subtract(1, 'day').format('YYYY-MM-DD') + ' 00:00:00')
                    this.$set(this.params, 'endTime', dayjs().subtract(1, 'day').format('YYYY-MM-DD') + ' 23:59:59')
                    break;
                case '本周':
                    this.$set(this.params, 'startTime', this.getCurrentWeekRange().start.format('YYYY-MM-DD') + ' 00:00:00')
                    this.$set(this.params, 'endTime', this.getCurrentWeekRange().end.format('YYYY-MM-DD') + ' 23:59:59')
                    break;
                case '本月':
                    this.$set(this.params, 'startTime', dayjs().startOf('month').format('YYYY-MM-DD') + ' 00:00:00')
                    this.$set(this.params, 'endTime', dayjs().endOf('month').format('YYYY-MM-DD') + ' 23:59:59')
                    break;
                case '今年':
                    this.$set(this.params, 'startTime', dayjs().startOf('year').format('YYYY-MM-DD') + ' 00:00:00')
                    this.$set(this.params, 'endTime', dayjs().endOf('year').format('YYYY-MM-DD') + ' 23:59:59')
                    break;
                case '本年':
                    this.$set(this.params, 'startTime', dayjs().startOf('year').format('YYYY-MM-DD') + ' 00:00:00')
                    this.$set(this.params, 'endTime', dayjs().endOf('year').format('YYYY-MM-DD') + ' 23:59:59')
                    break;

            }
        }
    }
}
</script>