<template>
  <div>
    <fm :showSave="false" :ref="fmParams.tagId" :quickRegistration="quickRegistration" :parameter="fmParams.param"
      v-if="fmParams.load" :formMountId="fmParams.tagId" />
  </div>
</template>

<script>
import { sImageUploadLocal } from '@/components/upload/image'
import { fileUpload } from 'sd-minio-upfile'
import fm from "@/components/fm/component/fm.vue";
export default {
  components: {
    sImageUploadLocal, fileUpload, fm
  },
  props: {
    formData: {
      type: [Array, Object],
      default: () => ({})
    },
    quickRegistration: {
      type: Boolean,
      default: false,
    },
    rowData: {
      type: [Array, Object],
      default: () => ({})
    },
  },
  watch: {
    'rowData': {
      handler(n, o) {
        this.$set(this.fmParams.param, 'businessId', this.rowData && this.rowData.id ? this.rowData.id : '')
      }, deep: true, immediate: true
    },
  },
  data() {
    return {
      showFile: false,
      sjpzwsdzUrl: [],
      serviceMark: serverConfig.OSS_SERVICE_MARK,
      bucketName: serverConfig.bucketName,
      ruleValidate: {},
      fmParams: {
        tagId: 'case',
        load: true,
        param: {
          mark: serverConfig.APP_CODE + ':sjdj-ajxx',//'1953283412538298368',//'1899019819911614464',
          operType: this.rowData && this.rowData.id ? '1' : '0',  //0为新增 1为修改
          businessId: this.rowData && this.rowData.id ? this.rowData.id : '',
          quickRegistration:this.quickRegistration
        }
      }
    }
  },
  methods: {
    beforeUpload() { },
    fileSuccessFile() { },
    fileRemoveFile() { },
    fileCompleteFile(data, index) {
      if (data && data.length > 0) {
        console.log(data, 'sjpzwsdz')
        this.$set(this.formData, 'sjpzwsdz', JSON.stringify(data))
      }
    },
    validate() {
      return new Promise((resolve) => {
        this.$refs.formData.validate(valid => {
          resolve(valid); // valid 是 boolean，表示是否验证通过
        });
      });
    },
    updataSjpzwsdzUrl(sjpzwsdz) {
      if (sjpzwsdz) {
        this.formData.sjpzwsdz = sjpzwsdz
        if (this.formData.sjpzwsdz) {
          this.sjpzwsdzUrl = JSON.parse(this.formData.sjpzwsdz)
        }
      }
      this.showFile = true
    },
    getDaysDiff(dateStr1, dateStr2) {
      // 将字符串转为 Date 对象
      const date1 = new Date(dateStr1);
      const date2 = new Date(dateStr2);
      // 检查是否是有效日期
      if (isNaN(date1) || isNaN(date2)) {
        console.log("日期格式错误，请确保传入的格式为 YYYY-MM-DD");
      }
      // 计算两个时间戳之间的毫秒差
      const diffTime = Math.abs(date2 - date1);
      // 转换为天数（1 天 = 1000 * 60 * 60 * 24 毫秒）
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      return diffDays;
    },
    sjqzrqChange(data) {
      console.log("日期改变")
      this.formData.sjqsrq = data[0]
      this.formData.sjjzrq = data[1]
      this.formData.sjqzrq = data
      let sjqx = this.getDaysDiff(this.formData.sjqsrq, this.formData.sjjzrq)
      this.formData.sjqx = sjqx.toString()
      console.log(this.formData.sjqx, "日期改变")
    },
  },
  mounted() {
    this.formData.badwlx = '1';
    this.formData.sjqx = '两年'
  }
}
</script>

<style scoped>
.bsp-imgminio-container {
  width: 100% !important;
}
</style>
