<template>
  <div>

    <fm :showSave="false" :ref="fmParams.tagId" :parameter="fmParams.param"
      v-if="fmParams.load" :formMountId="fmParams.tagId" />
  </div>
</template>

<script>
import fm from "@/components/fm/component/fm.vue";
export default {
  components: { fm },
  props: {
    formData: {
      type: [Array, Object],
      default: () => ({})
    },
    rowData: {
      type: [Array, Object],
      default: () => ({})
    },
    

  },

  data() {
    return {
      ruleValidate: {},
      fmParams: {
        tagId: 'otherInfo',
        load: true,
        param: {
          mark: serverConfig.APP_CODE + ':sjdj-qtxx',//'1953283412538298368',//'1899019819911614464',
          operType: this.rowData && this.rowData.id ? '1' : '0',  //0为新增 1为修改
          businessId: this.rowData && this.rowData.id ? this.rowData.id : ''
        }
      },
    }
  },
  watch: {
    'rowData': {
      handler(n, o) {
        this.$set(this.fmParams.param, 'businessId', this.rowData && this.rowData.id ? this.rowData.id : '')
      }, deep: true, immediate: true
    },
  },
  methods: {
    validate() {
      return new Promise((resolve) => {
        this.$refs.formData.validate(valid => {
          resolve(valid); // valid 是 boolean，表示是否验证通过
        });
      });
    }
  }
}
</script>

<style scoped></style>
