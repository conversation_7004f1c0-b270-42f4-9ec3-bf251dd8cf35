<template>
  <div class="" style="height: 95%;overflow: hidden;">
    <div class="bsp-base-content" style="top:0px !important;">
      <Form ref="formData" :model="formData" :rules="ruleValidate" :label-width="150" label-colon>
        <div class="steps-box">
          <Steps :current="current">
            <Step v-for="(step, index) in steps" :key="index">
              <template #title>
                <div @click="stepClick(index)" style="cursor: pointer">{{ step.title }}</div>
              </template>
            </Step>
          </Steps>
        </div>
        <div class="bsp-base-subtit" v-show="current === 0" style="height: 100%;overflow-x: hidden;overflow-y: auto;">
          <baseInfo :formData="formData" :rowData="rowData" ref="baseInfoForm" :quickRegistration="quickRegistration"
            @getRecordByzjhm="getRecordByzjhm"></baseInfo>
        </div>
        <div class="bsp-base-subtit" v-show="current === 1">
          <caseInfo :formData="formData" :rowData="rowData" :quickRegistration="quickRegistration" ref="caseInfoForm">
          </caseInfo>
        </div>

        <div class="bsp-base-subtit" v-show="current === 2">
          <registrationInfo :formData="formData" :rowData="rowData" :quickRegistration="quickRegistration"
            ref="registrationInfoForm"></registrationInfo>
        </div>
        <div class="bsp-base-subtit" v-show="current === 3">
          <socialRelationshipInfo :formData="formData" :rowData="rowData" ref="socialRelationshipInfoForm">
          </socialRelationshipInfo>
        </div>
        <div class="bsp-base-subtit" v-show="current === 4">
          <otherInfo :formData="formData" :rowData="rowData" ref="otherInfoForm"></otherInfo>
        </div>

      </Form>
    </div>
    <div class="bsp-base-fotter">
      <Button style="margin: 0 20px" @click="handleClose()">返 回 </Button>
      <Button style="margin: 0 20px" :loading="loadingSave" @click="handleSubmit(false)">暂 存</Button>
      <Button style="margin: 0 20px" v-if="current !== 0" type="primary" @click="prev()">上一步</Button>
      <Button style="margin: 0 20px" v-if="current !== 4" type="primary" @click="next()">下一步</Button>
      <Button style="margin: 0 20px" v-if="current === 4 || quickRegistration" type="primary" :loading="loading"
        @click="handleSubmit(true)">提交</Button>
    </div>


    <start-approval ref="approval" :assigneeUserId="approvalData.assigneeUserId"
      :assigneeUserName="approvalData.assigneeUserName" :assigneeOrgId="approvalData.assigneeOrgId"
      :assigneeOrgName="approvalData.assigneeOrgName" :assigneeAreaId="approvalData.assigneeAreaId"
      :assigneeAreaName="approvalData.assigneeAreaName" :definition="approvalData.definition" :bindEvent="false"
      :showcc="false" :error="startError" :businessId="approvalData.businessId" :variables="approvalData.variables"
      :startUpSuccess="startUpSuccess" :beforeOpen="beforeOpen" :msgUrl="msgUrl" :msgTit="msgTit"
      :module="module"></start-approval>
  </div>
</template>

<script>
import { startApproval } from 'gs-start-approval'
import { sImageUploadLocal } from '@/components/upload/image'
import { fileUpload } from 'sd-minio-upfile'
import { getUserCache, getCurrentTimeFormatted,removeEmptyFields } from '@/libs/util'
import caseInfo from './caseInfo'
import baseInfo from './baseInfo'
import registrationInfo from './registrationInfo'
import socialRelationshipInfo from './socialRelationshipInfo'
import otherInfo from "./otherInfo";
import { mapActions } from "vuex";
export default {
  components: {
    sImageUploadLocal, fileUpload, caseInfo, baseInfo, registrationInfo, socialRelationshipInfo, otherInfo, startApproval
  },
  props: {
    rowData: {
      type: [Array, Object],
      default: () => ({})
    },
    saveType: {
      default: 'add',
      type: String
    },
    quickRegistration: {
      type: Boolean,
      default: false,
    },
    entireProcess: {
      type: Boolean,
      default: false,
    }
  },
  data() {
    return {
      steps: [
        { title: '基本信息' },
        { title: '案件信息' },
        { title: '收戒信息' },
        { title: '社会关系' },
        { title: '其他信息' },
      ],
      current: 0,
      formData: {
        sfsm: '0'
      },
      loading: false,
      loadingSave: false,
      ruleValidate: {},
      msgUrl: '/#/detentionBusiness/rehabilitationEnterRegister',
      msgTit: '【审批】收戒入所',
      businessId: this.rowData.rybh,
      module: serverConfig.APP_MARK,
      approvalData: {
        definition: [
          {
            name: '快速入所审批',
            defKey: 'syywksrssp'
          }
        ],
        assigneeOrgId: this.$store.state.common.orgCode,
        assigneeOrgName: this.$store.state.common.orgName,
        assigneeUserId: this.$store.state.common.idCard,
        assigneeUserName: this.$store.state.common.userName,
        businessId: this.rowData.rybh,
        fApp: serverConfig.APP_MARK,
        fXxpt: 'pc',
        variables: {
          eventCode: this.rowData.rybh,
          busType: '105_005'
        }
      },
    }
  },
  methods: {
    ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
    handleClose() {
      this.$emit('close', false)
    },
    handleNext(data) {
      this.$emit('nextStep', data)
    },
    stepClick(index) {
      this.current = index
    },
    next() {
      if (this.current < 4) {
        this.current += 1;
      }
    },
    prev() {
      if (this.current > 0) {
        this.current -= 1;
      }
    },
    getDaysDiff(dateStr1, dateStr2) {
      // 将字符串转为 Date 对象
      const date1 = new Date(dateStr1);
      const date2 = new Date(dateStr2);
      // 检查是否是有效日期
      if (isNaN(date1) || isNaN(date2)) {
        console.log("日期格式错误，请确保传入的格式为 YYYY-MM-DD");
      }
      // 计算两个时间戳之间的毫秒差
      const diffTime = Math.abs(date2 - date1);
      // 转换为天数（1 天 = 1000 * 60 * 60 * 24 毫秒）
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      return diffDays;
    },
    getSocialRelationsList(rybh) {
      this.$store.dispatch('authPostRequest', {
        url: this.$path.app_getSocialRelationsList,
        params: {
          rybh: rybh
        }
      }).then(resp => {
        if (resp.code == 0) {
          this.formData.socialRelations = resp.data
          if (this.formData.socialRelations) {
            this.$refs.socialRelationshipInfoForm.updateSocialRelations(this.formData.socialRelations)
          }

        } else {
          this.$Modal.error({
            title: '温馨提示',
            content: resp.msg || '操作失败'
          })
        }
      })
    },
    getDetail(id) {
      this.$store.dispatch('authGetRequest', {
        url: this.$path.app_inRecordJdsGet,
        params: {
          id: id
        }
      }).then(resp => {
        if (resp.code == 0) {
          this.formData = resp.data
          this.formData.id = resp.data.id
          this.formData.rybh = resp.data.rybh
          if (this.formData.xzhjaj != undefined && this.formData.xzhjaj != null) {
            this.formData.xzhjaj = this.formData.xzhjaj.toString()
          }
          if (this.formData.sfsm != undefined && this.formData.sfsm != null) {
            this.formData.sfsm = this.formData.sfsm.toString()
          }
          if (this.formData.xzhj) {
            this.formData.xzhj = this.formData.xzhj.toString()
          }
          if (this.formData.jlqsrq && this.formData.jljzrq) {
            this.formData.jlqzrq = []
            this.formData.jlqzrq.push(this.formData.jlqsrq)
            this.formData.jlqzrq.push(this.formData.jljzrq)
            let jlqx = this.getDaysDiff(this.formData.jlqsrq, this.formData.jljzrq)
            this.formData.jlqx = jlqx.toString()
          }
          if (!this.formData.sfsm) {
            this.formData.sfsm = "0"
          }
          this.$refs.caseInfoForm.updataSjpzwsdzUrl(this.formData.sjpzwsdz)
          if (this.formData.socialRelations) {
            this.$refs.socialRelationshipInfoForm.updateSocialRelations(this.formData.socialRelations)
          } this.formData.jbsj = getCurrentTimeFormatted();
          this.$set(this.formData, 'jbr', getUserCache.getUserName())
          this.getSocialRelationsList(this.rowData.rybh)
        } else {
          this.$Modal.error({
            title: '温馨提示',
            content: resp.msg || '操作失败'
          })
        }
      })
    },
    getRecordByzjhm() {
      if (!this.formData.zjhm || !this.formData.zjhm.trim()) {
        this.$Message.error('请填写证件号码!!');
        return;
      }
      this.$store.dispatch('authGetRequest', {
        url: this.$path.app_getRecordByzjhm,
        params: {
          zjhm: this.formData.zjhm
        }
      }).then(resp => {
        if (resp.code == 0) {
          this.formData = resp.data
          if (this.formData.xzhjaj) {
            this.formData.xzhjaj = this.formData.xzhjaj.toString()
          }
          if (this.formData.xzhj) {
            this.formData.xzhj = this.formData.xzhj.toString()
          }
          if (!this.formData.sfsm) {
            this.formData.sfsm = "0"
          }
          this.$refs.caseInfoForm.updataSjpzwsdzUrl(this.formData.sjpzwsdz)


          this.formData.jbsj = getCurrentTimeFormatted();
          this.$set(this.formData, 'jbr', getUserCache.getUserName())
        } else {
          this.$Modal.error({
            title: '温馨提示',
            content: resp.msg || '操作失败'
          })
        }
      })
    },
    async handleSubmit(tag) {
      
      let baseInfoForm = this.$refs.baseInfoForm.$refs.fm.childVue
      let caseInfoForm = this.$refs.caseInfoForm.$refs.case.childVue
      let registrationInfoForm = this.$refs.registrationInfoForm.$refs.registrationInfo.childVue
      let otherInfoForm = this.$refs.otherInfoForm.$refs.otherInfo.childVue
      console.log(baseInfoForm.formData, 'baseInfoForm');
      if (baseInfoForm) {
        if (tag || !tag && this.current === 0) {
          const valid = await baseInfoForm.$refs.formData.validate();
          if (!valid) {
            this.$Message.error('基本信息请填写完整!!');
            return;
          }else {
             Object.assign(this.formData,removeEmptyFields(baseInfoForm.formData))
          }
        }
      }
      if (caseInfoForm) {
        if (tag || !tag && this.current === 1) {
          const valid = await caseInfoForm.$refs.formData.validate();
          if (!valid) {
            this.$Message.error('案件信息请填写完整!!');
            return;
          }else {
             Object.assign(this.formData,removeEmptyFields(caseInfoForm.formData))
          }
        }
      }
      if (registrationInfoForm) {
        if (tag || !tag && this.current === 2) {
          const valid = await registrationInfoForm.$refs.formData.validate();
          if (!valid) {
            this.$Message.error('收戒信息请填写完整!!');
            return;
          }else {
             Object.assign(this.formData,removeEmptyFields(registrationInfoForm.formData))
          }
        }
      }
      if (otherInfoForm) {
        if (tag || !tag && this.current === 4) {
          const valid = await otherInfoForm.$refs.formData.validate();
          if (!valid) {
            this.$Message.error('其他信息请填写完整!!');
            return;
          }else {
             Object.assign(this.formData,removeEmptyFields(otherInfoForm.formData))
          }
        }
      }
      if (tag) {
        this.saveData(tag)
      } else {
        this.saveData()
      }
    },
    saveData(tag) {
      this.formData.dataSources ? '' : this.$set(this.formData, 'dataSources', 0)
      let params = this.formData
      console.log(params,'ssssssssssssssssscccccccccccc');
      
      let url = this.$path.app_inRecordJdsCreate
      if (this.formData.status && this.formData.status != "01") {
        url = this.$path.app_inRecordJdsUpdate
      }
      if (tag) {
        this.loading = true
        params.status = "03"
      } else {
        this.loadingSave = true
        params.status = "02"
      }
      if (this.saveType == "add") {
        params.rslx = "01"
        if (this.quickRegistration) {
          console.log("快速入所")
          params.rslx = "02"
        }
      } else if (this.rowData.current_step == "01" && this.rowData.status == "01") {
        params.rslx = "01"
        if (this.quickRegistration) {
          console.log("快速入所")
          params.rslx = "02"
        }
      }
      // return
      this.$store.dispatch('authPostRequest', {
        url: url,
        params: params
      }).then(resp => {
        this.loading = false
        this.loadingSave = false
        if (resp.code == 0) {
          if (tag) {
            this.$Message.success('提交成功!');
          } else {
            this.$Message.success('保存成功!');
          }
          if (this.quickRegistration && tag && !this.rowData.act_inst_id) {
            this.rowData.rybh = resp.data
            this.businessId = this.rowData.rybh
            this.msgUrl = '/#/detentionBusiness/rehabilitationEnterRegister?eventCode=' + this.rowData.rybh
            this.$refs['approval'].openStartApproval()
          } else {
            this.handleClose();
          }
        } else {
          this.$Notice.error({
            title: '错误提示',
            desc: resp.msg
          })
        }
      })
    },
    startError(data) {
      this.errorModal({ content: '流程启动失败。原因:' + data.msg }).then(() => {
        location.reload()
      })
    },
    startUpSuccess(data) {
      return new Promise((resolve, reject) => {
        let that = this
        setTimeout(() => {
          this.updateRegStatus(data)
        }, 500)
      })
    },
    updateRegStatus(data) {
      let params = {
        rybh: this.rowData.rybh,
        taskId: "",
        actInstId: data.actInstId
      }
      //调用接口
      this.$store.dispatch('authPostRequest', {
        url: this.$path.app_inRecordJdsUpdateWorkflowInfo,
        params: params
      }).then(resp => {
        if (resp.code == 0) {
          this.$Message.success('提交成功!');
          this.handleClose();
        } else {
          this.$Message.error(resp.msg);
        }
      })
    },
    beforeOpen() {
      return new Promise((resolve, reject) => {
        this.$set(this.approvalData, 'businessId', this.rowData.rybh)
        this.msgUrl = '/#/detentionBusiness/rehabilitationEnterRegister?eventCode=' + this.rowData.rybh
        this.msgTit = `【收戒入所审批】民警：${this.formData.jbr}于${this.formData.jbsj}提交了对${this.formData.xm}收戒入所的申请，请尽快审批！`
        resolve(true)
      })
    },
  },
  mounted() {
    this.formData.jbsj = getCurrentTimeFormatted();
    this.$set(this.formData, 'symjxm', getUserCache.getUserName())
    this.$set(this.formData, 'jbr', getUserCache.getUserName())
    if (this.saveType == "edit") {
      this.formData = JSON.parse(JSON.stringify(this.rowData))
      this.formData.jbsj = getCurrentTimeFormatted();
      this.$set(this.formData, 'jbr', getUserCache.getUserName())
      if (this.rowData.status == "01") {
        this.getRecordByzjhm();
      }
      if (this.rowData.status == "02") {
        this.getDetail(this.rowData.id)
      }

    } else {
      this.$refs.caseInfoForm.updataSjpzwsdzUrl()
    }
  }
}
</script>

<style scoped lang="less">
.steps-box {
  height: 100px;
  display: flex;
  align-items: center;
}

.bsp-imgminio-container {
  width: 100% !important;
}
</style>
