<template>
  <div>
    <Form ref="formData" :model="formData" :rules="ruleValidate" :label-width="150" label-colon>
      <p class="subtit"><Icon type="md-list-box" size="24" color="#2b5fda" />基本信息</p>
      <div class="form">
        <Row>
          <Col span="8">
            <FormItem label="证件号码" prop="zjhm" :rules="[{ trigger: 'blur', message: '证件号码为必填', required: true }]">
              <Input type="text" v-model="formData.zjhm" placeholder="文本输入或二代证扫描"/>
            </FormItem>
          </Col>
          <Col span="2">
            <Button style="margin: 0 0px 0 19px" type="primary" @click.native="getRecordByzjhmEvent()">执法办案转入</Button>
          </Col>
          <Col span="6">
            <FormItem label="档案编号" prop="dabh" >
              <Input type="text" v-model="formData.dabh" placeholder="系统自动生成" disabled/>
            </FormItem>
          </Col>
          <Col span="8">
            <FormItem label="证件类型"  prop="zjlx" :rules="[{ trigger: 'blur,change', message: '证件类型为必填', required: true }]">
              <s-dicgrid v-model="formData.zjlx" @change="$refs.formData.validateField('zjlx')" :isSearch="true"  dicName="ZD_ZJLX" />
            </FormItem>
          </Col>
        </Row>
        <Row>
          <Col span="8">
            <FormItem label="信息核查"  prop="xxhc">
              <s-dicgrid v-model="formData.xxhc" @change="$refs.formData.validateField('xxhc')" :isSearch="true"  dicName="ZD_XXHC" />
            </FormItem>
          </Col>
          <Col span="8">
            <Button style="margin: 0 13px 0 16px" type="primary">核录比对</Button>
          </Col>
          <Col span="8">
            <FormItem label="人员编号" prop="rybh">
              <Input type="text" v-model="formData.rybh" placeholder="系统自动生成" disabled/>
            </FormItem>
          </Col>

        </Row>
        <Row>
          <Col span="8">
            <FormItem label="姓名" prop="xm" :rules="[{ trigger: 'blur', message: '姓名为必填', required: true }]">
              <Input type="text" v-model="formData.xm" placeholder="文本输入或接口复用数据"/>
            </FormItem>
          </Col>
          <Col span="8">
            <FormItem label="曾用名/别名/绰号" :label-width="200" prop="bm" :rules="!quickRegistration?[{ trigger: 'blur', message: '曾用名/别名/绰号为必填', required: true }]:[]">
              <Input type="text" v-model="formData.bm" placeholder="请填写"/>
            </FormItem>
          </Col>
          <Col span="8">
            <FormItem label="性别"  prop="xb" :rules="[{ trigger: 'blur,change', message: '性别为必填', required: true }]">
              <s-dicgrid v-model="formData.xb" @change="$refs.formData.validateField('xb')" :isSearch="true"  dicName="ZD_XB" />
            </FormItem>
          </Col>
        </Row>
        <Row v-if="quickRegistration">
          <Col span="8">
            <FormItem label="强制收戒决定机关"  prop="sjjdjgmc" :rules="[{ trigger: 'blur,change', message: '收戒决定机关为必填', required: true }]">
              <s-dicgrid v-model="formData.sjjdjgmc" @change="$refs.formData.validateField('sjjdjgmc')" :isSearch="true"  dicName="ZD_BADW_GAJG" />
            </FormItem>
          </Col>
          <Col span="8">
            <FormItem label="收戒决定机关类型"  prop="sjjdjglx" :rules="[{ trigger: 'blur,change', message: '收戒决定机关类型为必填', required: true }]">
              <s-dicgrid v-model="formData.sjjdjglx" @change="$refs.formData.validateField('sjjdjglx')" :isSearch="true"  dicName="ZD_SYYWSYDWLX" />
            </FormItem>
          </Col>
          <Col span="8">
            <FormItem label="收戒民警" prop="sjmj" :rules="[{ trigger: 'blur', message: '收戒民警姓名为必填', required: true }]">
              <Input type="text" v-model="formData.sjmj" placeholder="请填写"/>
            </FormItem>
          </Col>
        </Row>
        <Row v-if="quickRegistration">
          <Col span="8">
            <FormItem label="戒室号" prop="jsh" :rules="[{ trigger: 'blur,change', message: '戒室号为必填', required: true }]">
              <Select v-model="formData.jsh" @on-change="orgChange">
                <Option v-for="item in orgCodeList" :value="item.roomCode" :key="item.roomCode">{{ item.roomName }}</Option>
              </Select>
            </FormItem>
          </Col>
          <Col span="8">
            <FormItem label="案件类别"  prop="ajlbdm" :rules="[{ trigger: 'blur,change', message: '案件类别为必填', required: true }]">
              <s-dicgrid v-model="formData.ajlbdm" @change="$refs.formData.validateField('ajlbdm')" :multiple="true" :isSearch="true"  dicName="ZD_AJLB" />
            </FormItem>
          </Col>
          <Col span="8">
            <FormItem label="入所日期" prop="rsrq" :rules="[{ trigger: 'blur,change', message: '入所日期为必填', required: true }]">
              <el-date-picker type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd" @change="$refs.formData.validateField('rsrq')"  v-model="formData.rsrq" size="small"  placeholder="请选择" style="width: 100%;" />
            </FormItem>
          </Col>
        </Row>
        <Row>
          <Col span="8">
            <FormItem label="是否为在校学生" :label-width="150" prop="sfwxzxs" :rules="!quickRegistration?[{ trigger: 'blur,change', message: '请选择是否为在校学生', required: true }]:[]">
              <RadioGroup v-model="formData.sfwxzxs">
                <Radio label="1">是</Radio>
                <Radio label="0">否</Radio>
              </RadioGroup>
            </FormItem>
          </Col>
          <Col span="8">
            <FormItem label="学校名称"  prop="xxmc">
              <s-dicgrid v-model="formData.xxmc" @change="$refs.formData.validateField('xxmc')" :isSearch="true"  dicName="ZD_XXMC" />
            </FormItem>
          </Col>
          <Col span="8">
            <FormItem label="文化程度"  prop="whcd" :rules="!quickRegistration?[{ trigger: 'blur,change', message: '文化程度为必填', required: true }]:[]">
              <s-dicgrid v-model="formData.whcd" @change="$refs.formData.validateField('whcd')" :isSearch="true"  dicName="ZD_WHCD" />
            </FormItem>
          </Col>
        </Row>
        <Row>
          <Col span="8">
            <FormItem label="人员管理类别"  prop="gllb">
              <s-dicgrid v-model="formData.gllb" @change="$refs.formData.validateField('gllb')" :isSearch="true"  dicName="ZD_KSS_RYGLLB" />
            </FormItem>
          </Col>

          <Col span="8">
            <FormItem label="出生日期" prop="csrq" :rules="!quickRegistration?[{ trigger: 'blur,change', message: '出生日期为必填', required: true }]:[]">
              <el-date-picker type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd" @change="$refs.formData.validateField('csrq')"  v-model="formData.csrq" size="small"  placeholder="请选择" style="width: 100%;" />
            </FormItem>
          </Col>
          <Col span="8">
            <FormItem label="职业"  prop="zy" :rules="!quickRegistration?[{ trigger: 'blur,change', message: '职业为必填', required: true }]:[]">
              <s-dicgrid v-model="formData.zy" @change="$refs.formData.validateField('zy')" :isSearch="true"  dicName="ZD_ZY" />
            </FormItem>
          </Col>
        </Row>
        <Row>
          <Col span="8">
            <FormItem label="民族"  prop="mz" :rules="!quickRegistration?[{ trigger: 'blur,change', message: '民族为必填', required: true }]:[]">
              <s-dicgrid v-model="formData.mz" @change="$refs.formData.validateField('mz')" :isSearch="true"  dicName="ZD_MZ" />
            </FormItem>
          </Col>
          <Col span="8">
            <FormItem label="婚姻状况"  prop="hyzk" :rules="!quickRegistration?[{ trigger: 'blur,change', message: '婚姻状况为必填', required: true }]:[]">
              <s-dicgrid v-model="formData.hyzk" @change="$refs.formData.validateField('hyzk')" :isSearch="true"  dicName="ZD_HYZK" />
            </FormItem>
          </Col>
          <Col span="8">
            <FormItem label="籍贯"  prop="jg" :rules="!quickRegistration?[{ trigger: 'blur,change', message: '籍贯为必填', required: true }]:[]">
              <s-dicgrid v-model="formData.jg" @change="$refs.formData.validateField('jg')" :isSearch="true"  appMark="bsp"  dicName="ZD_GABBZ_JG" />
            </FormItem>
          </Col>
        </Row>
        <Row>
          <Col span="8">
            <FormItem label="国籍"  prop="gj" :rules="!quickRegistration?[{ trigger: 'blur,change', message: '国籍为必填', required: true }]:[]">
              <s-dicgrid v-model="formData.gj" @change="$refs.formData.validateField('gj')" :isSearch="true" appMark="bsp" dicName="ZD_GABBZ_GJ" />
            </FormItem>
          </Col>
          <Col span="8">
            <FormItem label="政治面貌"  prop="zzmm" :rules="!quickRegistration?[{ trigger: 'blur,change', message: '政治面貌为必填', required: true }]:[]">
              <s-dicgrid v-model="formData.zzmm" @change="$refs.formData.validateField('zzmm')" :isSearch="true"  dicName="ZD_ZZMM" />
            </FormItem>
          </Col>
          <Col span="8">
            <FormItem label="宗教信仰"  prop="zjxy" :rules="!quickRegistration?[{ trigger: 'blur,change', message: '宗教信仰为必填', required: true }]:[]">
              <s-dicgrid v-model="formData.zjxy" @change="$refs.formData.validateField('zjxy')" :isSearch="true"  dicName="ZD_ZJXY" />
            </FormItem>
          </Col>
        </Row>
        <Row>
          <Col span="8">
            <!--              <FormItem label="户籍所在地" prop="hjd" :rules="[{ trigger: 'change', message: '户籍所在地为必填', required: true }]">-->
            <!--                <Input type="text" v-model="formData.hjd" placeholder="请填写"/>-->
            <!--              </FormItem>-->

            <FormItem label="户籍所在地"  prop="hjd" :rules="!quickRegistration?[{ trigger: 'blur,change', message: '户籍所在地为必填', required: true }]:[]">
              <s-dicgrid v-model="formData.hjd" @change="$refs.formData.validateField('hjd')" :isSearch="true" appMark="bsp" dicName="ZD_GABBZ_JG" />
            </FormItem>
          </Col>
          <Col span="16">
            <FormItem label="户籍所在地详址" prop="hjdxz" :rules="!quickRegistration?[{ trigger: 'change', message: '户籍所在地详址为必填', required: true }]:[]">
              <Input type="text" v-model="formData.hjdxz" placeholder="请填写"/>
            </FormItem>
          </Col>
        </Row>
        <Row>
          <Col span="8">
            <!--              <FormItem label="现居住地" prop="xzz" :rules="[{ trigger: 'change', message: '现居住地为必填', required: true }]">-->
            <!--                <Input type="text" v-model="formData.xzz" placeholder="请填写"/>-->
            <!--              </FormItem>-->

            <FormItem label="现居住地"  prop="xzz" :rules="!quickRegistration?[{ trigger: 'blur,change', message: '现居住地为必填', required: true }]:[]">
              <s-dicgrid v-model="formData.xzz" @change="$refs.formData.validateField('xzz')" :isSearch="true" appMark="bsp" dicName="ZD_GABBZ_JG" />
            </FormItem>
          </Col>
          <Col span="16">
            <FormItem label="现居住地详址" prop="xzzxz" :rules="!quickRegistration?[{ trigger: 'change', message: '现居住地详址为必填', required: true }]:[]">
              <Input type="text" v-model="formData.xzzxz" placeholder="请填写"/>
            </FormItem>
          </Col>
        </Row>
        <Row>

          <Col span="8">
            <FormItem label="身份"  prop="sf" :rules="!quickRegistration?[{ trigger: 'blur,change', message: '身份为必填', required: true }]:[]">
              <s-dicgrid v-model="formData.sf" @change="$refs.formData.validateField('sf')" :isSearch="true"  dicName="ZD_C_SF" />
            </FormItem>
          </Col>
          <Col span="8">
            <FormItem label="特殊身份"  prop="tssf" :rules="!quickRegistration?[{ trigger: 'blur,change', message: '特殊身份为必填', required: true }]:[]">
              <s-dicgrid v-model="formData.tssf" @change="$refs.formData.validateField('tssf')" :isSearch="true"  dicName="ZD_TSSF" />
            </FormItem>
          </Col>
          <Col span="8">
            <FormItem label="工作单位" prop="gzdw">
              <Input type="text" v-model="formData.gzdw" placeholder="请填写"/>
            </FormItem>

            <!--              <FormItem label="工作单位"  prop="gzdw">-->
            <!--                <s-dicgrid v-model="formData.gzdw" @change="$refs.formData.validateField('gzdw')" :isSearch="true"  dicName="ZD_JSROOM8" />-->
            <!--              </FormItem>-->
          </Col>
        </Row>
        <Row>
          <Col span="8">
            <FormItem label="专长" prop="tc">
              <Input type="text" v-model="formData.tc" placeholder="请填写"/>
            </FormItem>
          </Col>
          <Col span="8">
            <FormItem label="联系电话" prop="lxdh">
              <Input type="text" v-model="formData.lxdh" placeholder="请填写"/>
            </FormItem>
          </Col>
        </Row>
      </div>
    </Form>

  </div>
</template>

<script>
  import {mapActions} from "vuex";
  import { getUserCache } from '@/libs/util'

  export default {
    props:{
      formData:{
        type: [Array,Object],
        default: () => ({})
      },
      quickRegistration:{
        type: Boolean,
        default: false,
      },
    },
    data(){
      return{
        ruleValidate: {},
        orgCodeList: [],
      }
    },
    methods:{
      ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
      getRecordByzjhmEvent(){
        this.$emit('getRecordByzjhm',false)
      },
      getAreaList(){
        let params = {
          // sameCase:true,
          pageNo:1,
          pageSize:100,
          // roomType:'7'
        }
        let orgCode = getUserCache.getOrgCode()
        params.orgCode = orgCode
        this.$store.dispatch('authPostRequest',{
          url: this.$path.app_getAreaPrisonRoomPage,
          params:params
        }).then(resp=>{
          this.loading=false
          if (resp.code==0){
            if(resp.data && resp.data.list && resp.data.list.length > 0){
              this.orgCodeList  = resp.data.list.map(item => ({
                roomCode: item.roomCode,
                roomName: item.roomName,
                areaId:item.areaId,
                areaName:item.areaName
              }));
            }

          }else{
            this.$Notice.error({
              title:'错误提示',
              desc:resp.msg
            })
          }
        })
      },
      orgChange(data) {
        console.log(data,"orgChange");
        if(data) {
          this.formData.jsh = data
          let org = this.orgCodeList.find(t=>t.roomCode == data);
          this.formData.roomName = org.roomName;
          this.formData.areaId = org.areaId;
          this.formData.areaName = org.areaName;
        }
      },
      areaChange(data) {
        console.log(data);
        if(!data) {

        }
      },
      getRecordByzjhm(){
        if(!this.formData.zjhm || !this.formData.zjhm.trim()){
          this.$Message.error('请填写证件号码!!');
          return;
        }
        this.$store.dispatch('authGetRequest',{
          url: this.$path.app_getRecordByzjhm,
          params: {
            zjhm:this.formData.zjhm
          }
        }).then(resp => {
          if(resp.code == 0){
            this.formData = resp.data
          }else{
            this.$Modal.error({
              title: '温馨提示',
              content: resp.msg || '操作失败'
            })
          }
        })
      },
      validate() {
        return new Promise((resolve) => {
          this.$refs.formData.validate(valid => {
            resolve(valid); // valid 是 boolean，表示是否验证通过
          });
        });
      }
    },
    mounted() {
      this.getAreaList()
    }
  }
</script>

<style scoped>

</style>
