<template>
  <Modal v-model="visible" :mask-closable="false" :styles="{ top: '50px' }" :closable="false" width="860"
    title="个别谈话教育结果">
    <Form ref="form" v-loading.body="loading" :model="formData" :rules="ruleValidate" :label-width="140"
      :label-colon="true">
      <Row>
        <Col :span="24">
        <!-- 原型与数据库返回数据类型不统一，且间隔时间长，顾先使用单选且没有多级数据的形式完成基础功能 -->
        <FormItem label="谈话结论" prop="talkSummary">
          <Select v-model="formData.talkSummary" @on-change="handleConclusionChange" :label-in-value="true"
            style="width:100%">
            <Option v-for="item in talkConclusionList" :value="item.code" :key="item.code">{{ item.name }}
            </Option>
          </Select>
        </FormItem>
        </Col>
        <Col :span="24" v-if="formData.talkSummary === '99'">
        <FormItem label="请输入详细结论" prop="xxjl">
          <Input type="text" v-model="formData.xxjl" style="width: 100%" />
        </FormItem>
        </Col>
        <Col :span="24">
        <FormItem label="人员标签" prop="rybq">
          <Select v-model="formData.rybq" multiple @on-change="handleTagChange" style="width: 100%"
            :label-in-value="true">
            <Option v-for="item in prisonerTagList" :value="item.id" :key="item.id">{{ item.tagName }}</Option>
          </Select>
        </FormItem>
        </Col>
        <Col :span="24">
        <FormItem label="是否岗位协同">
          <i-switch v-model="isCooperate" @on-change="getJobList" />
        </FormItem>
        </Col>
        <Col :span="24" v-if="isCooperate">
        <FormItem label="协同岗位" prop="jobCollaborationList">
          <CheckboxGroup v-model="formData.jobCollaborationList" style="
                width: 100%;
                display: flex;
                gap: 10px
              ">
            <Checkbox v-for="item in jobList" :key="item.code" style="display: inline-block" size="large"
              :label="item.code">{{ item.name }}
            </Checkbox>
          </CheckboxGroup>
        </FormItem>
        </Col>
        <Col v-if="isCooperate" :span="24">
        <FormItem label="指定用户">
          <userSelector :disabled="!formData.jobCollaborationList.length" v-model="formData.pushUserid"
            :text.sync="formData.pushUserName" returnField="id" numExp="num>=1" msg="至少选中1人">
          </userSelector>
        </FormItem>
        </Col>
        <Col :span="24">
        <FormItem label="更多评价">
          <i-switch v-model="isMoreCommit" @on-change="handleMoreCommitChange" />
        </FormItem>
        </Col>
        <Col v-if="isMoreCommit" :span="24">
        <FormItem label="评价内容" prop="commitList">
          <CheckboxGroup v-model="formData.commitList" style="width: 100%">
            <div style="border-radius: 3px; border: 1px solid #2b5fd933; padding: 5px;background-color: #2b5fd91a;">
              <Checkbox v-for="(item) in commitDictList" :key="item.code" :label="item.code">{{ item.name }}</Checkbox>
            </div>
          </CheckboxGroup>
        </FormItem>
        </Col>
        <Col v-if="
          formData.jgryxm &&
          formData.talkSummary &&
          formData.tagName
        " :span="24">
        <FormItem label="">
          <p style="padding: 8px 10px; border: 1px solid #ddd">
            {{ formData.jgryxm }}本次谈话后{{
              formData.tagName
            }}、{{ formData.talkSummaryString }}，各岗位同事多加注意。
          </p>
        </FormItem>
        </Col>
      </Row>
    </Form>
    <div slot="footer">
      <div class="modal-footer-buttons">
        <Button type="primary" :loading="loading" @click="handleSubmit" class="save">提交</Button>
      </div>
    </div>
  </Modal>
</template>

<script>
import api from "../api.js";
import { userSelector } from "sd-user-selector";
import { mapActions } from "vuex";
export default {
  name: "SubmitModal",
  components: {
    userSelector
  },
  data() {
    return {
      visible: false,
      isCooperate: false,
      isMoreCommit: false,
      loading: false,
      formData: {
        talkSummary: "",
        talkSummaryString: "",
        rybq: [],
        xxjl: "",
        jobCollaboration: "",
        jobCollaborationList: [],
        commitList: [],
        pushUserid: "",
        pushUserName: "",
        tagName: "",
        tagId: "",
        recordType: "",
        psychologyAssess: "",
      },
      ruleValidate: {
        talkSummary: [
          { trigger: 'blur,change', message: '必选', required: true },
        ],
        xxjl: [
          { trigger: 'blur,change', message: '必填', required: true },
        ]
      },
      talkConclusionList: [],
      jobList: [],
      prisonerTagList: [],
      commitDictList: []
    };
  },
  computed: {},
  created() {
  },
  mounted() {
  },
  methods: {
    ...mapActions([
      "authGetRequest",
      "authPostRequest",
    ]),
    open(data) {
      Object.assign(this.formData, data);
      this.visible = true;
      this.getTalkConclusionList();
      this.getPrisonerTagList();
    },
    // 获取谈话结论
    getTalkConclusionList() {
      this.authGetRequest({
        url: "/bsp-uac/ops/dic/code/getDicCodeTreeData",
        params: {
          dicName: "ZD_THJY_THXJ",
        },
      }).then((res) => {
        if (res.success) {
          this.talkConclusionList = res.data;
        } else {
          this.$Message.error("获取谈话结论字典失败")
        }
      });
    },
    // 获取人员标签
    getPrisonerTagList() {
      this.authPostRequest({
        url: this.$path.bsp_talk_ryTagList,
        params: {
          remarks: "",
          tagName: "",
        },
      }).then((res) => {
        if (res.success) {
          this.prisonerTagList = res.data;
        } else {
          this.$Message.error("获取人员标签字典失败")
        }
      });
    },
    // 获取协同岗位字典
    getJobList(e) {
      e && this.authGetRequest({
        url: `/bsp-com/static/dic/tem/ZD_THJY_GWXT.js`,
      }).then((res) => {
        let func = { getData: eval("(" + res + ")") };
        this.jobList = func.getData();
      });

      if (!e) {
        this.formData.jobCollaborationList = [];
      }
    },
    // 获取评论字典
    getCommitList() {
      this.authGetRequest({
        url: "/bsp-uac/ops/dic/code/getDicCodeTreeData",
        params: {
          dicName: "ZD_THJY_THPJ",
        },
      }).then((res) => {
        if (res.success) {
          this.commitDictList = res.data;
        } else {
          this.$Message.error("获取评论字典失败")
        }
      });
    },
    // 监听结论变化
    handleConclusionChange(data) {
      this.formData.talkSummaryString = data?.label;
    },
    // 监听人员标签变化
    handleTagChange(data) {
      if (data.length) {
        let ids = [];
        let names = [];
        data.forEach(item => {
          ids.push(item.value);
          names.push(item.label);
        })
        this.formData.tagId = ids.join(",");
        this.formData.tagName = names.join(",");
      } else {
        this.formData.tagId = "";
        this.formData.tagName = "";
      }
    },
    // 监听更多评价变化
    handleMoreCommitChange(value) {
      if (value) {
        // 如果开启更多评价，加载评价选项
        this.getCommitList();
      } else {
        // 如果取消选择更多评价，清空评价选项
        this.formData.commitList = [];
        this.formData.psychologyAssess = "";
      }
    },
    // 平铺数组且去重
    flattenArray(arr) {
      // 参数校验
      if (!Array.isArray(arr)) {
        console.error(arr, '平铺数组时，输入必须是一个数组');
      }

      // 使用递归处理多维数组
      const result = [];
      const flatten = (nested) => {
        for (const item of nested) {
          if (Array.isArray(item)) {
            flatten(item);
          } else {
            result.push(item);
          }
        }
      };

      flatten(arr);

      return [...new Set(result)];
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.loading = true;
          this.formData.psychologyAssess = this.formData.commitList.join(",");
          this.formData.jobCollaboration = this.formData.jobCollaborationList.join(',');
          this.formData.talkReason = this.flattenArray(this.formData.thyy).join(",");
          this.authPostRequest({
            url: this.$path.bsp_test_tem_grTalk_submit,
            params: this.formData,
          }).then((res) => {
            this.loading = false;
            if (res.success) {
              this.$refs.form.resetFields();
              this.$router.replace({ name: "individualTalkEducationList" });
            } else {
              this.errorModal({ content: res.msg || "保存失败" });
            }
          });
        } else {
          this.$Message.error(
            "表单验证失败，请确定选择了监管人员与填写了必填表单。"
          );
        }
      });
    }
  }
}
</script>
