<template>
  <Modal v-model="visible" :mask-closable="false" footer-hide :styles="{ top: '50px' }" :closable="false" width="980">
    <div slot="header"
      style="display: flex;padding: 0 15px;flex-direction: row; justify-content: space-between; align-items: center;">
      <span>谈话笔录</span>
      <Icon style="cursor: pointer;" title="点击缩小" type="md-contract" size="26" @click="handleShrink"></Icon>
    </div>
    <RecordArea :key="visible" :value="records" @input="handleInput" />
  </Modal>
</template>

<script>
import { RecordArea } from "./index";
export default {
  components: {
    RecordArea
  },
  name: "RecordAreaModal",
  data() {
    return {
      visible: false,
      records: []
    };
  },
  created() { },
  methods: {
    open(records) {
      this.visible = true;
      this.records = [...records];
    },
    handleInput(newRecords) {
      this.records = newRecords;
    },
    handleShrink() {
      this.visible = false;
      this.$emit("shrink", this.records);
    }
  }
};
</script>

<style lang="less" scoped>
/deep/ .ivu-modal-body {
  height: 750px;
  padding: 0;
  overflow-y: auto;
}

/deep/ .qa-item {
  font-size: 16px;
}
</style>