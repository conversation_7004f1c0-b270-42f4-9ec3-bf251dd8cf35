<template>
  <Tabs value="talkTemplate" class="fixTabsHeight">
    <TabPane label="谈话模板" name="talkTemplate">
      <div style="padding: 15px; display: flex; flex-direction: column; height: 100%">
        <div class="thmb-header">
          <div class="thmb-title">
            <span style="display: inline-block; width: 90px">选择模板：</span>
            <el-cascader v-model="localTalkReason" :options="props.talkReasonList" :show-all-levels="false" size="small"
              popper-class="areaClassName" @change="handleTalkReasonChange" style="width: 80%" :stretch="true" :props="{
                multiple: false,
                value: 'code',
                label: 'name',
                children: 'subTreeData'
              }" :disabled="!props.isTalkingState"></el-cascader>
          </div>
          <div>
            <Button type="primary" icon="md-arrow-back" size="small" @click="handleImportTemplate()"
              :disabled="!props.isTalkingState || !localTalkReason.length"
              style="padding: 0 5px; margin: 0">添加全部</Button>
            <span
              style="display: inline-block; line-height: 30px; margin-left: 12px; color: red; font-size: 13px">双击下方对话可添加至左侧笔录中</span>
          </div>
        </div>
        <div v-loading="loading" class="template-list" style="flex:1">
          <div v-if="templates.length && props.isTalkingState">
            <div v-for="(item, index) in templates" :key="index" class="template-item"
              @dblclick="handleImportTemplate(item)" @mouseenter="hoverIndex = index" @mouseleave="hoverIndex = null">
              <p>
                <span style="font-weight: bold; color: #3768dc">问：</span>{{ item.question }}
              </p>
              <p>
                <span style="font-weight: 600; color: #3768dc">答：</span>{{ item.answer }}
              </p>
            </div>
          </div>
          <div class="tip" v-else>
            <img src="@/assets/images/no-people.png" alt="" />
            <p>暂无数据</p>
          </div>
        </div>
      </div>
    </TabPane>
    <TabPane label="近期谈话动态" name="recentlyTalk">
      <div style="padding: 10px;display:flex; justify-content: center;height: 100%">
        <div class="tip">
          <img src="@/assets/images/no-people.png" alt="" />
          <p>暂无数据</p>
        </div>
      </div>
    </TabPane>
  </Tabs>
</template>

<script>
import { mapActions } from "vuex";
export default {
  props: {
    props: {
      type: Object,
      default: () => ({})
    },
  },
  data() {
    return {
      hoverIndex: null,
      localTalkReason: [],
      templates: [],
      talkTemplateList: [],
      loading: false,
    };
  },
  watch: {
    "props.thyy": {
      deep: true,
      handler(newVal, oldVal) {
        // 获取模板
        if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
          this.localTalkReason = newVal;
          this.handleTalkReasonChange(newVal)
        }
      },
    },
  },
  created() {
  },
  methods: {
    ...mapActions([
      "authPostRequest",
    ]),
    handleImportTemplate(template) {
      if (template) {
        this.$emit("importSingle", template);
      } else {
        this.$emit("importAll", this.templates);
      }

    },
    getTempList(type) {
      if (!type) return;

      this.loading = true;
      this.authPostRequest({
        url: this.$path.bsp_talk_talkTemplateList,
        params: {
          templateType: type,
        },
      }).then((resp) => {
        this.loading = false;
        if (resp.success) {
          this.talkTemplateList = resp.data;
          const allContents = [];
          this.talkTemplateList.forEach((template) => {
            if (template.talkTemplateContents?.length) {
              allContents.push(...template.talkTemplateContents);
            }
          });
          this.templates = allContents.length ? this.transformTemData(allContents) : [];
        } else {
          this.$Message.error(resp.msg || "操作失败");
        }
      });
    },
    handleTalkReasonChange(data) {
      if (data && data.length) {
        this.getTempList(data[data.length - 1]);
      } else {
        this.templates = [];
      }
    },
    transformTemData(temData) {
      const result = [];
      const groups = {};

      temData.forEach((item) => {
        const groupKey = String(item.contentGroup); //contentGroup转为字符串(对象键只能是字符串)
        // console.log(groupKey,'groupKey');

        if (!groups[groupKey]) {
          groups[groupKey] = {};
        }

        const type = String(item.contentType).trim();
        // console.log(type,'type');

        if (type === "1") {
          groups[groupKey].question = item.content;
        } else if (type === "2") {
          groups[groupKey].answer = item.content;
        }
      });
      // console.log(groups,'groups');

      for (const groupId in groups) {
        const group = groups[groupId];
        if (group.question || group.answer) {
          result.push({
            question: group.question,
            answer: group.answer,
            groupId: groupId,
          });
        }
      }

      return result;
    },
  },
};
</script>

<style lang="less" scoped>
.fixTabsHeight {
  height: 100% !important;
}

/deep/ .ivu-tabs-bar {
  margin-bottom: 0;

  .ivu-tabs-nav {
    width: 100% !important;

    .ivu-tabs-tab {
      width: 50%; // 因为只有两个tab 暂时写死
      text-align: center;
      margin-right: 0;
      padding-top: 10px;
      padding-bottom: 15px;
      font-size: 16px;

      &.ivu-tabs-tab-active {
        font-weight: 600;
      }
    }
  }
}

/deep/ .ivu-tabs-content {
  height: 100% !important;
}

.thmb-header {
  padding-bottom: 10px;
  border-bottom: 1px solid #e8eef0;

  .thmb-title {
    display: flex;
    line-height: 30px;
    margin-bottom: 12px;
  }
}

.template-list {
  width: 100%;
  padding: 10px;
  padding-bottom: 20px;
  max-height: 520px;
  overflow: hidden;
  overflow-y: auto;

  .template-item {
    cursor: pointer;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    background: #f3f6fd;
    margin-bottom: 10px;
    border-radius: 4px;
    border: 1px #cfd9f0 solid;
    padding: 10px;
    position: relative;
    padding-left: 30px;
    /* 给图标留空间 */
    overflow: hidden;

    &:hover {
      border-color: #3491fa;
      background: #e0efff;
      transition: all 0.2s;
    }

    &:before {
      content: "〈";
      position: absolute;
      font-weight: bolder;
      font-size: 16px;
      display: flex;
      color: #3491fa;
      left: 0;
      top: 0;
      right: 0;
      bottom: 0;
      align-items: center;
      opacity: 0;
      left: 10px;
    }

    &:hover:before {
      left: 0;
      opacity: 1;
      transition: all 0.5s;
    }

    p {
      font-size: 16px;
    }

    .icon {
      position: absolute;
      left: 0px;
      top: 50%;
      transform: translateY(-50%);
      display: inline-block;
      animation: shake 0.5s infinite alternate;
      animation-play-state: running;
      /* 默认动画运行 */
      cursor: pointer;
      width: 20px;
      height: 20px;

      img {
        width: 100%;
        height: 100%;
      }

      /* 鼠标悬停在图标上时，动画暂停 */
      &:hover {
        animation-play-state: paused;
      }
    }

    /* 左右摇动动画 */
    @keyframes shake {
      0% {
        transform: translateY(-50%) translateX(0);
      }

      100% {
        transform: translateY(-50%) translateX(5px);
      }
    }

    p {
      padding: 3px 0px;
      font-size: 16px;
      font-weight: 500;
      color: #000;
      letter-spacing: 1px;
    }
  }
}

.tip {
  text-align: center;
  display: flex;
  height: 100%;
  flex-flow: column;
  align-items: center;
  justify-content: center;

  >img {
    width: 133px;
    height: 108px;
  }

  >p {
    font-size: 18px;
    font-weight: 400;
    text-align: center;
    color: #666666;
    line-height: 50px;
  }
}

.areaClassName .el-scrollbar__wrap {
  height: 200px !important;
}
</style>
