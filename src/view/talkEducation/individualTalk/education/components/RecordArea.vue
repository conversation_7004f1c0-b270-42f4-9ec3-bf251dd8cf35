<template>
  <div class="qa-item">
    <template v-for="(item, index) in records">
      <div class="question" v-show="item.isQuestionEditing" :key="item.questionTime">
        <textarea :ref="'questionInput' + index" v-model="item.question"
          @keydown.enter.prevent="handleQuestionEnter(index)" @keydown.backspace="
            handleBackspace('question', index, $event)
            " placeholder="输入问题后按回车确认" autoFocus rows="1" @input="resizeTextarea" style="width: 80%"></textarea>
        <span style="
          font-size: 14px;
          color: rgb(1 172 178);
          margin: 0px 10px;
          width: 140px;
        ">{{ item.questionTime || "" }}</span>
      </div>
      <div class="answer" v-show="item.isAnswerEditing" :key="item.anwserTime">
        <textarea :ref="'answerInput' + index" v-model="item.answer"
          @keydown.enter.prevent="handleAnswerEnter(index, $event)" @keydown.backspace="
            handleBackspace('answer', index, $event)
            " placeholder="输入回答后按回车确认" autoFocus rows="1" @input="resizeTextarea"
          style="width: 80%; overflow-y: hidden; resize: none"></textarea>
        <span style="
          font-size: 14px;
          color: rgb(1 172 178);
          margin: 0px 10px;
          width: 140px;
        ">{{ item.answerTime || "未记录时间" }}</span>
      </div>
    </template>
  </div>
</template>

<script>
import api from "../api.js";
import { mapActions } from "vuex";
export default {
  name: "RecordArea",
  props: {
    value: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      records: [...this.value],
      defaultQA: {
        question: "问：",
        answer: "答：",
        isQuestionEditing: true, // 初始第一个问题可编辑
        isQuestionEdit: true,
        isAnswerEditing: false,
        isAnswerEdit: false,
      }
    };
  },
  watch: {
    records(newVal, oldVal) {
      // this.$emit('input', newVal)
      this.$nextTick(() => {
        newVal.forEach((item, index) => {
          const textarea = this.$refs['questionInput' + index][0];
          textarea && this.resizeTextarea({ target: textarea });
        });
        this.autoFocusTheLastInput(newVal);
      });
    },
  },
  methods: {
    ...mapActions([
      "authGetRequest",
    ]),
    resizeTextarea(e) {
      e.target.style.height = "auto";
      e.target.style.height = e.target.scrollHeight + "px";
      e.target.style.overflowY = "hidden";

      const parent = e.target.parentNode;
      if (parent) {
        parent.style.height = "auto";
      }
    },
    // 获取当前时间
    getCurrentTime() {
      return this.authGetRequest({
        url: this.$path.bsp_talk_tem_grTalk_time,
      }).then((res) => {
        return res;
      });
    },
    // 开始笔录
    handleStartRecord(time) {
      this.records.push({
        ...this.defaultQA,
        answerTime: "",
        questionTime: time,
      });
      this.$emit("input", this.records);
    },
    // 设置record
    setRecord(record) {
      this.records =  [...record];
      this.autoFocusTheLastInput(this.records);
    },
    // 添加新问答-单条
    setSingleQA(question, answer) {
      const lastIndex = this.records.length - 1;
      const lastItem = this.records[lastIndex];
      let needConfirm = false;

      // 移除默认前缀（如果存在）
      const cleanQuestion = question.startsWith("问：")
        ? question.slice(2)
        : question;
      const cleanAnswer = answer.startsWith("答：") ? answer.slice(2) : answer;

      this.getCurrentTime().then((timer) => {
        const timeRes = timer.data;

        // 检查最后一条是否是未完成的回答
        const lastAnswer = lastItem.answer.startsWith("答：")
          ? lastItem.answer.slice(2)
          : lastItem.answer;

        const pureQA = {
          question: "问：" + cleanQuestion, // 确保带前缀
          answer: "答：" + cleanAnswer,
          isQuestionEditing: true,
          isAnswerEditing: true,
          isQuestionEdit: false,
          isAnswerEdit: true,
          questionTime: timeRes,
          answerTime: timeRes,
        }
        // 检查最后一条是否是未完成的问题
        const lastQuestion = lastItem.question.startsWith("问：")
          ? lastItem.question.slice(2)
          : lastItem.question;

        if (lastItem.isAnswerEdit && !lastAnswer.trim()) {
          needConfirm = true;
        } else if (lastItem.isQuestionEdit && !lastQuestion.trim()) {
          // 替换未完成的问题（保留默认前缀）
          this.records.splice(lastIndex, 1, pureQA);
        } else {
          // 最后一条已完成，直接添加新记录
          this.records.push(pureQA);
        }

        this.$emit("input", this.records);

        // 处理未完成的回答（需用户确认）
        if (needConfirm) {
          this.$Modal.confirm({
            title: "温馨提示",
            content: "上一个问题是否不作答？",
            onOk: () => {
              this.records.push(pureQA);
              this.$emit("input", this.records);
            },
            onCancel: () => {
              this.$nextTick(() => {
                this.autoFocusTheLastInput(this.records);
              });
            },
          });
          return;
        }
        this.scrollToBottom();
      });
    },
    // 添加新问答-模板全部
    setAllQA(templates) {
      this.$Modal.confirm({
        title: "提示",
        content: "是否全部添加该模板内容？",
        onOk: () => {
          const lastIndex = this.records.length - 1;
          const lastItem = this.records[lastIndex];

          // 统一处理前缀的辅助函数
          const getCleanContent = (text, prefix) =>
            text.startsWith(prefix) ? text.slice(prefix.length) : text;

          this.getCurrentTime().then((timer) => {
            const timeRes = timer.data;
            const pureQA = {
              answer: "答：",
              isQuestionEditing: true,
              isAnswerEditing: true,
              isQuestionEdit: false,
              isAnswerEdit: true,
              questionTime: timeRes,
              answerTime: timeRes,
            };

            // 检查未完成的回答（移除"答："前缀后判断）
            const lastAnswer = getCleanContent(lastItem.answer, "答：");
            if (lastItem.isAnswerEdit && !lastAnswer.trim()) {
              this.$Modal.confirm({
                title: "温馨提示",
                content: "上一个问题是否不作答？",
                onOk: () => {
                  // 用户确认跳过未完成回答，批量添加模板
                  templates.forEach((template) => {
                    this.records.push({
                      question: "问：" + getCleanContent(template.question, "问："),
                      ...pureQA
                    });
                  });
                  this.$emit("input", this.records);
                },
                onCancel: () => {
                  this.$nextTick(() => {
                    this.autoFocusTheLastInput(this.records);
                  });
                },
              });
              return; // 提前返回，避免执行后续逻辑
            }

            // 检查未完成的问题（移除"问："前缀后判断）
            const lastQuestion = getCleanContent(lastItem.question, "问：");

            if (lastItem.isQuestionEdit && !lastQuestion.trim()) {
              this.records.splice(lastIndex, 1); // 删除未完成的问题
              templates.forEach((template) => {
                this.records.push({
                  question: "问：" + getCleanContent(template.question, "问："),
                  ...pureQA
                });
              });
              this.$emit("input", this.records);
              return;
            }

            // 默认情况：直接添加所有模板
            templates.forEach((template) => {
              this.records.push({
                question: "问：" + getCleanContent(template.question, "问："),
                ...pureQA
              });
            });
            this.$emit("input", this.records);
            this.scrollToBottom();
          });
        },
      });
    },
    // 位于最后位置的Input自动聚焦
    autoFocusTheLastInput(record, type) {
      this.$nextTick(() => {
        const lastIndex = record.length - 1;
        const lastQuestionInput = this.$refs['questionInput' + lastIndex][0];
        const lastAnswerInput = this.$refs['answerInput' + lastIndex][0];

        // type === 'question' || lastAnswerInput ? lastAnswerInput?.focus() : lastQuestionInput?.focus();
        if (type) {
          if (type === 'question') {
            lastAnswerInput.focus();
          } else {
            lastQuestionInput.focus();
          }
        } else {
          if (lastAnswerInput) {
            lastAnswerInput.focus();
          } else {
            lastQuestionInput.focus();
          }
        }
      })
    },
    scrollToBottom() {
      this.$nextTick(() => {
        const container = this.$el;
        if (container) {
          container.scrollTop = container.scrollHeight;
        }
      });
    },
    // 监听删除事件
    handleBackspace(type, recordIndex, event) {
      const textarea = event.target;
      const cursorPos = textarea.selectionStart;

      // 如果光标不在开头，正常删除字符
      if (cursorPos > 0) return;

      event.preventDefault();

      const currentRecord = this.records[recordIndex];

      // 删除回答的逻辑
      if (type === "answer") {
        const answerContent = currentRecord.answer.startsWith("答：")
          ? currentRecord.answer.slice(2)
          : currentRecord.answer;

        if (answerContent.trim().length > 0) {
          currentRecord.answer = "答：";
          textarea.value = "答：";
          return;
        }

        // 检查问题是否为空
        const questionContent = currentRecord.question.startsWith("问：")
          ? currentRecord.question.slice(2)
          : currentRecord.question;

        if (!questionContent.trim()) {
          this.records.splice(recordIndex, 1);
        } else {
          currentRecord.isAnswerEditing = false;
          currentRecord.isQuestionEditing = true;
          return;
        }
        this.$emit('input', this.records);
      } else if (type === "question") {
        // 删除问题的逻辑
        // 如果是最后一条记录，不允许删除
        if (this.records.length <= 1) {
          return;
        }

        const questionContent = currentRecord.question.startsWith("问：")
          ? currentRecord.question.slice(2)
          : currentRecord.question;

        if (questionContent.trim().length > 0) {
          currentRecord.question = "问：";
          textarea.value = "问：";
          return;
        }

        // 检查回答是否为空
        const answerContent = currentRecord.answer.startsWith("答：")
          ? currentRecord.answer.slice(2)
          : currentRecord.answer;

        if (!answerContent.trim()) {
          this.records.splice(recordIndex, 1);
        } else {
          currentRecord.isQuestionEditing = false;
          currentRecord.isAnswerEditing = true;
        }
      }

      this.$emit('input', this.records);
      // 智能切换到上一个问答组的可编辑区域
      this.$nextTick(() => {
        const prevIndex = recordIndex - 1;
        if (prevIndex >= 0) {
          const prevRecord = this.records[prevIndex];

          // 优先检查是否有未完成的回答
          const prevAnswerContent = prevRecord.answer.startsWith("答：")
            ? prevRecord.answer.slice(2)
            : prevRecord.answer;

          if (
            prevRecord.isAnswerEdit &&
            prevAnswerContent.trim().length === 0
          ) {
            // 切换到未完成的回答
            prevRecord.isQuestionEditing = true;
            prevRecord.isAnswerEditing = true;
          } else if (prevRecord.isQuestionEdit) {
            // 其次检查是否有未完成的问题
            prevRecord.isQuestionEditing = true;
            prevRecord.isAnswerEditing = true;
          } else { // 如果都已完成，默认切换到回答
            // 检查回答是否有内容
            if (prevAnswerContent.trim().length > 0) {
              // 切换到回答
              prevRecord.isQuestionEditing = true;
              prevRecord.isAnswerEditing = true;
            } else {
              // 切换到问题
              prevRecord.isQuestionEditing = true;
              prevRecord.isAnswerEditing = true;
            }
          }
        }
      });
    },
    // 问题框回车处理
    handleQuestionEnter(index) {
      const defaultQuestionPrefix = "问：";
      let questionContent = this.records[index].question;

      if (questionContent.startsWith(defaultQuestionPrefix)) {
        questionContent = questionContent.slice(defaultQuestionPrefix.length);
      }

      if (!questionContent.trim()) {
        // 只有默认开头，没有内容
        if (index !== 0) {
          this.$set(this.records[index], 'question', "\n");
          return;
        }
      }
      this.getCurrentTime().then((timer) => {
        this.$set(this.records[index], 'answerTime', timer.data);
      });

      // 锁定问题
      this.$set(this.records[index], 'isQuestionEdit', false);
      this.$set(this.records[index], 'isAnswerEditing', true);
      this.$set(this.records[index], 'isAnswerEdit', true);
      this.$emit('input', this.records);

      // 下一个问题输入框自动聚焦
      this.autoFocusTheLastInput(this.records, 'question');
    },
    // 回答框回车处理
    handleAnswerEnter(index, event) {
      // 如果按的是纯回车（没有Shift）
      if (!event.shiftKey) {
        const defaultAnswerPrefix = "答：";
        let answerContent = this.records[index].answer;

        // 如果以默认开头开头，去掉开头
        if (answerContent.startsWith(defaultAnswerPrefix)) {
          answerContent = answerContent.slice(defaultAnswerPrefix.length);
        }

        // 判断剩余内容是否为空
        if (!answerContent.trim()) {
          // 说明只有默认开头，没有实质内容
          this.$set(this.records[index], 'answer', this.records[index].answer + "\n");
          return;
        }

        // 锁定回答
        this.getCurrentTime().then((timer) => {
          const timeRes = timer.data;
          // 添加新问答组
          this.records.push({
            ...this.defaultQA,
            questionTime: timeRes,
          });
          this.$emit('input', this.records);
          this.autoFocusTheLastInput(this.records, 'answer');
        });
      }
    },
  },
};
</script>

<style lang="less" scoped>
.qa-item {
  height: 100%;
  overflow-y: auto;

  .question {
    padding: 10px 5px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    background: #f3f2f2;
    border-radius: 0px;
    width: 100%;
    // min-height: 50px;
    height: auto;
    border: 8px;

    // margin-bottom: 5px;
    textarea {
      border: none;
      background: none;
      white-space: normal;
      word-break: break-word;
      word-wrap: break-word;
      overflow-y: hidden !important;
      resize: none;
      box-sizing: border-box;
      // color: rgb(216, 9, 9);
    }

    textarea::-webkit-scrollbar {
      display: none;
    }

    p {
      white-space: normal;
      word-break: break-word;
      word-wrap: break-word;
    }
  }

  .answer {
    padding: 10px 5px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    background: #fff;
    border-radius: 0px;
    width: 100%;
    height: auto;
    // min-height: 50px;
    border: 8px;

    // margin-bottom: 5px;
    textarea {
      border: none;
      background: none;
      white-space: normal;
      word-break: break-word;
      word-wrap: break-word;
      overflow-y: hidden !important;
      resize: none;
      box-sizing: border-box;
      // color: rgb(14, 172, 235);
    }

    textarea::-webkit-scrollbar {
      display: none;
    }

    p {
      white-space: normal;
      word-break: break-word;
      word-wrap: break-word;
    }
  }
}
</style>