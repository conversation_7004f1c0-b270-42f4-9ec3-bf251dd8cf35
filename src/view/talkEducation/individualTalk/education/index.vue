<template>
  <div>
    <topStat :list="statData"></topStat>
    <rs-DataGrid ref="grid" funcMark="gbthjylb" :customFunc="true">
      <template slot="customHeadFunc" slot-scope="{ hasPermission }">
        <Button type="primary" v-if="hasPermission('gbthjylb:add')" 
          @click="handleCreate">新增谈话</Button>
      </template>

      <template slot="customRowFunc" slot-scope="{ hasPermission, row }">
        <Button type="primary" v-if="hasPermission('gbthjylb:gbthjy')"
          @click="handleUpdate(row)">个别谈话教育</Button>
      </template>
    </rs-DataGrid>
  </div>
</template>

<script>
import { mapActions } from "vuex";
import topStat from "@/components/top-stat";
export default {
  name: "IndividualTalkEducation",
  components: {
    topStat
  },
  data() {
    return {
      statData: [
        {
          title: "全部人员",
          value: "0",
        },
        {
          title: "谈话待办",
          value: "0",
        },
      ],
    };
  },
  created() {
    this.getGridData();
  },
  methods: {
    ...mapActions([
      "postRequest",
    ]),
    getGridData() {
      this.postRequest({
        url: this.$path.get_query_grid,
        params: { modelId: "ihc:visit:count:list" },
      }).then((res) => {
        if (res.success) {
          let data = res.rows[0];
          this.statData[0].value = data.todo_num;
          this.statData[1].value = data.done_num;
        }
      });
    },
    handleCreate() {
      this.$router.replace({
        path: `/talkEducation/individualTalk/education/add`
      })
    },
    handleUpdate(row) {
      this.$router.replace({
        path: `/talkEducation/individualTalk/education/update?id=${row.id}&jgrybm=${row.jgrybm}`
      })
    },
  },
};
</script>

<style lang="less" scoped></style>