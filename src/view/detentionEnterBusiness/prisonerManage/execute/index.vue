<template>
  <!-- 暂予监外执行 -->
  <div>
    <div class="bsp-base-content">
      <s-DataGrid ref="grid" funcMark="zfgl-zyjwzx" :customFunc="true">
        <template slot="customHeadFunc" slot-scope="{ func }">
          <Button v-if="func.includes(globalAppCode + ':zfgl-zyjwzx:add')" type="primary"
            @click.native="handleAdd">暂予监外执行登记</Button>
        </template>
        <template slot="customRowFunc" slot-scope="{ func, row }">
          <Button v-if="func.includes(globalAppCode + ':zfgl-zyjwzx:swhcs') && row.status === '01'" type="primary"
            style="margin-right: 8px" @click="handleUpdate(row, 'step1')">所务会初审</Button>
          <Button v-if="func.includes(globalAppCode + ':zfgl-zyjwzx:zdjdjc') && row.status === '02'" type="primary"
            style="margin-right: 8px" @click="handleUpdate(row, 'step2')">诊断鉴定检查</Button>
          <Button v-if="func.includes(globalAppCode + ':zfgl-zyjwzx:bwjycx') && row.status === '03'" type="primary"
            style="margin-right: 8px" @click="handleUpdate(row, 'step3')">保外就医程序</Button>
          <Button v-if="func.includes(globalAppCode + ':zfgl-zyjwzx:hsjzd') && row.status === '04'" type="primary"
            @click="handleUpdate(row, 'step4')">核实居住地</Button>
          <Button v-if="func.includes(globalAppCode + ':zfgl-zyjwzx:swhsy') && row.status === '05'" type="primary"
            @click="handleUpdate(row, 'step5')">所务会审议</Button>
          <Button v-if="func.includes(globalAppCode + ':zfgl-zyjwzx:sngs') && row.status === '06'" type="primary"
            @click="handleUpdate(row, 'step6')">所内公示</Button>
          <Button v-if="func.includes(globalAppCode + ':zfgl-zyjwzx:tzsh') && row.status === '07'" type="primary"
            @click="handleUpdate(row, 'step7')">听证审核</Button>
          <Button v-if="func.includes(globalAppCode + ':zfgl-zyjwzx:zsjcsjd') && row.status === '08'" type="primary"
            @click="handleUpdate(row, 'step8')">驻所检察室监督</Button>
          <Button v-if="func.includes(globalAppCode + ':zfgl-zyjwzx:gajggs') && row.status === '09'" type="primary"
            @click="handleUpdate(row, 'step9')">公安机关公示</Button>
          <Button v-if="func.includes(globalAppCode + ':zfgl-zyjwzx:gajgsp') && row.status === '10'" type="primary"
            @click="handleUpdate(row, 'step10')">公安机关审批</Button>
          <Button v-if="func.includes(globalAppCode + ':zfgl-zyjwzx:zfjfzx') && row.status === '11'" type="primary"
            @click="handleUpdate(row, 'step11')">罪犯交付执行</Button>
          <Button v-if="func.includes(globalAppCode + ':zfgl-zyjwzx:xq')" type="primary" style="margin-left: 8px"
            @click="toDetail(row)">详情</Button>
        </template>
      </s-DataGrid>
    </div>
  </div>
</template>

<script>
import { sDataGrid } from "sd-data-grid";
export default {
  components: {
    sDataGrid,
  },
  data() {
    return {};
  },
  methods: {
    handleAdd() {
      this.$router.replace({
        path: "/detentionBusiness/prisonerManage/execute/add",
      });
    },
    handleUpdate(row, type) {
      const step = type.split('p')[1];
      this.$router.replace({
        path: `/detentionBusiness/prisonerManage/execute/update?id=${row.id}&jgrybm=${row.jgrybm}&type=${type}&step=${step}`,
      });
    },
    toDetail(row) {
      this.$router.replace({
        path: `/detentionBusiness/prisonerManage/execute/detail?id=${row.id}&jgrybm=${row.jgrybm}&step=12`,
      });
    },
    on_refresh_table() {
      this.$refs.grid.query_grid_data(1);
    },
  },
};
</script>

<style scoped lang="less">
/deep/ .DataGrid-BOX .hale>div {
  width: 30% !important;
}

/deep/ .DataGrid-BOX .titleNav>div.navright {
  width: 70% !important;
}
</style>
