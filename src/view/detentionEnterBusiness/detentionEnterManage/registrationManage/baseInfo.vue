<template>
  <div>
    <fm :showSave="false" :ref="fmParams.tagId" :quickRegistration="quickRegistration" :parameter="fmParams.param" :fmParams="params" v-if="fmParams.load"
      :formMountId="fmParams.tagId"   />
  </div>
</template>

<script>
import { mapActions } from "vuex";
import { getUserCache, } from '@/libs/util'
import fm from "@/components/fm/component/fm.vue";

export default {
  components: { fm },
  props: {
    formData: {
      type: [Array, Object],
      default: () => ({})
    },
    rowData: {
      type: [Array, Object],
      default: () => ({})
    },
    quickRegistration: {
      type: Boolean,
      default: false,
    },
  },
  watch:{
     'rowData':{
       handler(n,o){
          this.$set(this.fmParams.param,'businessId',this.rowData && this.rowData.id?this.rowData.id:'')
       },deep:true,immediate:true
     },
  },
  data() {
    return {
      ruleValidate: {},
      orgCodeList: [],
      params:{
        test:'1212'
      },
      fmParams: {
        tagId: 'fm',
        load: true,
        param: {
          mark: serverConfig.APP_CODE+':sydj-jbxx',//'1953283412538298368',//'1899019819911614464',
          operType:this.rowData && this.rowData.id?'1':'0' ,  //0为新增 1为修改
          businessId:this.rowData && this.rowData.id?this.rowData.id:''
        }
      }
    }
  },
  methods: {
    ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
    getRecordByzjhmEvent() {
      this.$emit('getRecordByzjhm', false)
    },
    getAreaList() {
      let params = {
        // sameCase:true,
        pageNo: 1,
        pageSize: 100,
        // roomType:'7'
      }
      let orgCode = getUserCache.getOrgCode()
      params.orgCode = orgCode
      this.$store.dispatch('authPostRequest', {
        url: this.$path.app_getAreaPrisonRoomPage,
        params: params
      }).then(resp => {
        this.loading = false
        if (resp.code == 0) {
          if (resp.data && resp.data.list && resp.data.list.length > 0) {
            this.orgCodeList = resp.data.list.map(item => ({
              roomCode: item.roomCode,
              roomName: item.roomName,
              areaId: item.areaId,
              areaName: item.areaName
            }));
          }

        } else {
          this.$Notice.error({
            title: '错误提示',
            desc: resp.msg
          })
        }
      })
    },
    orgChange(data) {
      console.log(data, "orgChange");
      if (data) {
        this.formData.jsh = data
        let org = this.orgCodeList.find(t => t.roomCode == data);
        this.formData.roomName = org.roomName;
        this.formData.areaId = org.areaId;
        this.formData.areaName = org.areaName;
      }
    },
    areaChange(data) {
      console.log(data);
      if (!data) {

      }
    },
    getRecordByzjhm() {
      if (!this.formData.zjhm || !this.formData.zjhm.trim()) {
        this.$Message.error('请填写证件号码!!');
        return;
      }
      this.$store.dispatch('authGetRequest', {
        url: this.$path.app_getRecordByzjhm,
        params: {
          zjhm: this.formData.zjhm
        }
      }).then(resp => {
        if (resp.code == 0) {
          this.formData = resp.data
        } else {
          this.$Modal.error({
            title: '温馨提示',
            content: resp.msg || '操作失败'
          })
        }
      })
    },
    validate() {
      return new Promise((resolve) => {
        this.$refs.formData.validate(valid => {
          resolve(valid); // valid 是 boolean，表示是否验证通过
        });
      });
    }
  },
  mounted() {
    console.log(this.rowData,'this.rowData')
    this.getAreaList()
  }
}
</script>

<style scoped></style>
