<template>
  <div class='Inquiry-wrap'>
    <div class='Inquiry-wrap-left'>
      <!-- 使用新的人员选择组件 -->
      <personnel-selector v-model="formData.jgrybm" title="被监管人员" placeholder="点击选择在押人员或扫码识别" :show-case-info="true"
        :enable-scan="true" :show-scan-tip="true" @change="handlePersonnelChange" />
      <!-- <div class="Inquiry-flex" style="width: 100%;">
        <p class="detail-title">被监管人员</p><Button v-if="formData.jgrybm" @click='openPrison' type="primary"
          style="margin: -16px 6px 0 0;">重新选择</Button>
      </div> -->
      <!-- <div class='jgrySelect' @click='openPrison' v-if="!formData.jgrybm">
        <p class='jgrySelect-addIcon'>
          <Icon type="ios-people" color='#fff' size='36' style='position:relative;left:-10px;top:10px;' />
          <Icon style='position:relative;right:-10px;bottom:-10px;' size='36' type="md-add-circle" color='#fff' />
        </p>
        <p class='jgrySelect-text'>点击选择被监管人员</p>
      </div> -->
      <!-- <div class='jgrySelect-info' v-if="formData.jgrybm" style="width: 100%;">
        <div class='jgrySelect-flex'>
          <img :src="formData.prison.frontPhoto ? http + formData.prison.frontPhoto : defaultImg"
            style="width: 88px;height:110px;margin-right: 10px;" />
          <div>
            <p><span class='xm'>{{ formData.prison.xm ? formData.prison.xm : '-' }}</span>&nbsp;&nbsp;<span
                class='xm'>{{ formData.prison.roomName }}</span></p>
            <p><span>证件号码：</span><span>{{ formData.prison.zjhm }}</span></p>
            <p><span>出生日期：</span><span>{{ formData.prison.csrq }}</span></p>
            <p><span>籍 贯：</span><span>{{ formData.prison.jgName }}</span></p>
            <p><span>民 族：</span><span>{{ formData.prison.mzName }}</span></p>
          </div>
        </div>
        <div class='jgrySelect-flex' style='margin-top:16px;'>
          <div style='width:10px;padding:16px 26px 0 16px;background:#e6e9f2;border-radius:6px;margin-right:16px'>案件名称
          </div>
          <div>
            <p><span>涉嫌罪名：</span><span>{{ formData.prison.xszm }}</span></p>
            <p><span>案件编号：</span><span>{{ formData.prison.ajbh }}</span></p>
            <p><span>诉讼环节：</span><span>{{ formData.prison.sshjName }}</span></p>
            <p><span>入所时间：</span><span>{{ formData.prison.rssj }}</span></p>
            <p><span>关押期限：</span><span>{{ formData.prison.gyqx }}</span></p>
            <p><span>办案单位：</span><span>{{ formData.prison.basj }}</span></p>
          </div>
        </div>
      </div> -->
      <record :jgrybm="formData.jgrybm" style="width: 100%;" />
    </div>
    <div class="Inquiry-wrap-right" style="width: calc(100vw - 700px);">
      <div class='bary-flex' style='margin-top:10px;'>
        <p class="detail-title ml-16-title">律师信息</p>
        <p><Button type="primary" @click='addInvestigatorsCoop' style="cursor: pointer;"
            v-if="formData.lawyerList && formData.lawyerList.length < 4">添加律师</Button>&nbsp;&nbsp;</p>
      </div>
      <div class='bary-flex' style="margin-left: 16px;">
        <Form :ref="'coopformBox-' + index" class='form-box' :key="index + 'b'"
          v-for='(item, index) in formData.lawyerList' :model="item" :label-width="120" :label-colon="true"
          style="margin:0 16px 16px 0;">
          <Row style='padding:10px 0;background:#f5f7fa'>
            <Col span="22" style="margin-bottom:0px;text-align:center;">律师（{{ Number(index + 1) }}）</Col>
            <Col span="2">
            <Icon size="20" v-if="index > 0" @click="deleteCasePersonCoop(item, index)" type="ios-close-circle" />
            &nbsp;&nbsp;&nbsp;</Col>
          </Row>
          <Row>
            <Col span="4">
            <Col span="11" style="margin:10% auto">
            <img :src="item.zpUrl ? http + item.zpUrl : defaultImg" style="width: 96px; height: 120px;" />
            </Col>
            </Col>
            <Col span="20" style="margin-top:10px">
            <Row>
              <Col span="11">
              <FormItem label="姓名" prop="xm" :rules="[{ trigger: 'blur,change', message: '请输入姓名', required: true }]"
                style="width: 100%;">
                <div class="laywer-flex">
                  <Input search v-model="item.xm" readonly enter-button="选择" placeholder="请选择"
                    @on-search="selectLaywer(index, item)" />
                  &nbsp;<Button type="primary" @click="addLaywer">+绑定律师</Button>
                </div>
              </FormItem>
              </Col>
              <Col span="11">
              <FormItem label="性别" prop="xb" :rules="[{ trigger: 'blur,change', message: '请选择', required: true }]"
                style="width: 100%;">
                <s-dicgrid v-model="item.xb" disabled dicName="ZD_XB" />
              </FormItem>
              </Col>
              <Col span="11">
              <FormItem label="证件号码" prop="zjhm" style="width: 100%;">
                <Input v-model="item.zjhm" placeholder="" disabled maxlength="" style="width: 100%;"></Input>
              </FormItem>
              </Col>
              <Col span="11">
              <FormItem label="联系方式" prop="lxfs" style="width: 100%;">
                <Input v-model="item.lxfs" placeholder="" maxlength="" disabled style="width: 100%;"></Input>
              </FormItem>
              </Col>
              <Col span="11">
              <FormItem label="律师类型" prop="lslx" :rules="[{ trigger: 'blur', message: '必填', required: true }]"
                style="width: 100%;">
                <RadioGroup v-model="item.lslx" size="large">
                  <Radio label="0" disabled>正式律师</Radio>
                  <Radio label="1" disabled>律师助理</Radio>
                </RadioGroup>
              </FormItem>
              </Col>
              <Col span="11">
              <FormItem label="执业证号码" prop="zyzhm" style="width: 100%;">
                <Input v-model="item.zyzhm" placeholder="" maxlength="" disabled style="width: 100%;"></Input>
              </FormItem>
              </Col>
              <Col span="11">
              <FormItem label="委托阶段" prop="entrustStage" style="width: 100%;">
                <s-dicgrid v-model="item.entrustStage" disabled dicName="ZD_WTJD" />
              </FormItem>
              </Col>
              <Col span="11">
              <FormItem label="律师单位" prop="lsdw" style="width: 100%;">
                <Input v-model="item.lsdw" placeholder="" maxlength="" disabled style="width: 100%;"></Input>
              </FormItem>
              </Col>
              <Col span="11">
              <FormItem label="委托类型" prop="entrustType" style="width: 100%;">
                <s-dicgrid v-model="item.entrustType" disabled dicName="ZD_LSWT" />
              </FormItem>
              </Col>
              <Col span="11">
              <FormItem label="委托人姓名" prop="principal" style="width: 100%;">
                <Input v-model="item.principal" placeholder="" maxlength="" disabled style="width: 100%;"></Input>
              </FormItem>
              </Col>
              <Col span="11">
              <FormItem label="委托人证件号" prop="principalId" style="width: 100%;">
                <Input v-model="item.principalId" placeholder="" maxlength="" disabled style="width: 100%;"></Input>
              </FormItem>
              </Col>
              <Col span="11">
              <FormItem label="委托书类型" prop="powerOfAttorneyType" style="width: 100%;">
                <s-dicgrid v-model="item.powerOfAttorneyType" disabled dicName="ZD_WTSLX" />
              </FormItem>
              </Col>
              <Col span="11">
              <FormItem label="上传委托书/法律援助公函" prop="powerOfAttorneyUrl" style="width: 100%;">
                <file-upload disabled :defaultList="item.powerOfAttorneyUrl ? JSON.parse(item.powerOfAttorneyUrl) : []"
                  :serviceMark="serviceMark" :bucketName="bucketName" :beforeUpload="beforeUpload" />
              </FormItem>
              </Col>
              <Col span="11">
              <FormItem label="介绍信编号" prop="letterNumber" style="width: 100%;">
                <Input v-model="item.letterNumber" placeholder="" maxlength="" disabled style="width: 100%;"></Input>
              </FormItem>
              </Col>
              <Col span="11">
              <FormItem label="上传律师执业证书" prop="zyzsUrl" style="width: 100%;">
                <file-upload disabled v-if="item.zyzsUrl" :defaultList="item.zyzsUrl ? JSON.parse(item.zyzsUrl) : []"
                  :serviceMark="serviceMark" :bucketName="bucketName" :beforeUpload="beforeUpload" />
              </FormItem>
              </Col>
            </Row>
            </Col>
          </Row>
        </Form>
      </div>
      <div class="fm-content-wrap" style="padding: 0;">
        <div class="fm-content-form">
          <Form ref="taskForm" :model="formData" :label-width="170" :label-colon="true">
            <p class="fm-content-wrap-title"
              style='margin-bottom: 16px;padding:10px; border-bottom: 1px solid #cee0f0; background: #eff6ff;'>
              <Icon type="md-list-box" size="24" color="#2b5fda" />业务信息
            </p>
            <Row>
              <Col span="11">
              <FormItem label="会见方式" prop="meetingMethod" style="width: 100%;">
                <RadioGroup v-model="formData.meetingMethod" size="large">
                  <Radio label="0">现场会见</Radio>
                  <Radio label="1">快速会见</Radio>
                  <Radio label="2">远程会见</Radio>
                </RadioGroup>
              </FormItem>
              </Col>
              <Col span="11">
              <FormItem label="预约会见时间" prop="arraignmentTime"
                :rules="[{ trigger: 'change', message: '请选择会见时间', required: true }]" style="width: 100%;">
                <div style="position: relative;" class="laywer-flex">
                  <el-date-picker v-if="formData.meetingMethod == '0'" size='small' @change="selectDate" key="pick01"
                    v-model="formData.appointmentTime" format='yyyy-MM-dd' value-format='yyyy-MM-dd' type="date"
                    placeholder="请选择" />
                  <Select v-model="formData.appointmentTimeSlot" style="width:200px"
                    v-if="formData.meetingMethod == '0'">
                    <Option :value="item.time" v-for="(item, index) in applyMeetingTimeSlotList" :key="index + 'meet'"
                      :disabled="!item.select">{{ item.timeTag }}</Option>
                  </Select>
                  <el-date-picker v-if="formData.meetingMethod == '1'" size='small' @change="selectDateFirst"
                    key="pick02" v-model="formData.applyMeetingStartTime" format='yyyy-MM-dd HH:mm:ss'
                    value-format='yyyy-MM-dd HH:mm:ss' type="datetime" placeholder="请选择会见开始时间" style="width: 100%;" />
                  <el-date-picker format='yyyy-MM-dd HH:mm:ss' value-format='yyyy-MM-dd HH:mm:ss'
                    v-if="formData.meetingMethod == '2'" v-model="formData.arraignmentTime" @change="selectDateSecond"
                    key="pick03" type="datetimerange" size='small' range-separator="-" style='width:100%'
                    start-placeholder="开始日期" end-placeholder="结束日期">
                  </el-date-picker>
                </div>
              </FormItem>
              </Col>
              <Col span="11">
              <FormItem label="会见批准机关" prop="approvalAuthority" style="width: 100%;">
                <Input v-model="formData.approvalAuthority" placeholder="" maxlength="" style="width: 100%;"></Input>
              </FormItem>
              </Col>
              <Col span="11">
              <FormItem label="许可决定文书号" prop="approvalDocumentNumber" style="width: 100%;">
                <Input v-model="formData.approvalDocumentNumber" placeholder="" maxlength=""
                  style="width: 100%;"></Input>
              </FormItem>
              </Col>
              <Col span="11">
              <FormItem label="法律援助公函号" prop="legalAidOfficialLetterNumber" style="width: 100%;">
                <Input v-model="formData.legalAidOfficialLetterNumber" placeholder="" maxlength=""
                  style="width: 100%;"></Input>
              </FormItem>
              </Col>
              <Col span="11">
              <FormItem label="备注" prop="remarks" style="width: 100%;">
                <Input v-model="formData.remarks" placeholder="" maxlength="" style="width: 100%;"></Input>
              </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="16">
              <FormItem label="上传会见批准材料附件" prop="approvalAttachmentPath" style="width: 100%;">
                <file-upload :defaultList="meetingDocumentsUrl" :serviceMark="serviceMark" :bucketName="bucketName"
                  :beforeUpload="beforeUpload" @fileSuccess="fileSuccessFile" v-if="showFile"
                  @fileRemove="fileRemoveFile" @fileComplete="fileCompleteFileCertUrl" />
              </FormItem>
              </Col>
            </Row>
          </Form>
        </div>
      </div>
      <!-- 同行人 -->
      <companions :formData="formData" ref="companions" />
      <br />
      <!-- 被监管人员选择组件 -->
      <Modal v-model="openModal" :mask-closable="false" :closable="true" class-name="select-use-modal" width="1360"
        title="人员列表">
        <div class="select-use">
          <prisonSelect v-if="openModal" ref="prisonSelect" ryzt="ZS" :isMultiple='false'
            :selectUseIds="formData.jgrybm" />
        </div>
        <div slot="footer">
          <Button type="primary" @click="useSelect" class="save">确 定</Button>
          <Button @click="openModal = false" class="save">关 闭</Button>
        </div>
      </Modal>
    </div>
    <div class='bsp-base-fotter'>
      <Button @click='goBack'>返 回</Button>
      <Button @click="saveData" type="primary" :loading='loading'>保 存</Button>
    </div>

    <camera-modal ref="cameraModal" @takePhoto="takePhotoItem"></camera-modal>
    <laywerModal :laywerArr="laywerArr" ref="laywerModal" :formData="formData" :openLawer="openLawer" @close="close" />
  </div>
</template>

<script>
import { sDataGrid } from 'sd-data-grid'
import { mapActions } from 'vuex'
import { sImageUploadLocal } from '@/components/upload/image'
import { fileUpload } from 'sd-minio-upfile'
import { orgSelector } from 'sd-org-selector'
import { prisonSelect } from "sd-prison-select"
import { sDialog } from 'sd-custom-dialog'
import cameraModal from './camera-modal.vue'
import record from './record.vue'
import companions from './companions.vue'
import laywerModal from "./laywerModal.vue"
import { normalizeObject, formatDateparseTime } from '@/libs/util'
import personnelSelector from '@/components/personnel-selector/index.vue'

export default {
  components: {
    sDataGrid, sImageUploadLocal, fileUpload, orgSelector, prisonSelect, sDialog, cameraModal, record, companions, laywerModal, personnelSelector
  },
  data() {
    return {
      laywerArr: [],
      defaultImg: require('@/assets/images/main.png'),
      http: serverConfig.severHttp,
      selectUseIds: '',
      openModal: false,
      showFile: true,
      showImg: false,
      serviceMark: serverConfig.OSS_SERVICE_MARK,
      bucketName: serverConfig.bucketName,
      importUrl: this.$path.upload_fj,
      custom_loading: false,
      defaultList: [],
      fileList: [],
      meetingDocumentsUrl: [],
      evidenceUrl: [],
      buttenloading: false,
      stream: null,
      uploadForm: {},
      params: {},
      showData: false,
      modalTitle: '新增办案人员',
      pzData: [],
      openModal: false,
      formData: {
        isdisabled: '5',
        dataSources: 0,
        meetingMethod: '0',
        prison: {},
        companionList: [],
        lawyerList: [
          { isdisabled: '5', lslx: '0' },
        ],
      },
      loading: false,
      saveType: 'add',
      loadingStates: [],
      curBaryData: {},
      applyMeetingTimeSlotList: [],
      openLawer: false,
      curIndex: 0
    }
  },
  watch: {},
  methods: {
    ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
    // 处理人员选择变化
    handlePersonnelChange(personnelData, jgrybm) {
      if (personnelData && jgrybm) {
        this.formData.prison = personnelData
        this.formData.jgrybm = jgrybm
        // 自动获取该监管人委托律师信息
        this.getPersonList()
      } else {
        this.formData.prison = {}
        this.formData.jgrybm = ''
        // 清空办案人员信息
        // this.clearCasePersonnelInfo()
      }
    },
    deleteCasePersonCoop(item, index) {
      this.formData.lawyerList.splice(index, 1)
    },
    selectLaywer(index, item) {
      if (!this.formData.jgrybm) {
        this.$Message.error('请选择被监管人员！')
        return
      }
      this.curIndex = index
      console.log(index, item, '(index,item)')
      this.openLawer = true
    },
    close(tag, data) {
      console.log(data, 'datadatadatadata')
      if (data) {
        this.sameCaseJudgment(data.id, data)
      } else {
        this.openLawer = false
      }

    },
    // 同案验证
    sameCaseJudgment(lawyerIds, data) {
      this.$store.dispatch('getRequest', {
        url: this.$path.acp_lawyerMeeting_sameCaseJudgment,
        params: {
          jgrybm: this.formData.jgrybm,
          lawyerIds: lawyerIds
        }
      }).then(res => {
        if (res.success) {
          if (res.data && res.data.proxy) {
            this.$Message.error(res.proxyMsg || '所选律师已代理当前被监管人员的同案人员，为避免利益冲突，根据相关规定，暂无法选择该律师。')
          } else if (res.data && res.data.prompt) {
            this.$Modal.confirm({
              title: res.data.promptMsg,
              onOk: async () => {
                this.openLawer = false
                this.formData.lawyerList[this.curIndex] = data
              },
              onCancel: async () => {
                this.openLawer = false
              },
            });
          } else {
            this.openLawer = false
            this.formData.lawyerList[this.curIndex] = data
          }
        }
      })

    },
    takePhoto(row, index) {
      this.curBaryData = row
      this.curBaryData.index = index
      this.$refs.cameraModal.open({});
    },
    takePhotoItem(imgUrl) {
      this.$set(this.curBaryData, 'imageUrl', imgUrl)
      this.$set(this.formData.casePersonnelList[this.curBaryData.index], 'imageUrl', imgUrl)
    },
    selectDate(data) {
      console.log(data, 'selectDate')
      this.getHuNumber(data)
    },
    getHuNumber(data) {
      this.$store.dispatch('getRequest', {
        url: this.$path.acp_lawyerMeeting_getMeetingConfig,
        params: {
          applData: data,
        }
      }).then(res => {
        if (res.success) {
          if (res.data && res.data.applyMeetingTimeSlotList.length > 0) {
            this.applyMeetingTimeSlotList = res.data.applyMeetingTimeSlotList

          }
        }
      })
    },
    goBack() {
      this.$emit('toback')
    },
    addLaywer() {
      this.$router.push({
        path: '/lawyer/lawyerManagement',
        query: {
          type: 'add',
          jgrybm: this.formData.jgrybm
        }
      })
    },
    saveData() {
      let arr = [this.$refs.taskForm.validate()]
      if (this.formData.lawyerList && this.formData.lawyerList.length > 0) {
        this.formData.lawyerList.forEach((item, index) => {
          console.log(this.$refs['coopformBox-' + index], 'formBox')
          arr.push(this.$refs['coopformBox-' + index][0].validate())
        })
      }
      console.log(arr, 'arr')
      if (!this.formData.jgrybm) {
        this.$Message.error('请选择被监管人员！')
        return
      }
      if (!this.formData.appointmentTime || !this.formData.appointmentTimeSlot) {
        this.$Message.error('请选择预约会见时间！');
        return;
      }
      if (this.$refs.companions.dataTable && this.$refs.companions.dataTable.length > 0) {
        this.$set(this.formData, 'companionList', this.$refs.companions.dataTable)
      }
      Promise.all(arr).then(data => {
        this.loading = true
        this.saveDataForm()
      })
    },
    selectDateSecond(data) {
      if (this.formData.arraignmentTime && this.formData.arraignmentTime.length > 0) {
        this.$set(this.formData, 'applyMeetingStartTime', this.formData.arraignmentTime[0])
        this.$set(this.formData, 'applyMeetingEndTime', this.formData.arraignmentTime[1])
      }
    },
    selectDateFirst(data) {
      if (this.formData.applyMeetingStartTime) {
        let now = new Date(this.formData.applyMeetingStartTime).getTime() + 300000;
        console.log(formatDateparseTime(now), 'applyMeetingEndTime')
        this.$set(this.formData, 'applyMeetingEndTime', formatDateparseTime(now))
      }
    },
    saveDataForm() {
      this.$store.dispatch('authPostRequest', { url: this.$path.acp_lawyerMeeting_create, params: this.formData }).then(resp => {
        if (resp.success) {
          this.loading = false
          this.goBack()
          this.$Message.success('律师会见登记成功')
        } else {
          this.loading = false
          this.$Message.error(resp.msg || '律师会见登记失败')
        }
      }).catch(err => {
        this.loading = false
      })
    },
    openPrison() {
      this.openModal = true
    },
    addInvestigators() {
      console.log(this.formData, 'this.formData.')
      let obj = {
        isdisabled: '5'
      }
      this.formData.casePersonnelList.push(obj)
    },
    addInvestigatorsCoop() {
      console.log(this.formData, 'this.formData.')
      let obj = {
        isdisabled: '5'
      }
      this.formData.lawyerList.push(obj)
    },
    useSelect() {
      if (this.$refs.prisonSelect.checkedUse && this.$refs.prisonSelect.checkedUse.length > 0) {
        this.formData.prison = normalizeObject(this.$refs.prisonSelect.checkedUse[0])
        this.formData.jgrybm = this.$refs.prisonSelect.checkedUse[0].jgrybm
        this.openModal = false
        this.getPersonList()
      } else {
        this.$Notice.warning({
          title: '提示',
          desc: '请选择人员!'
        })
      }
    },
    getPersonList() {
      this.$store.dispatch('authPostRequest', {
        url: this.$path.acp_lawyer_getLawyerPageByJgrybm,
        params: {
          jgrybm: this.formData.jgrybm,
          pageNo: 1,
          pageSize: 100
        }
      }).then(res => {
        if (res.success) {
          this.laywerArr = res.data && res.data.list ? res.data.list : []
        }
      })
    },
    // 上传组件回调方法
    handleSuccessImg(res, index) {
      console.log(res, '1212')
      this.defaultList.push(res.data);
      this.defaultList.forEach(ele => {
        ele.src = ele.url
        ele.imgSrc = ele.url
      })
    },
    removeItem(e, index) {
      this.$Modal.confirm({
        title: "提示",
        render: (h, params) => {
          return h("div", [
            h(
              "p",
              { style: { marginLeft: "10px", wordBreak: "break-all" } },
              "是否确认删除【" + e.item.filename + "】?"
            ),
          ]);
        },
        loading: true,
        onOk: async () => {
          this.$Modal.remove();
          this.defaultList.splice(e.index, 1);
          this.imgArr.forEach((ele, i) => {
            if (ele.url == e.url) {
              this.defaultList.splice(i, 1);
            }
          })
        },
      });
    },
    onSelectXm(data, index, item, fileName) {
      console.log(data, index, '1212')
      data.forEach(ele => {
        ele.zp_url ? this.$set(ele, 'zpUrl', ele.zp_url) : ''
      })
      this.$set(this.formData[fileName], [index], data[0])
      //  this.$set(this.formData[fileName][index],'isdisabled','5')
      //  this.formData[fileName][index].defaultList=[{ url: data[0].imageUrl, name: '' }]
      //  this.$forceUpdate()
    },
    onClearXm(data, index, item, fileName) {
      this.$set(this.formData[fileName], [index], {})
      this.$set(this.formData[fileName][index], 'isdisabled', '5')

    },
    onClear(data) {
      console.info(data, this.formData, 'onClear')
      //    this.$set(this.formData,'badwmc','')
    },
    beforeUpload() { },
    fileSuccessFile() { },
    fileRemoveFile() { },
    fileCompleteFileCertUrl(data) {
      this.meetingDocumentsUrl = data
      this.$set(this.formData, 'approvalAttachmentPath', JSON.stringify(data))
    },
    removeFile(file) {
      if (file.length == 0) {
        this.$set(this.formData, 'imageUrl', '')
        this.defaultList = []
      }
    },
    remove() {
      this.$set(this.formData, 'imageUrl', '')
      this.defaultList = []
    },
    async uploadImg(imgSrc) {
      let blob = this.dataURLtoBlob(imgSrc);
      let file = this.blobToFile(blob, "拍照");
      let formData = new FormData();
      formData.append("file", file);
      let res = await this.$store.dispatch('authPostRequest', { url: this.importUrl, params: formData })
      let { status, data } = res;
      if (status === 200) {
        this.formData.imageUrl = data.url;
      } else {
        this.errorModal({ content: "上传失败!" })
      }
    },
    dataURLtoBlob(dataurl) {
      let arr = dataurl.split(",");
      let type = arr[0].match(/:(.*?);/)[1];
      let bstr = atob(arr[1]);
      let n = bstr.length;
      let u8str = new Uint8Array(n);
      while (n--) {
        u8str[n] = bstr.charCodeAt(n);
      }
      return new Blob([u8str], { type });
    },
    blobToFile(blob, fileName) {
      blob.lastModifiedDate = new Date();
      blob.name = fileName;
      return blob;
    },
    getfile(file, index, fileName, fileUrl) {
      console.log(file, 'getfile', this.formData[fileName][index][fileUrl])
      this.$set(this.formData[fileName][index], fileUrl, file.url)
      this.formData[fileName][index].defaultList = [file]
      console.log(this.formData, 'this.formData')
    },
    getRadio(value) {
      console.log(value, 'getRadio')
      console.log(this.formData.isdisabled, this.defaultList, 'this.formData.isdisabled')
      if (this.defaultList && this.defaultList.length == 0) {
        this.$set(this.formData, 'imageUrl', '')
      }
      if (value) {
        // 获取摄像头视频流
        if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
          navigator.mediaDevices.getUserMedia({ video: true }).then(stream => {
            this.stream = stream
            // this.$refs.videoElement= this.stream;
            const video = document.getElementById('video');
            video.srcObject = this.stream;
            video.play();
          });
        }
      } else {
        this.defaultList = []
        let faceImageUrl = this.formData.imageUrl
        if (faceImageUrl) {
          let urlObj = { url: faceImageUrl, name: '' }
          this.defaultList.push(urlObj)
        }
        this.stopCamera()
      }
    },
    stopCamera() {
      // 关闭摄像头
      if (this.stream) {
        const tracks = this.stream.getTracks();
        tracks.forEach(track => track.stop());
        this.$refs.videoElement.srcObject = null;
      }
    },
    capture() {
      this.buttenloading = true;
      const video = document.getElementById('video');
      const canvas = document.getElementById('canvas');
      const context = canvas.getContext('2d');
      context.drawImage(video, 0, 0, 100, 100);
      // 如果有自定义文件名称，优先使用自定义的文件名称
      let filenames = ''
      if (this.uploadForm.filename != undefined && this.uploadForm.filename != '') {
        filenames = this.uploadForm.filename + '.png'
      } else {
        filenames = this.uploadForm.code + '_' + this.picnum + '.png'
      }
      const base64 = canvas.toDataURL('image/png');//canvas.toDataURL();
      console.log(filenames, 'filenames', base64);
      this.uploadImg(base64);
      this.$set(this.formData, 'imageUrl', base64)
      this.defaultList = []
      let faceImageUrl = this.formData.imageUrl
      if (faceImageUrl) {
        let urlObj = { url: faceImageUrl, name: '' }
        this.defaultList.push(urlObj)
      }
      this.buttenloading = false;
    },
    addEvent(row) {
      this.showFile = true
      this.showImg = true
      this.saveType = 'add'
      this.formData = { isdisabled: 0, imageUrl: '' },
        this.fileList = []
      this.openModal = true
      this.modalTitle = '新增办案人员'
      let faceImageUrl = this.formData.imageUrl
      if (faceImageUrl) {
        let urlObj = { url: faceImageUrl, name: '' }
        this.defaultList.push(urlObj)
      }
    },
    editEvent(index, row, tag) {
      if (tag == 'edit') {
        this.modalTitle = '编辑办案人员'
        this.saveType = 'edit'
      } else {
        this.modalTitle = '查看办案人员'
        this.saveType = 'info'
      }

      this.openModal = false
      console.log(row);
      this.$store.dispatch("authGetRequest", {
        url: this.$path.acp_casePersonnel_get,
        params: {
          id: row.id
        }
      }).then(res => {
        console.log(res, '编辑');
        if (res.success && res.data) {
          this.$set(this.loadingStates, index, false);
          this.formData = res.data
          this.defaultList = []
          this.fileList = []
          if (this.formData.gzzjUrl) {
            this.$set(this, 'fileList', JSON.parse(this.formData.gzzjUrl))
          } else {
            this.fileList = []
          }
          console.log(this.fileList, 'fileList', this.formData.imageUrl)
          this.showFile = true
          // this.formData.isdisabled=0
          if (this.formData.imageUrl) {
            let urlObj = { url: this.formData.imageUrl, name: '' }
            //  this.formData.isdisabled=0
            this.defaultList.push(urlObj)
          }
          this.showImg = true

          this.openModal = true
        } else {
          this.$set(this.loadingStates, index, false);
          this.$Modal.error({
            title: '温馨提示',
            content: res.msg || '操作失败'
          })
          this.showFile = true
          this.showImg = true


        }
      })
    },
    deleleChange(row) {
      this.$Modal.confirm({
        title: '温馨提示',
        content: '请确认是否删除？',
        onOk: () => {
          this.$store.dispatch('authGetRequest', {
            url: this.$path.acp_casePersonnel_delete,
            params: {
              ids: row.id
            }
          }).then(res => {
            console.log(res);
            if (res.success) {
              this.on_refresh_table()
            }
          })
        }
      })
    },
    onCancel() {
      this.showFile = false
      this.showImg = false
      this.openModal = false
      this.stopCamera()
    },
    submitClick() {
      this.$refs['releaseForm'].validate((valid) => {
        if (valid) {
          this.loading = true
          this.saveForm()
        } else {
          this.loading = false
          this.$Message.error('请填写完整!!')
        }
      })
    },
    saveForm() {
      console.log(this.formData, 'this.formData');
      if (this.fileList && this.fileList.length > 0) {
        this.$set(this.formData, 'gzzjUrl', JSON.stringify(this.fileList))
      } else {
        this.$set(this.formData, 'gzzjUrl', '')
      }
      // return
      let url = ''
      if (this.formData.id) {
        url = this.$path.acp_casePersonnel_update
      } else {
        url = this.$path.acp_casePersonnel_create
      }
      this.$store
        .dispatch("authPostRequest", {
          url: url,
          params: this.formData
        }).then(res => {
          console.log(res, 'res');
          if (res.success) {
            this.loading = false
            this.openModal = false
            this.on_refresh_table()
          } else {
            this.$Message.error(res.msg || '保存失败！')
            this.loading = false
          }
        })
    },
    changeStatus(row, val) {
      console.log(row, val);
      this.$store.dispatch('postRequest', {
        url: this.$path.bsp_pam_reportItem_updateStatus,
        params: {
          id: row.id,
          status: row.status,
        }
      }).then(res => {
        if (res.success) {
          this.$Message.success('修改成功!!')

          //   this.on_refresh_table()
        }
      })
    },
    handleOrgIChange(newVal, oldVal) {
      //   console.log(newVal, oldVal,'newVal, oldVal');
      if (!newVal) {
        this.params.orgCode = this.$store.state.common.orgCode
        this.showData = false
      } else {
        this.showData = false
        this.params.orgCode = newVal
      }
    },
    on_refresh_table() {
      this.$refs.grid.query_grid_data(1);
    },
  },
  mounted() {
  }
}
</script>

<style scoped lang="less">
.ivu-form-item {
  margin-bottom: 18px !important;
}
</style>
<style>
.bsp_org_sel_box .ivu-modal {
  width: 930px !important;
}
</style>
<style lang='less'>
@import url("~@/view/windowBusiness/Inquiry/Inquirylist/index.less");
</style>
<style lang='less' scoped>
.laywer-flex {
  display: flex;
  align-items: center;
}

.jgrySelect-info {
  border: 1px solid #dcdee2;
  padding: 16px 0 16px 16px;
  margin: 16px 16px 0 0;
  border-radius: 6px;

  .xm {
    font-size: 18px;
    font-weight: 700;
  }
}

.jgrySelect {
  border: 1px solid #dcdee2;
  height: 180px;
  border-radius: 6px;
  background: #f7fbfd;
  margin-right: 16px;
  text-align: center;
  display: flex;
  cursor: pointer;
  flex-wrap: wrap;
  align-items: self-start;

  .jgrySelect-addIcon {
    width: 110px;
    height: 60px;
    background: #50a6f9;
    border-radius: 6px;
    margin: auto;
  }

  .jgrySelect-text {
    width: 100%;
    margin-top: -20px;
    color: #2d8cf0;
  }
}

.jgrySelect-flex {
  display: flex;
}

.bary-flex {
  display: flex;
  // align-items:center;
  justify-content: space-between;
  z-index: 9999;
  flex-wrap: wrap;
}

.bsp-imgminio-container {
  width: 100% !important;
}

.bary-flex .bsp-imgminio-container .upload-list-container .upload-list-content .content-filecontent .filecontent-top .top-success .text-hidden {
  max-width: 75px !important;
}

.bary-flex .bsp-imgminio-container .upload-list-container .upload-list-content .filecontent-top /deep/ p>span:last-of-type {
  display: none !important;
}

.Inquiry-flex {
  width: 100%;
  display: flex;
  align-content: center;
  align-items: center;
  justify-content: space-between;
}
</style>
