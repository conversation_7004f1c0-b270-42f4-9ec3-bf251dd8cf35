<template>
  <!-- 律师弹窗 -->
  <Modal
    v-model="openModal"
    :mask-closable="false"
    :closable="false"
    class-name="select-laywer-modal"
    width="40%"
    :title="modalTitle"
  >
    <div>
      <Row>
        <Col span="24" style="padding-right: 0; border: none">
          <Table :columns="columns" :data="dataTable" border @on-selection-change="onSelectionChange">
            <!-- <template slot-scope="{ row, index }" slot="checked">
               <Icon type="ios-radio-button-on" :size="20" :color="checked == row.id ? '#2d8cf0' : '',"
                @click="selectSatus(row, index)" />
            </template> -->
          </Table>
        </Col>
      </Row>
      <br />
    </div>
    <div class="bsp-modal-submit" slot="footer">
      <Button @click="cancal">取消</Button>
      <Button type="primary" @click="submit">确定</Button>
    </div>
  </Modal>
</template>

<script>
export default {
  props: {
    laywerArr: Array,
    openLawer: Boolean,
    formData:Object
  },
  watch: {
    laywerArr: {
      handler(n, o) {
        console.log(n, "laywerArr");
        if (n && n.length > 0) {
          this.dataTable = n;
        }
      },
      deep: true,
      immediate: true,
    },
    openLawer: {
      handler(n, o) {
        console.log(n, o, this.laywerArr, "n,on,on,o");
        if(n){
          this.openModal = n;
        }
      },
      deep: true,
      immediate: true,
    },
    formData: {
      handler(n, o) {
          if(n.lawyerList && n.lawyerList.length>0){
            this.$set(this,'selectLaywerArr',n.lawyerList)
          }
      },
      deep: true,
      immediate: true,
    },
  },
  data() {
    return {
      selectLaywerArr:[],
      checked: "",
      openModal: false,
      modalTitle: "律师",
      selectLaywer: {},
      dataTable: [],
      columns: [
        {
          type: "index",
          width: 80,
          align: "center",
          title: "序号",
        },
        // {
        //   slot: "checked",
        //   width: 80,
        //   align: "center",
        //   title: "-",
        // },
        {
          slot: "selection",
          type: 'selection',
          width: 80,
          align: 'center'
        },
        {
          title: "姓名",
          key: "xm",
          align: "center",
        },
        {
          title: "性别",
          key: "xbName",
          align: "center",
          width: 80,
        },
        {
          title: "律师类型",
          key: "lslxName",
          align: "center",
        },
        {
          title: "律师单位:",
          key: "lsdw",
          align: "center",
        },
      ],
    };
  },
  methods: {
    cancal(data,tag) {
        if(tag){
          this.selectTip(data)
        }else{
          this.openModal = false;
          this.$emit("close", false);
        }

    },
    selectTip(data){
        if(this.selectLaywerArr && this.selectLaywerArr.length>0){
            const isExist =this.selectLaywerArr.some(item => item.id === data.id);
              if (!isExist) {
                 this.openModal = false;
                 this.$emit("close", false, data);
              }else{
                this.$Message.error('该律师已选，请重新选择！')
                return
              }
          }
    },
    submit() {
      if (this.checked) {
        this.cancal(this.selectLaywer,true);
      } else {
        this.$Message.error("请选择！");
      }
    },
    selectSatus(row, index) {
      this.checked = row.id;
      this.selectLaywer = row;
      console.log(row, index);
    },
    onSelectionChange(selection) {
      console.log(selection,'selection');
      
      if (selection.length > 0) {
        this.checked = selection[selection.length - 1].id;
        this.selectLaywer = selection[selection.length - 1];
        console.log(this.selectLaywer);
        
      } else {
        this.checked = '';
        this.selectLaywer = null;
      }
    }
  },
};
</script>

<style lang="less" scoped>
.LaywerBOrder {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  border: 1px solid #a59e9e;
  display: inline-block;
  cursor: pointer;
}
.activeLaywer {
  border: 1px solid #2d8cf0 !important;
  background: #2d8cf0 !important;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  display: inline-block;
  cursor: pointer;
}
</style>