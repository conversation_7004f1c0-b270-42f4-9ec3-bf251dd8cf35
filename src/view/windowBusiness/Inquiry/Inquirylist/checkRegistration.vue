<template>
    <!-- 带出安检登记 -->
    <div>
       	<Form ref="checkForm" :model="formData" :label-width="130" :label-colon="true" style="margin-right: 16px;" >
             <Row>
                <Col span="12">
                    <FormItem label="带出民警" prop="escortingPolice" :rules="[{ trigger: 'blur,change', message: '请选择带出民警', required: true }]" style="width: 100%;">
                         <user-selector style="width: 500px" v-model="formData.escortingPoliceSfzh" tit="带出民警选择"
                            :text.sync="formData.escortingPolice" returnField="idCard" numExp='num>=1' msg="至少选中1人">
                            </user-selector>
                    </FormItem>
                </Col>
                <Col span="12">
                    <FormItem label="带出监室时间" prop="escortingTime" :rules="[{ trigger: 'blur,change', message: '请选择带出监室时间', required: true }]" style="width: 100%;">
                       <el-date-picker  format='yyyy-MM-dd HH:mm:ss'  value-format='yyyy-MM-dd HH:mm:ss'
                            v-model="formData.escortingTime" style="width: 100%;"
                            type="datetime" size='small'
                            placeholder="选择日期时间">
                            </el-date-picker>
                    </FormItem>
                </Col>
                <Col span="12">
                    <FormItem label="检查时间" prop="inspectionTime" :rules="[{ trigger: 'blur,change', message: '请选择检查时间', required: true }]" style="width: 100%;">
                         <el-date-picker  format='yyyy-MM-dd HH:mm:ss'  value-format='yyyy-MM-dd HH:mm:ss'
                            v-model="formData.inspectionTime" style="width: 100%;"
                            type="datetime" size='small'
                            placeholder="选择日期时间">
                            </el-date-picker>
                    </FormItem>
                </Col>
                <Col span="12">
                    <FormItem label="检查人" prop="inspector" :rules="[{ trigger: 'blur,change', message: '请选择检查人', required: true }]" style="width: 100%;">
                        <user-selector style="width: 500px" v-model="formData.inspectorSfzh" tit="检查人选择"
                            :text.sync="formData.inspector" returnField="idCard" numExp='num==1' msg="至少选中1人">
                            </user-selector>
                    </FormItem>
                </Col>
                <Col span="12">
                    <FormItem label="带出安检结果" prop="inspectionResult" :rules="[{ trigger: 'blur,change', message: '请选择带出安检结果', required: true }]" style="width: 100%;">
                       <RadioGroup v-model="formData.inspectionResult" >
                            <Radio label="0" >正常</Radio>
                            <Radio label="1">异常</Radio>
                        </RadioGroup>
                    </FormItem>
                </Col>
                <Col span="12">
                    <FormItem label="提讯开始时间" :rules="[{ trigger: 'blur,change', message: '请选择提讯开始时间', required: true }]" prop="arraignmentStartTime"  style="width: 100%;">
                        <el-date-picker  format='yyyy-MM-dd HH:mm:ss'  value-format='yyyy-MM-dd HH:mm:ss'
                            v-model="formData.arraignmentStartTime" style="width: 100%;"
                            type="datetime" size='small'
                            placeholder="选择日期时间">
                            </el-date-picker>
                    </FormItem>
                </Col>
                 <Col span="24" v-if="formData.inspectionResult=='1'">
                    <FormItem label="违禁物品登记" prop="prohibitedItems"  style="width: 100%;">
                        <Input v-model="formData.prohibitedItems" type="textarea" placeholder="请填写" maxlength="" style="width: 100%;"></Input>
                    </FormItem>
                </Col>
                 <Col span="24" v-if="formData.inspectionResult=='1'">
                    <FormItem label="体表检查登记" prop="physicalExam"  style="width: 100%;">
                        <Input v-model="formData.physicalExam" type="textarea" placeholder="请填写" maxlength="" style="width: 100%;"></Input>
                    </FormItem>
                </Col>
                 <Col span="24" v-if="formData.inspectionResult=='1'">
                    <FormItem label="异常情况登记" prop="abnormalSituations"  style="width: 100%;">
                        <Input v-model="formData.abnormalSituations" type="textarea" placeholder="请填写" maxlength="" style="width: 100%;"></Input>
                    </FormItem>
                </Col>

             </Row>
        </Form>
    </div>
</template>

<script>
import { getUuid,removeNullFields,getUserCache,formatDateparseTime } from "@/libs/util.js";
import {userSelector} from 'sd-user-selector'
export default {
   components:{userSelector},
    props:{
        curId:String,
        noReq:{
            type:Boolean,
            default:false,
        }
    },
   data(){
    return{
        formData:{
            inspectionResult:'0'
        }
    }
   },
   methods:{
    	submitClick(){
		  this.$refs['checkForm'].validate((valid) => {
			  if(valid) {
				  if(this.formData.inspectionResult && this.formData.inspectionResult=='1'){
                      if(this.formData.prohibitedItems || this.formData.physicalExam || this.formData.abnormalSituations){
                        console.log(121212)
                        if(this.noReq){
                          this.$emit('validate',this.formData,true)
                        }else{
                          this.saveForm()
                        }
                      }else{
                        this.$emit('validate',this.formData,false)
                        this.$Message.error('违禁物品登记、体表检查登记、异常情况登记请选择任一填写完整!!')
                      }
                  }else{
                    if(this.noReq){
                          this.$emit('validate',this.formData,true)
                        }else{
                          this.saveForm()
                        }
                  }
			  } else {
				  this.$emit('validate',this.formData,false)
				  this.$Message.error('请填写完整!!')
			  }
		  })
	    },
        saveForm(){ 
                    let params={id: this.curId}
                            Object.assign(params,this.formData)
                            console.log(params,'params')
                            this.$store.dispatch('authPostRequest', {url: this.$path.acp_arraignment_escortingInspect, params: params}).then(resp => {
                            if (resp.success) {
                            this.$emit('escortingInspect',true)
                            this.$Message.success('带出安检登记成功')
                            } else {
                            this.$emit('escortingInspect',false)
                            this.$Message.error(resp.msg||'带出安检登记失败')
                            }
                        })
        }
   },
   mounted(){
          if(!this.formData.escortingTime){
            this.$set(this.formData,'escortingTime',formatDateparseTime(new Date()))
          }
          if(!this.formData.arraignmentStartTime){
            this.$set(this.formData,'arraignmentStartTime',formatDateparseTime(new Date()))
          }
          if(!this.formData.inspectionTime){
            this.$set(this.formData,'inspectionTime',formatDateparseTime(new Date()))
          }
          if(!this.formData.inspector){
            this.$set(this.formData,'inspector',getUserCache.getUserName())
            this.$set(this.formData,'inspectorSfzh',getUserCache.getIdCard())
          }
          if(!this.formData.escortingPolice){
            this.$set(this.formData,'escortingPolice',getUserCache.getUserName())
            this.$set(this.formData,'escortingPoliceSfzh',getUserCache.getIdCard())
          }
   }
}
</script>

<style>
.ivu-modal-body{
    font-family: MicrosoftYaHei, MicrosoftYaHei;
    font-weight: normal;
}
</style>