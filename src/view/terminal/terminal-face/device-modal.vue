<template>
  <Modal :width="920" v-model="visible" title="下发照片">
    <div class="bsp-modal-container" v-if="visible">
      <bsp-condition-box v-model="deviceParam" @on-submit="onSearch" @on-reset="onSearch">
        <FormItem label="设备名称：" prop="deviceName">
          <Input v-model="deviceParam.deviceName" />
        </FormItem>
        <FormItem label="设备类型：" prop="deviceType">
          <Select v-model="deviceParam.deviceType">
            <Option :value="item.value" v-for="(item, index) in deviceTypeLists" :key="index">{{ item.label }}</Option>
          </Select>
        </FormItem>
      </bsp-condition-box>
      <div class="card-box" v-if="deviceLists.length > 0">
        <Checkbox class="othr" @on-change="selectAllChange" v-model="selectAll">全选</Checkbox>
        <div class="card-content-box">
          <bsp-screen-card @on-select="selectCard(item)" class="pointer" :active="item.checked" show-checkbox
            v-for="(item, idx) in deviceLists" :type="item.deviceType + ''" :key="idx"
            :device-name="item.deviceName"></bsp-screen-card>
        </div>
      </div>
      <bsp-empty v-else height="300px"></bsp-empty>
    </div>
    <div class="bsp-modal-submit" slot="footer">
      <Button @click="close">取消</Button>
      <Button type="primary" :disabled="serialNumbers.size === 0" @click="submit">提交</Button>
    </div>
  </Modal>
</template>

<script>
import bspScreenCard from "../../../components/bsp-card/bsp-screen-card";
import bspConditionBox from "../../../components/bsp-condition";
import bspEmpty from "../../../components/bsp-empty"
export default {
  name: "deviceModal",
  components: { bspScreenCard, bspConditionBox, bspEmpty },
  data() {
    return {
      visible: false,
      params: {},
      deviceTypeLists: [
        { label: "仓内屏", value: 1 },
        { label: "仓外屏", value: 2 },
        { label: "防误放终端", value: 6 },
      ],
      deviceParam: {},
      deviceLists: [],
      selectAll: false,
      serialNumbers: new Set(),
    };
  },
  methods: {
    open(params) {
      this.params = params;
      this.deviceParam = {};
      this.visible = true;
      this.onSearch();
    },
    selectAllChange(val) {
      this.serialNumbers = new Set();
      this.deviceLists.forEach((item) => {
        val && this.serialNumbers.add(item.serialNumber);
        this.$set(item, "checked", val);
      });
    },
    close() {
      this.visible = false;
    },
    submit() {
      let params = {
        personnelType: this.params.personnelType,
        personnelIdList: [this.params.personnelId],
        serialNumbers: new Array(...this.serialNumbers),
      }
      this.$store.dispatch('authPostRequest', { url: this.$path.apiTerminal.terminal_face_send, params }).then(() => {
        this.successModal({ content: "下发成功" });
        this.close();
        this.$emit("on-refresh");
      }).catch(err => this.errorModal({ content: err }));
    },
    onSearch() {
      this.serialNumbers = new Set()
      this.selectAll = false;
      let { deviceName, deviceType } = this.deviceParam;
      let { personnelType } = this.params;
      this.$store.dispatch("authGetRequest", {
        url: this.$path.apiTerminal.terminal_face_device,
        params: { deviceName, deviceType, personnelType }
      }).then(res => {
        this.deviceLists = res.data;
      })
    },
    selectCard(item) {
      let serialNumber = item.serialNumber;
      if (this.serialNumbers.has(serialNumber)) {
        this.serialNumbers.delete(serialNumber)
      } else {
        this.serialNumbers.add(serialNumber)
      }
      this.$set(item, "checked", !item.checked);
      this.selectAll = this.serialNumbers.size === this.deviceLists.length
    },
  },
};
</script>

<style lang="less" scoped>
.card-box {
  height: 300px;
  overflow: auto;
}

.card-content-box {
  padding: 16px 0;
  display: grid;
  grid-template-columns: repeat(3, 276px);
  align-items: center;
  justify-items: start;
  grid-row-gap: 16px;
  justify-content: space-between;
  align-content: start;
}

.othr {
  margin-top: 12px;
}
</style>
