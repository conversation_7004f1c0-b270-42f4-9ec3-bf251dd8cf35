<template>
  <div>
    <div class="">
      <div class="bsp-list-container" v-if="showFormCompnent">
        <rs-DataGrid ref="grid" funcMark="ylgl-ypgs" :customFunc="false">
          <template slot="slot_total" slot-scope="{ row }">
            <span style="color: #2c2cf9; text-decoration: none; border-bottom: 1px solid; cursor: pointer"
              @click="gscs(row)">{{ row.total }}</span>
          </template>
          <template v-slot:ypgs:xz="{ oper }">
            <Button type="primary" class="header-button" @click="handleAdd()">
              <Icon :type="oper.iconPath ? oper.iconPath : 'md-add'" size="20" />
              {{ oper.name }}
            </Button>
          </template>
        </rs-DataGrid>
      </div>
    </div>
    <div v-if="!showFormCompnent">
      <component :is="component" @on_show_table="on_show_table" :curData="rowData" :ref="component"
        :modalTitle="modalTitle" />
    </div>
    <Modal v-model="visible" :title="rowData.xm + '顾送药次数详情'" @on-cancel="visible = false" class="face-camera-modal"
      width="60%">
      <div class="bsp-modal-container" v-if="visible">
        <rs-DataGrid ref="grid" funcMark="ypgs-rygsjllb" :customFunc="false" :params="rowData">
          <template slot="slot_total" slot-scope="{ row }">
            <span style="color: #2c2cf9; text-decoration: none; border-bottom: 1px solid; cursor: pointer"
              @click="gscs(row)">{{ row.total }}</span>
          </template>
          <!-- //签收 -->
          <template v-slot:rygsjl:qs="{ oper, row, }">
            <Button type="primary" v-if="row.status == '02' || row.status == '004'" class="header-button" @click="handleQs(row)">
              <Icon :type="oper.iconPath" size="20" />
              {{ oper.name }}
            </Button>
          </template>
          <template v-slot:rygsjl:zz="{ oper, row, index }"><!--终止操作-->
            <Button type="primary" v-if="row.status == '02' || row.status == '01' || row.status == '004'" class="row-button"
              @click="handleEnd(row)">
              <Icon v-if="oper.iconPath" :type="oper.iconPath" />
              {{ oper.name }}
            </Button>
          </template>
          <template v-slot:rygsjl:xq="{ oper, row, index }"><!--详情操作-->
            <Button v-if="row.status == '04' || row.status == '01' || row.status == '006' || row.status == '005' " type="primary" size="small" class="row-button"
              @click="handleXq(row)">
              <Icon v-if="oper.iconPath" :type="oper.iconPath" />
              {{ oper.name }}
            </Button>
          </template>
          <template v-slot:rygsjl:zcsq="{ oper, row, index }"><!--再次申请操作-->
            <Button v-if="row.status == '04' || row.status == '006' || row.status == '005' " type="primary" class="row-button" @click="handleAdd(row)">
              <Icon v-if="oper.iconPath" :type="oper.iconPath" />
              {{ oper.name }}
            </Button>
          </template>
        </rs-DataGrid>
      </div>

    </Modal>
  </div>
</template>

<script>
import { mapActions } from "vuex";
import addForm from "./AddForm.vue";
import { Modal } from "view-design";
import info from "./info.vue";
export default {
  components: {
    addForm, info
  },
  data() {
    return {
      showFormCompnent: true,
      rowData: {},
      component: null,
      modalTitle: '药品顾送申请',
      visible: false
    }
  },
  mounted() {
  },
  methods: {
    ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
    gscs(row) {
      this.rowData = row
      this.visible = true
    },
    handleQs(row) {
      this.rowData = row
      this.rowData.type = 'qs'
      this.modalTitle = '药品顾送签收'
      this.component = 'info'
      this.showFormCompnent = false
      this.visible = false
    },
    handleEnd(row) {
      this.rowData = row
      this.rowData.type = 'zz'
      this.modalTitle = '药品顾送终止'
      this.component = 'info'
      this.showFormCompnent = false
      this.visible = false
    },
    handleXq(row) {
      console.log(row, 'this.rowData')
      this.rowData = row
      this.rowData.type = 'approve'
      this.modalTitle = '药品顾送详情'
      this.component = 'info'
      this.showFormCompnent = false
      this.visible = false
    },
    handleZcsq(row) {

    },
    handleAdd(row) {
      // 新增
      this.rowData = row ? Object.assign(this.rowData, row) : {}
      this.modalTitle = '药品顾送再次申请'
      this.component = 'addForm'
      this.showFormCompnent = false
      this.visible = false
    },
    handleInfo(row) {
      console.log(row, 'this.rowData')

      this.rowData = row
      this.rowData.type = 'info'
      this.modalTitle = '药品顾送详情'
      this.component = 'addForm'
      this.showFormCompnent = false
    },
    handleEdit(row) {
      this.rowData = row
      this.modalTitle = '药品顾送登记'
      this.component = 'addForm'
      this.showFormCompnent = false
    },
    handleDelete(id) {
      this.confirmModal({ content: '是否确认删除？' }).then(async () => {
        this.getRequest({
          url: this.$path.acp_bizQuickAccessType_delete,
          params: { ids: id }
        }).then(data => {
          if (data.success) {
            this.successModal({
              title: '成功提示',
              content: '删除成功'
            }).then(async () => {
              this.on_refresh_table()
            })
          } else {
            this.errorModal({
              title: '错误提示',
              content: '删除失败'
            })
          }
        })
      })
    },
    on_show_table(isRefreshTable) {
      this.showFormCompnent = true
      if (isRefreshTable) {
        this.on_refresh_table()
      }
    },
    on_refresh_table() {
      this.$refs.grid.query_grid_data(1)
    },
  }
}
</script>

<style scoped></style>
