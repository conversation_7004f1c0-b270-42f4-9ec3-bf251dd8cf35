<template>
  <div class="disinfect-registration" style="width: 100%;">
    <div style="width: 100%;">
      <p class="title">{{ modalTitle }}</p>
      <div style="display: flex;width: 100%;">
        <div>
          <!-- 使用新的人员选择组件 -->
          <personnel-selector v-model="formValidate.jgrybm" :mode="formValidate.id ? 'detail' : 'edit'" title="被监管人员"
            placeholder="点击选择在押人员或扫码识别" :show-case-info="true" :enable-scan="true" :show-scan-tip="true"
            @change="handlePersonnelChange" />
          <record :jgrybm="formValidate.jgrybm" :traceList="formValidate.traceList" />
        </div>
        <div style="margin-left: 16px;width: calc(100% - 400px);">
          <!-- <ypxx :formData="formValidate"  :xcsy="true"></ypxx> -->
          <p class="section-title">药品信息</p>
          <Table :columns="columns" :data="medicineDeliverys" tooltip-theme="light" :tooltip-max-width="300" border>
            <template slot-scope="{ row,index }" slot="medicineName">
              <Input v-model="row.medicineName" search enter-button="选择" placeholder="请选择"
                @on-change="changeItem(row, index, 'medicineName')" @on-search="openYpSelect(row, index)" readonly
                @on-click="openYpSelect(row, index)" />
            </template>
             <template slot-scope="{ row ,index}" slot="approvalNum" v-if="row.medicineName">
              <Input v-model="row.approvalNum"
                  @on-change="changeItem(row, index, 'approvalNum',)" placeholder="请输入" 
                  style="width: 40%;"></Input>
             </template>
            
            <template slot-scope="{ row ,index}" slot="totalNum" v-if="row.medicineName">
              <div style="width: 100%;display: flex;justify-content: space-evenly;align-items: center;">
                <InputNumber :min="0" v-model="row.totalNum"
                  @on-change="(data) => changeItem(row, index, 'totalNum', data)" placeholder="请输入" type="number"
                  style="width: 40%;"></InputNumber>
                <Select v-model="row.dw" placement="top-end" @on-change="changeItem(row, index,)" placeholder="-（计量单位）"
                  style="width: 50%;z-index: 999999;">
                  <Option v-for="item in row.dwList" :value="item.value" :key="item.value">{{ item.title }}</Option>
                </Select>
                <Tooltip placement="top" :content="row.content">
                  <Icon type="ios-help-circle" size="24" color="#2d8cf0" />
                </Tooltip>
              </div>
            </template>

            <template slot-scope="{ row,index }" slot="action">
              <Button type="primary" @click="addTime(row, index)">增行</Button>&nbsp;<Button type="error"
                @click="deleteTime(row, index)">删除</Button>
            </template>
          </Table>
          <Form ref="formValidate" :model="formValidate" :rules="ruleValidate" :label-width="140">
            <p class="section-title" style="margin-top: 10px;">业务信息</p>
            <Row>
              <Col span="8">
              <FormItem label="期望送药日期" prop="expectedDate">
                <el-date-picker v-if="curData.type != 'info'" type="datetimerange" format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss" v-model="formValidate.expectedDate" size="small" placeholder="请选择"
                  style="width: 100%;" />
                <span v-else>{{ formValidate.expectedStartDate }}-{{ formValidate.expectedEndDate }}</span>
              </FormItem>
              </Col>
              <Col span="8">
              <FormItem label="顾送药来源" prop="drugSource">
                <s-dic-tree-grid placeholder="请选择" style="width: 100%; max-width: 450px; height: 40px"
                  v-model="formValidate.drugSource" ref="dicGrid" dicName="ZD_GSYLY" />
              </FormItem>
              </Col>
              <Col span="8">
              <FormItem label="顾送药原因" prop="deliveryReason">
                <s-dicgrid placeholder="请选择" style="width: 100%; max-width: 450px; height: 40px"
                  v-model="formValidate.deliveryReason" ref="dicGrid" dicName="ZD_GSYYY" />
              </FormItem>
              </Col>
            </Row>
            <FormItem label="备注" prop="deliveryRemark">
              <Input v-if="curData.type != 'info'" v-model="formValidate.deliveryRemark" type="textarea"
                placeholder="请填写"></Input>
              <span v-else>{{ formValidate.deliveryRemark }}</span>
            </FormItem>


          </Form>
        </div>
      </div>
    </div>
    <div class="bsp-base-fotter">
      <Button @click="on_return_table(false)">返 回</Button>
      <Button type="primary" :loading="loading" @click="handleSubmit" v-if="curData.type != 'info'">保 存</Button>
    </div>

    <!-- 药品选择 -->
    <Modal v-model="openModal" :mask-closable="false" :closable="true" class-name="" width="1100" title="选择药品">
      <div class="select-use">
        <ypData v-if="openModal" :url="$path.md_medicineCode_page" ref="ypData" />
      </div>
      <div slot="footer">
        <Button type="primary" @click="ypSelect" class="save">确 定</Button>
        <Button @click="openModal = false" class="save">关 闭</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import { mapActions } from 'vuex'
import uploadList from "@/components/upload/upload-list.vue";
import personnelSelector from '@/components/personnel-selector/index.vue'
import { getUserCache, formatDateparseTime } from "@/libs/util";
import Table from '@/view/disciplineBusiness/psy/table.vue';
import ypxx from "@/components/bl-form/ypxx.vue"
import ypData from "@/view/yfgl/ypcg/ypSelect.vue";
import record from "./record.vue";
import { Input } from 'view-design';

export default {
  name: 'AddDqxdForm',
  components: {
    uploadList, personnelSelector, ypxx, ypData, record
  },
  props: {
    modalTitle: String,
    curData: Object
  },
  data() {
    return {
      curIndex: 0,
      openModal: false,
      loading: false,
      formValidate: {
        prison: {},
        id: ''
      },
      ryId: '',
      ruleValidate: {
        expectedDate: [
          { required: true, message: '期望送药日期不能为空', trigger: 'blur,change',type:'array' }
        ],
        drugSource: [
          { required: true, message: '顾送药来源不能为空', trigger: 'blur,change' }
        ],
        deliveryReason: [
          { required: true, message: '顾送药原因不能为空', trigger: 'blur,change' }
        ],
        deliveryRemark: [
          { required: true, message: '备注不能为空', trigger: 'blur,change' }
        ],
      },
      formData: {},
      importUrl: this.$path.upload_fj,

      columns: [
        { type: "index", title: "序号", width: 70, align: "center" },
        {
          title: "*批准文号",
          slot: "approvalNum",
          align: "center",
          tooltip: true,
        },
        {
          title: "药品名称*",
          slot: "medicineName",
          align: "center",
          width:360,
          tooltip: true,
        },
        // {
        //   title: "*剂型",
        //   key: "dosageFormName",
        //   align: "center",
        //   width: 150,
        //   tooltip: true,
        // },
        // {
        //   title: "*规格",
        //   key: "specs",
        //   align: "center",
        //   width: 160,
        //   tooltip: true,
        // },
        // {
        //   title: "*生产单位",
        //   key: "productUnitEn",
        //   align: "center",
        //   width: 180,
        //   tooltip: true,
        // },
        {
          title: "*数量",
          slot: "totalNum",
          width: 200,
          align: "center",
          tooltip: true,
        },
        {
          title: "操作",
          slot: "action",
          width: 180,
          align: "center",
        },
      ],
      medicineDeliverys: [
        {
          name: "",
          goodsType: "",
          quantity: "",
          unit: "",
          features: "",
          storageLocationName: "",
          photoCount: "",
          remark: "",
        },
      ],
      dicData: []
    }
  },
  watch: {
    'curData': {
      handler(n, o) {
        console.log(n, 'curData')
        if (n.id) {
          this.getData(n.id)
        }
        if (n.jgrybm) {
          this.$set(this.formValidate, 'jgrybm', n.jgrybm)
          this.$set(this.formValidate, 'id', n.id)

        }
      }, deep: true, immediate: true
    }
  },
  mounted() {
    if (!this.formValidate.deliveryDate) {
      this.$set(this.formValidate, 'deliveryDate', formatDateparseTime(new Date()))
    }
    if (!this.formValidate.doctorName) {
      this.$set(this.formValidate, 'doctorName', getUserCache.getUserName())
      this.$set(this.formValidate, 'doctorIdCard', getUserCache.getIdCard())
    }
    this.$dicKit.getDataRows("ZD_MEASUREMENT_UNIT").then(res => {
      this.dicData = res
    })
  },

  methods: {
    ...mapActions(['authPostRequest', 'authGetRequest', 'getRequest']),
    // 处理人员选择变化
    handlePersonnelChange(personnelData, jgrybm) {
      if (personnelData && jgrybm) {
        this.formValidate.prison = personnelData
        this.formValidate.jgrybm = jgrybm
        this.formValidate.jgryxm = personnelData.xm

        // 自动获取该监管人委托律师信息
        // this.getPersonList()
      } else {
        this.formValidate.prison = {}
        this.formValidate.jgrybm = ''
        this.formValidate.jgryxm = ''

        // 清空办案人员信息
        // this.clearCasePersonnelInfo()
      }
    },
    on_return_table() {
      this.$emit('on_show_table', false)
    },
    removeItem(e, index) {
      this.$Modal.confirm({
        title: "提示",
        render: (h, params) => {
          return h("div", [
            h(
              "p",
              { style: { marginLeft: "10px", wordBreak: "break-all" } },
              "是否确认删除【" + e.item.filename + "】?"
            ),
          ]);
        },
        loading: true,
        onOk: async () => {
          this.$Modal.remove();
          this.formValidate.fileList.splice(e.index, 1);
        },
      });
    },
    selectUser(data) {
      console.log(data, 'datadatadata')
      this.$set(this.formValidate, 'jgrybm', data.jgrybm)
      this.$set(this.formValidate, 'jgryxm', data.xm)

    },
    // 提交
    handleSubmit() {
      if(!this.formValidate.jgrybm){
          this.$Message.error('请选择监管人员');
      }
      this.validateAll()
    },
    async validateAll() {
      try {
        // 使用Promise.all等待所有表单验证完成
        const [valid1] = await Promise.all([
          // new Promise(resolve => this.$refs.ryxx.$refs.formData?.validate(resolve)),
          new Promise(resolve => this.$refs.formValidate?.validate(resolve))
        ]);

        if (valid1) {
          console.log('验证通过，开始保存数据')
          this.saveData();
        } else {
          this.$Message.error('请检查表单填写是否正确');
        }
      } catch (error) {
        console.error('验证出错:', error);
      }
    },
    getData(id) {
      this.getRequest({
        url: this.$path.md_medicineDeliveryApply_get,
        params: { id }
      }).then(res => {
        this.loading = false
        if (res.success || res.code === 200) {
          // this.formValidate = res.data
          console.log(this.formValidate, 'this.formValidatethis.formValidate')
          if (res.data.medicineDeliverys && res.data.medicineDeliverys.length > 0) {
            this.medicineDeliverys = res.data.medicineDeliverys
            this.medicineDeliverys.forEach(ele => {
              let dataArr = [
                {
                  title: ele.dosageFormName + "（计量单位）", 
                  value: 1
                },
              ]
              this.$set(ele, 'dwList', dataArr)
              this.$set(ele, 'dw', 1)
            })
            {

            }
          }

        } else {

        }
      }).catch(error => {

      })
    },
    // 保存数据
    saveData() {
      this.loading = true
      // 准备提交数据
      if (this.formValidate.expectedDate && this.formValidate.expectedDate.length > 0) {
        this.formValidate.expectedStartDate = this.formValidate.expectedDate[0]
        this.formValidate.expectedEndDate = this.formValidate.expectedDate[1]
      }
      this.medicineDeliverys.forEach(ele=>{
          this.$set(ele, 'id','')
      })
      this.$set(this.formValidate, 'medicineDeliverys', this.medicineDeliverys)
       this.$set(this.formValidate, 'id','')
      let url = this.$path.md_medicineDeliveryApply_create
      // if (this.formValidate.id) {
      //   url = this.$path.md_medicineDeliveryApply_update
      // }
      this.authPostRequest({
        url: url,
        params: this.formValidate
      }).then(res => {
        this.loading = false
        if (res.success || res.code === 200) {
          this.$Message.success('保存成功')
          this.on_return_table()
        } else {
          this.$Message.error(res.msg || res.message || '保存失败')
        }
      }).catch(error => {
        this.loading = false
        this.$Message.error('网络异常，请稍后重试')
        console.error('提交失败：', error)
      })
    },

    // 准备提交数据
    prepareSubmitData() {
      const data = { ...this.formData }

      // 过滤掉空值和undefined
      Object.keys(data).forEach(key => {
        if (data[key] === '' || data[key] === null || data[key] === undefined) {
          delete data[key]
        }
      })

      return data
    },
    deleteTime(item, index) {
      this.medicineDeliverys.splice(index, 1)
    },
    addTime(item, index) {
      this.medicineDeliverys.push({})
    },
    openYpSelect(row, index) {
      this.rowData = row;
      this.curIndex = index;
      this.openModal = true;
    },
    changeItem(row, index, file, data) {
      console.log(row, index, file, 'row, index, file', data)
      if (file) {
        //  this.$set(row,file,row.file)
        this.$set(this.medicineDeliverys[index], file, data)
      }
      console.log(this.medicineDeliverys, 'medicineDeliverys')
    },
    filterName(e) {
      if (this.dicData && this.dicData.length > 0) {
        if (e && e != '') {
          return this.dicData.filter(i => {
            return i.code == e
          })[0].name
        } else {
          return "-"
        }
      }
    },
    ypSelect(data) {
      console.log(this.$refs.ypData.selectionData[0], ' this.$refs.ypData.selectionData[0]')
      this.$set(this.medicineDeliverys, this.curIndex, this.$refs.ypData.selectionData[0])
      this.$set(this.medicineDeliverys[this.curIndex], 'id', '')
      let dataArr = [
        {
          title: this.$refs.ypData.selectionData[0].dosageForm + "（计量单位）",  // this.$refs.ypData.selectionData[0].measurementUnitName + "（计量单位）",
          value: 1
        },
        // {
        //   title: this.filterName(this.$refs.ypData.selectionData[0].minMeasurementUnit) + "（最小计量单位）",  //this.$refs.ypData.selectionData[0].minMeasurementUnitName + "（最小计量单位）",//
        //   value: 2
        // }
      ]
      this.$set(this.medicineDeliverys[this.curIndex], 'dwList', dataArr)
      this.$set(this.medicineDeliverys[this.curIndex], 'dw', 1)
      this.$forceUpdate()
      this.openModal = false;


    }
  }
}
</script>

<style lang="less" scoped>
.disinfect-registration {
  height: 100%;

  // 确保表单项标签对齐
  /deep/ .ivu-form-item-label {
    text-align: right;
    padding-right: 12px;
  }
}

.section-title {
  border-left: 4px solid #2d8cf0;
  padding-left: 8px;
  font-size: 16px;
  font-weight: 700;
  height: 20px;
  line-height: 20px;
  position: relative;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
}

.title {
  border-bottom: 1px solid #f1f5f6;
  padding-bottom: 10px;
  margin-bottom: 10px;
}
</style>
