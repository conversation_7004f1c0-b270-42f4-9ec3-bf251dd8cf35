<template>
    <!-- 展开数据 -->
    <div style="padding: 0 16px;">
        <div style="padding-bottom:16px">
            <Input v-model="page.medicineName" style="width: 300px;" placeholder="请输入产品名称" /> &nbsp;&nbsp;<Button
                @click="cancalData">重置</Button>&nbsp;&nbsp;<Button type="primary" @click="getPage">搜索</Button>
        </div>
        <!--             @on-selection-change="handleSelectRow()" 
            @on-select="handleSelect" -->
        <Table :loading="spinShow" :columns="columns" :data="dataTable" ref="selection" :height="500" stripe
            :filterMultiple="false" border>
            <template slot-scope="{ row ,index}" slot="selection">
                <Radio v-model="row.single" @on-change="selectData(row, index)"></Radio>
            </template>
            <template slot-scope="{ row ,index}" slot="dosageForm">
                <s-dicgrid placeholder="请选择"  v-model='row.dosageForm' disabled ref="dicGrid"
                    dicName='ZD_DOSAGE_FORM' />
            </template>
        </Table>
        <div style="padding: 16px 0 0 0;display: flex;justify-content: end;">
            <Page :total="total" class="pageWrap" size="small" show-elevator show-sizer :page-size="page.pageSize"
                @on-prev="getCurrent" @on-next="getCurrent" :current="page.pageNo" @on-change="getCurrent"
                @on-page-size-change="getSize" />
        </div>
    </div>
</template>

<script>
import { sDataGrid } from "sd-data-grid";
import { mapActions } from 'vuex'
export default {
    props:{
        url:String,
        data:Array
    },
    components:{
        sDataGrid,
    },
    data() {
        return {
            page: {
                pageSize: 10,
                pageNo: 1,
                medicineName: ''
            },
            single: false,
            selectionData: [],
            total: 0,
            spinShow: false,
            columns: [
                {
                    slot: 'selection',
                    width: 60,
                    align: 'center'
                },

                {
                    type: 'index',
                    width: 80,
                    align: 'center',
                    title: '序号'
                },
                {
                    title: "批准文号",
                    key: "approvalNum",
                    align: 'center'
                },
                {
                    title: "产品名称",
                    key: "medicineName",
                    width: 140,
                    align: 'center'
                },
                {
                    title: "剂型",
                    slot:"dosageForm",
                    align: 'center'
                },
                {
                    title: "规格",
                    key: "specs",
                    align: 'center'
                },
                // {
                //   title: "上市许可持有人",
                //   key: "marketApprovalHolder",
                //   align:'center'
                // },
                {
                    title: "生产单位",
                    key: "productUnit",
                    align: 'center'
                },
                // {
                //   title: "药品编码",
                //   key: "medicineStandardCode",
                //   align:'center'
                // },
                // {
                //   title: "单位",
                //   key: "packageUnit",
                //   align:'center'
                // }
            ],
            dataTable: []
        }
    },
    methods: {
        ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest']),
        getPage() {
            this.spinShow = true
            let params = this.page
            this.$store.dispatch('authGetRequest', {
                url: this.url? this.url:this.$path.get_purchase_apply_page,
                params: params
            }).then(resp => {
                if (resp.code == 0) {
                    this.spinShow = false
                    this.$set(this, 'dataTable', resp.data.list)
                    // this.dataTable=resp.data.list
                    this.total = resp.data.total
                } else {
                    this.spinShow = false
                }
            })
        },
        cancalData() {
            this.page.medicineName = '';
            this.getPage()
        },
        getCurrent(current) {
            this.page.pageNo = current;
            this.getPage()
        },
        getSize(pageSize) {
            this.page.pageSize = pageSize;
            this.getPage()
        },
        selectData(row, index) {
            console.log(row, index, 'row,indx')
            this.dataTable.forEach(ele => {
                this.$set(ele, 'single', false)
            })
            this.dataTable[index].single = true
            this.selectionData = [row]
        },
        handleSelectRow(selection, index) {
            console.log(this.$refs.selection.getSelection());
            this.selectionData = this.$refs.selection.getSelection()
        },
        handleSelect(selection, row) {
            // 当选择某一行时，取消之前的选择，只保留当前行
            if (this.selectionData && this.selectionData !== row) {
                this.selectionData = row; // 更新当前选中的行
            } else if (!this.selectionData) {
                this.selectionData = row; // 如果是首次选择，直接设置当前选中的行
            } else {
                // 如果已经是当前行，则不改变选中状态，保持为选中状态
            }
        }
    },
    mounted() {
        if(!this.data){
          this.getPage()
        }else{
          this.$set(this, 'dataTable', this.data)

        }
    }
}
</script>

<style></style>