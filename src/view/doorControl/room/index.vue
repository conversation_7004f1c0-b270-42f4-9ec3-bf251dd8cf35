<template>
    <div>
        <s-DataGrid ref="grid" funcMark="mjgl-mjdlb" :customFunc="true" v-if="!detailsModal"
            :beforeRender="beforeRender">
            <template slot="customHeadFunc" slot-scope="{ func }">
                <Button type="primary" v-if="func.includes(globalAppCode + ':mjdlb:tbmjd')" @click.native="handleUpdate"
                    :loading="btnLoading">同步门禁点信息</Button>
            </template>
            <template slot="customRowFunc" slot-scope="{ func,row,index }">
                <Button type="primary" v-if="func.includes(globalAppCode + ':mjdlb:bdjs')"
                    @click.native="handleRoom(row)">{{ '绑定监室' }}</Button>
                <!-- row.bind_room_status == '1'? '解绑': -->
                <Button type="primary" v-if="func.includes(globalAppCode + ':mjdlb:details')" style="margin-left: 10px;"
                    @click.native="handleDetails(row)">详情</Button>
            </template>
            <template slot="slot_door_status" slot-scope="{ row, index }">
                {{zdmjdztList.find(item => item.code == row.door_status)?.name}}
            </template>
        </s-DataGrid>
        <roomDetails v-if="detailsModal" @close="detailsClose" ref="roomDetails" :formItem="formItem"> </roomDetails>
        <Modal v-model="openModal" :mask-closable="false" :closable="true" width="40%" title="绑定监室">
            <Form ref="formData" :model="formData" :rules="ruleValidate" :label-width="150">
                <Row>
                    <Col span="24">
                    <FormItem label="绑定监室" prop="roomId">
                        <div class="room-list">
                            <div class="list-box" v-if="formData.roomId != ''" @click="addRoom">
                                <div class="user-flex-sm">
                                    <img :src="roomSex == '1' || roomSex == '5' ? require('@/assets/icons/man.svg') : require('@/assets/icons/woman.svg')"
                                        style="padding-right: 5px;" />
                                    <div style="width: 100%;">
                                        <p>{{ formData.roomName }}</p>
                                        <p>
                                            <Icon type="md-person" />&nbsp;{{ formData.imprisonmentAmount }}
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <div class="add-prisoner-btn" @click="addRoom" v-else><u>+</u>添加监室</div>
                        </div>
                    </FormItem>
                    </Col>
                    <Col span="14">
                    <FormItem label="绑定状态" prop="bindRoomStatus">
                        <RadioGroup v-model="formData.bindRoomStatus">
                            <Radio label="0">解绑</Radio>
                            <Radio label="1">绑定</Radio>
                        </RadioGroup>
                    </FormItem>
                    </Col>
                </Row>
            </Form>
            <div slot="footer">
                <Button @click="clearForm" class="save">关 闭</Button>
                <Button type="primary" @click="submit" class="save">确 定</Button>

            </div>
        </Modal>

        <Modal v-model="roomSelect" title="" width="80%" class="modal">
            <room-select ref="roomSelect" :selectRoomIds="formData.roomId" :isMultiple="false"></room-select>
            <div slot="footer">
                <Button @click="roomSelect = false" class="save">关 闭</Button>
                <Button type="primary" @click="getRoom" class="save">确 定</Button>

            </div>
        </Modal>

    </div>
</template>
<script>
import roomSelect from "@/components/roomSelect/index.vue";
import roomDetails from "./roomDetails.vue";
export default {
    name: 'doorControl',
    components: { roomSelect, roomDetails },
    data() {
        return {
            btnLoading: false,
            openModal: false,
            formData: {
                roomId: '',
                roomName: '',
                bindRoomStatus: '',
                id: ''
            },
            roomSex: '',
            roomSelect: false,
            ruleValidate: {
                bindRoomStatus: [
                    { required: true, message: "请选择绑定状态", trigger: "change" },
                ],
                roomId: [
                    { required: true, message: "请选择监室", trigger: "change" },
                ],
            },
            detailsModal: false,
            formItem: {},
            zdmjdztList: [],
            timer: null,
        }
    },
    created() {

    },
    mounted() {
        this.handleGetZD_MJDZT();
        this.timer = setInterval(() => {
            this.$refs.grid.query_grid_data()
        }, 15000) // 每15秒刷新一次
    },
    methods: {
        beforeRender(data) {     //请求案件的数据
            return new Promise((resolve, reject) => {
                //模拟异步方法，可处理data数据、请求后台数据
                if (data.success && data.rows != undefined && data.rows.length > 0) {
                    setTimeout(() => {
                        data.rows.forEach((item, index) => {
                            this.$store.dispatch('postRequest', { url: this.$path.pm_getDeviceDoorStatus, params: { doorIndexCode: item.id } }).then(res => {
                                data.rows[index].door_status = res.data.doorState
                            })

                        });
                        resolve(data);

                    }, 500)
                } else {
                    resolve(data);
                }
            });
        },
        async handleUpdate() {
            this.btnLoading = true;
            this.$store.dispatch('authPostRequest', { url: this.$path.pm_deviceDoorUpdate, params: {} }).then(res => {
                if (res.success) {
                    this.$Message.success('同步成功')
                    this.on_refresh_table()
                } else {
                    this.$Message.error(res.message || '同步失败')
                }
                this.btnLoading = false;
            })
        },
        submit() {
            this.$refs.formData.validate(valid => {
                if (valid) {

                    this.$store.dispatch('postRequest', { url: this.$path.pm_bindRoom, params: this.formData }).then(res => {
                        if (res.success) {
                            this.$Message.success('绑定成功')
                            this.on_refresh_table()
                            this.clearForm()
                        } else {
                            this.$Message.error(res.message || '绑定失败')
                        }
                        // this.openModal = false
                    })
                }
            })
        },
        getRoom() {
            let room = this.$refs.roomSelect.checkedRoom[0]
            console.log(room);
            this.formData.roomId = room.roomCode
            this.formData.roomName = room.roomName
            this.roomSex = room.roomSex
            // this.formData.roomId = this.roomList.map(item => item.roomCode).join(',')
            this.roomSelect = false
        },
        // 获取监室
        getRoomData(roomId) {
            let params = {
                orgCode: this.$store.state.common.orgCode,
                pageNo: 1,
                pageSize: 99,
            }
            this.$store.dispatch('authPostRequest', {
                url: '/acp-com/base/pm/areaPrisonRoom/page',
                params: params
            }).then(resp => {
                if (resp.code == 0) {
                    let roomList = resp.data.list.filter(item => roomId.split(',').includes(item.id))
                    this.roomSex = roomList[0].roomSex
                    this.formData.imprisonmentAmount = roomList[0].imprisonmentAmount
                } else {
                    this.$Notice.error({
                        title: '错误提示',
                        desc: resp.msg
                    })
                }
            })
        },
        addRoom() {
            this.roomSelect = true;
        },
        handleRoom(row) {
            this.formData.id = row.id
            if (row.bind_room_status == '1') {
                this.formData.roomId = row.room_id
                this.formData.roomName = row.room_name
                this.formData.bindRoomStatus = row.bind_room_status
                // this.formData.bindRoomStatus = '0'
                this.getRoomData(row.room_id)
                this.$refs.roomSelect.getCheckedRoom()
            } else {
                this.formData.bindRoomStatus = '1'
            }

            this.openModal = true
        },
        handleDetails(row) {
            // this.$Message.warning('功能开发中...')
            this.formItem = row
            this.detailsModal = true
            if (this.timer) {
                clearInterval(this.timer);
            }

        },
        detailsClose() {
            this.detailsModal = false
            this.timer = setInterval(() => {
                this.$refs.grid.query_grid_data()
            }, 15000) // 每15秒刷新一次
        },
        //清除表单
        clearForm() {
            this.formData = {
                roomId: '',
                roomName: '',
                bindRoomStatus: '',
                id: ''
            }
            this.openModal = false
        },
        on_refresh_table() {
            this.$refs.grid.query_grid_data(1)
        },
        handleGetZD_MJDZT() {
            this.$store
                .dispatch("authGetRequest", {
                    url: "/bsp-com/static/dic/acp/ZD_MJDZT.js",
                })
                .then((res) => {
                    let scales = eval("(" + res + ")");
                    this.zdmjdztList = scales();
                });
        },
    },
    beforeDestroy() {
        if (this.timer) {
            clearInterval(this.timer);
        }
    }


}
</script>

<style scoped lang="less">
/deep/.add-prisoner-btn {
    background: #FFFFFF;
    border-radius: 6px;
    border: 1px dashed #2390FF;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    cursor: pointer;
    color: #2390FF;
    height: 80px;
    width: 160px;

    u {
        font-size: 28px;
        text-decoration: none;
        margin-right: 5px;
    }
}

.room-list {
    display: flex;
    flex-wrap: wrap;
}

/deep/.list-box {
    width: 160px;
    height: 80px;
    border: 1px solid #c9ddf0;
    cursor: pointer;

    border-radius: 6px;
    margin: 0 5px 5px 0;
    padding: 10px;
    box-shadow: 0px 6px 14px 1px rgba(7, 31, 88, 0.1);

    .user-flex-sm {
        display: flex;
        align-items: center;
        width: 100%;
    }
}
</style>
