<template>
  <div style="padding-bottom: 30px;">
    <div class="cont">
      <div class="fm-content-info">
        <p class="fm-content-info-title">
          <Icon type="md-list-box" size="24" color="#2b5fda"/>
          详情
        </p>
        <div class="fm-content-box">
          <Row>
            <Col span="4"><span>门禁点名称</span></Col>
            <Col span="8"><span>{{ formItem.name }}</span></Col>
            <Col span="4"><span>所属区域</span></Col>
            <Col span="8"><span>{{ formItem.origin_region_name }}</span></Col>
            <Col span="4"><span>区域路径</span></Col>
            <Col span="8"><span>{{ formItem.origin_region_path_name }}</span></Col>
            <Col span="4"><span>监室</span></Col>
            <Col span="8"><span>{{ formItem.room_name }}</span></Col>
            <Col span="4"><span>门禁点编号</span></Col>
            <Col span="8"><span>{{ formItem.door_no }}</span></Col>
            <Col span="4"><span>门禁点状态</span></Col>
            <Col span="8"><span>{{ formItem.door_statusName }}</span></Col>
            <Col span="4"><span>绑定监室</span></Col>
            <Col span="20"><span>{{ formItem.bind_room_statusName }}</span></Col>
          </Row>
        </div>
      </div>
    </div>
    <div class="tab-box">
      <Tabs value="1">
        <TabPane label="门禁控制事件" name="1" style="min-height: 300px;">
          <div>
            <Form ref="searchform" :model="searchform" :label-width="80">
              <Row>
                <!-- <Col span="6">
                <FormItem label="姓名" prop="xm">
                    <Input v-model="searchform.xm"></Input>
                </FormItem>
                </Col> -->
                <Col span="8">
                  <FormItem label="时间" prop="sj">
                    <DatePicker v-model="searchform.sj" type="datetimerange" style="width: 100%"
                                @on-change="dateChange">
                    </DatePicker>
                  </FormItem>
                </Col>
                <Col span="6" style="margin-left: 10px">
                  <FormItem label="门禁事件" prop="eventType">
                    <Select clearable filterable style="width: 80%"
                            v-model="searchform.eventType">
                      <Option v-for="item in dictMapList" :value="item.code" :key="item.code">
                        {{ item.name }}
                      </Option>
                    </Select>
                  </FormItem>
                </Col>
                <Col span="6">
                  <FormItem label="">
                    <Button @click.native="handleReset">重置</Button>
                    <Button type="primary" style="margin-left: 15px;" @click.native="search">查询</Button>
                  </FormItem>

                </Col>
              </Row>
            </Form>
          </div>
          <div style="position: relative;">
            <Table border :columns="columns1" :data="tableData">
              <template slot-scope="{ row,index  }" slot="receiveTime">
                <div v-if="row.receiveTime">{{ dayjs(row.receiveTime).format('YYYY-MM-DD HH:mm:ss') }}</div>
                <div v-else>--</div>
              </template>
              <template slot-scope="{ row,index  }" slot="eventType">
                <div v-if="row.eventType">{{ typeFormatter(row.eventType) }}</div>
                <div v-else>--</div>
              </template>
            </Table>
            <Spin size="large" fix v-if="spinShow"></Spin>
          </div>
        </TabPane>
      </Tabs>
    </div>
    <div class="page_box">
      <Page :total="total" class="pageWrap" size="small" show-elevator show-sizer :page-size="searchform.pageSize"
            @on-prev="getNo" @on-next="getNo" :current="searchform.pageNo" @on-change="getNo"
            @on-page-size-change="getSize"/>
    </div>

    <div class="bsp-base-fotter">
      <Button @click="close" style="margin-right: 10px;">取消</Button>
    </div>
  </div>
</template>

<script>

export default {
  props: {
    formItem: {
      type: Object,
      default: () => {
      }
    }
  },
  data() {
    return {
      tableData: [],
      columns1: [
        {
          title: '事件时间',
          slot: 'receiveTime',
          align: 'center'
        },
        {
          title: '门禁点',
          key: 'doorName',
          align: 'center'
        },
        {
          title: '控制器',
          key: 'devName',
          align: 'center'
        },
        {
          title: '事件类型',
          slot: 'eventType',
          align: 'center'
        },
        {
          title: '读卡器',
          key: 'readerDevName',
          align: 'center'
        }, {
          title: '姓名',
          key: 'personName',
          align: 'center'
        }, {
          title: '出入',
          key: 'inOrOut',
          align: 'center'
        }

      ],
      searchform: {
        sj: [],
        pageNo: 1,
        pageSize: 10,
        sort: 'enventTime',
        order: 'desc',
      },
      total: 0,
      spinShow: false,
      dictMap: {},
      dictMapList: []

    }
  },
  methods: {
    handleGetZD_MJSJ() {
      this.$store.dispatch("authGetRequest", {
        url: "/bsp-com/static/dic/acp/ZD_MJSJ.js",
      }).then((res) => {
        let scales = eval("(" + res + ")");
        scales().forEach(item => {
          this.$set(this.dictMap, item.code, item.name)
        });
        this.dictMapList = scales()
      });
    },
    close() {
      this.$emit('close')
    },
    getData() {
      let params = {
        doorIndexCodes: [this.formItem.index_code],
        startTime: this.dayjs(this.searchform.sj[0]).format('YYYY-MM-DD HH:mm:ss'),
        endTime: this.dayjs(this.searchform.sj[1]).format('YYYY-MM-DD HH:mm:ss'),
        receiveStartTime: this.dayjs(this.searchform.sj[0]).format('YYYY-MM-DD HH:mm:ss'),
        receiveEndTime: this.dayjs(this.searchform.sj[1]).format('YYYY-MM-DD HH:mm:ss'),
        pageNo: this.searchform.pageNo,
        pageSize: this.searchform.pageSize,
        sort: this.searchform.sort,
        order: this.searchform.order
      }
      if (this.searchform.eventType) {
        params.eventTypes = [Number(this.searchform.eventType)]
      }
      this.spinShow = true
      this.$store.dispatch('authPostRequest', {
        url: this.$path.pm_getDoorControlEventList,
        params: params
      }).then(res => {
        if (res.success) {
          this.tableData = res.data.list
          this.total = res.data.total
        } else {
          this.$Message.error(res.message)
        }
        this.spinShow = false
      })

    },
    dateChange(dates) {
      const date1 = new Date(dates[0]);
      const date2 = new Date(dates[1]);
      // 计算天数差
      const timeDiff = Math.abs(date2 - date1);
      const daysDiff = timeDiff / (1000 * 60 * 60 * 24);

      // 检查是否 ≤ 90 天
      const isWithin90Days = daysDiff <= 90;
      if (!isWithin90Days) {
        this.$Message.error('日期范围超过三个月');
        return
      }

      console.log('dateChange', this.searchform.sj[0])

    },
    eventTypeChange() {
    },
    getNo(pageNo) {
      this.$set(this.searchform, 'pageNo', pageNo)
      this.getData()
    },
    getSize(pageSize) {
      this.$set(this.searchform, 'pageSize', pageSize)
      this.getData()
    },
    search() {
      this.getData()
    },
    handleReset() {
      this.searchform = {
        sj: [new Date().setHours(0, 0, 0), new Date().setHours(23, 59, 59)],
        pageNo: 1,
        pageSize: 10,
        sort: 'enventTime',
        order: 'desc',

      }
      this.getData()
    },
    typeFormatter(cellValue) {
      return this.dictMap[cellValue] || cellValue
    }
  },
  mounted() {
    this.handleGetZD_MJSJ()
    this.searchform.sj = [new Date().setHours(0, 0, 0), new Date().setHours(23, 59, 59)]
    this.getData()
  },
}

</script>

<style scoped lang="less">
.fm-content-info-title {
  border-top: 1px solid #CEE0F0;
  border-left: 1px solid #CEE0F0;
  border-right: 1px solid #CEE0F0;
}

.cont {
  box-shadow: 0 2px 4px 1px rgba(0, 34, 84, .12);
  border-radius: 4px;
  padding: 10px;
}

.tab-box {
  margin-top: 10px;
  box-shadow: 0 2px 4px 1px rgba(0, 34, 84, .12);
  padding: 10px;

}
</style>
