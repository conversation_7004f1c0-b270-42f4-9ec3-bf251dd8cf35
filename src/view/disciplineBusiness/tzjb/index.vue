<template>
  <div>
    <div class="">
      <div class="bsp-base-content" v-if="!showData">
        <s-DataGrid ref="grid" funcMark="tzjb:list" :customFunc="true" :params="params" >
          <template slot="customHeadFunc" slot-scope="{ func }">
            <Button type="primary" v-if="func.includes(globalAppCode + ':tzjb:list:add')" @click.native="addEvent('add')">新增</Button>
          </template>
          <template slot="customRowFunc" slot-scope="{ func, row, index }" class="btnList">
              <Button type="primary" v-if="func.includes(appCode + ':tzjb:list:dispose') && (row.not_proc_user && row.not_proc_user.includes(idCard))" 
              @click.native="editEvent(index,row,'handle')" >处理</Button>&nbsp;&nbsp;

              <Button type="primary" v-if="func.includes(appCode + ':tzjb:list:detail')" 
              @click.native="editEvent(index,row,'info')" >详情</Button>&nbsp;&nbsp;

              <Button type="error" v-if="func.includes(appCode + ':tzjb:list:del')" @click.native="delEvent(row)" >删除</Button>
          </template>
        </s-DataGrid>
      </div>
      <!-- 提讯登记 -->
      <!-- <div v-if='showData' class='InquiryTitle'>{{modalTitle}}</div> -->
      <detail v-if='saveType=="info" && showData' @toback='toback' :curId='curData.id' />
      <jbsxcl v-if='saveType=="handle" && showData' @toback='toback' :curId='curData.id' :type='curData.publish_type' />
	  </div>
    <!-- 新增 -->
    <Modal
        v-model="openModalAdd"
        :mask-closable="false"
        :closable="true"
        class-name="select-sy-modal"
        width="60%"
        title="新增"
    >
        <div>
          <p class="com-sub-title">业务信息</p>
            <Form ref="formData" :model="formData" :rules="ruleValidate" :label-width="140" :label-colon="true">
                <Row>
                    <Col span="12">
                      <FormItem label="标题" prop="title" style="width: 100%;" :rules="[{ trigger: 'blur', message: '标题为必填', required: true }]">
                          <Input v-model="formData.title" placeholder="" maxlength="" style="width: 100%;"></Input>
                      </FormItem>
                    </Col>
                    <Col span="12">
                      <FormItem label="发布类型" prop="publishType" style="width: 100%;" :rules="[{ trigger: 'blur', message: '发布类型为必填', required: true }]">
                          <RadioGroup v-model="formData.publishType">
                            <Radio label="01">交办事项</Radio>
                            <Radio label="02">通知公告</Radio>
			                    </RadioGroup>
                      </FormItem>
                    </Col>  
                    <Col span="24">
                      <FormItem label="正文" prop="content" style="width: 100%;" :rules="[{ trigger: 'blur', message: '请填写', required: true }]">
                          <Input v-model="formData.content" type="textarea" :autosize="{minRows: 5,maxRows: 6}"></Input>
                      </FormItem>
                    </Col>
                    <Col span="12">
                      <FormItem label="全警" prop="isFullPolice" style="width: 100%;" :rules="[{ trigger: 'blur', message: '全警为必填', required: true }]">
                          <RadioGroup v-model="formData.isFullPolice">
                            <Radio label="1">是</Radio>
                            <Radio label="0">否</Radio>
			                    </RadioGroup>
                      </FormItem>
                    </Col>
                    <Col span="12">
                      <FormItem label="处理人" prop="userList" style="width: 100%;" :rules="[{ trigger: 'blur', message: '处理人为必选', required: true, type: 'array' }]" v-if="formData.isFullPolice == '0'">
                          <user-selector  v-model="formData.userId" tit="用户选择" :orgCode='formData.orgCode'
                            @onCancel="onCancel" @onClear="onClear" @onSelect="onSelect" :selectPost="true"
                            :text.sync="formData.userName" returnField="id"  msg="至少选中1人">
                          </user-selector>
                      </FormItem>
                    </Col>
                    <Col span="12">
                      <FormItem label="附件" prop="attUrl" style="width: 100%;">
                          <file-upload :key="formData.attUrl" :serviceMark="serviceMark"
                            :bucketName="bucketName" :defaultList="fileList" :beforeUpload="() => true"
                            @fileComplete="fileCompleteFile" />
                      </FormItem>
                    </Col>
                </Row>
            </Form>
        </div>
        <div slot="footer">
          <Button @click="openModalAdd=false">取消</Button>
          <Button type="primary" @click="submitJb" :loading="loadingsignIn">确认</Button>
        </div>
    </Modal>
  </div>
</template>

<script>
import { sDataGrid } from 'sd-data-grid'
import { mapActions } from 'vuex'
import uploadList from "@/components/upload/upload-list.vue";
import {userSelector} from 'gs-user-selector'
import detail from './detail.vue'
import jbsxcl from './jbsxcl.vue'
import { getUserCache } from '@/libs/util'
export default {
    components: {
	  sDataGrid,
    uploadList,
    userSelector,
    detail,
    jbsxcl
	},
    data() {
        return {
            appName: serverConfig.APP_NAME,
            appCode: serverConfig.APP_MARK,
            appId: serverConfig.APP_ID,
            serviceMark: serverConfig.OSS_SERVICE_MARK,
            bucketName: serverConfig.bucketName,
            idCard: getUserCache.getIdCard(),
            params:{},
            showData: false,
            modalTitle: '',
            saveType: '',
            openModalAdd: false,
            loadingsignIn: false,
            fileList: [],
            formData: {
              userList: [],
              publishType: '01',
              isFullPolice: '0',
              userId:"",
              userIdName:"",
              orgCode:""
            },
            curData: {},
            ruleValidate: {},
        }
    },
    methods: {
        addEvent(type) {
          this.formData = {
            userList: [],
            publishType: '01',
            isFullPolice: '0',
            userId:"",
            userIdName:"",
            orgCode:""
          }
          this.fileList = []
          
          this.saveType = type
          this.openModalAdd = true
        },
        editEvent(index,row,type){
          this.saveType = type
          this.curData = row
          if(type == 'info') {
            this.modalTitle = '详情信息'
          } else if(type == 'handle') {
            this.modalTitle = '交办事项处理信息'
          }
          this.showData = true
        },
        delEvent(row) {
            this.$store.dispatch('authGetRequest',{
              url: this.$path.app_notifyAssign_delete,
              params:{
                ids: row.id
              }
            }).then(res => {
              if(res.success) {
                this.$nextTick(() => {
                  this.on_refresh_table()
                })
              } else {
                this.$Message.error('接口操作失败!')
              }
            })
        },
        submitJb() {
          console.log(this.formData,'this.formData');
          
          this.$refs.formData.validate(valid => {
            if(valid) {
              console.log(this.formData,'提交数据');
              this.saveData()
            } else {
              this.$Message.error('请填写完整内容!')
            }
          })
        },
        saveData() {
          this.$store.dispatch('authPostRequest',{
            url: this.$path.app_notifyAssign_create,
            params: this.formData
          }).then(res => {
            if(res.success) {
              this.openModalAdd = false
              this.$nextTick(() => {
                this.on_refresh_table()
              })
            } else {
              this.$Message.error(res.msg || '接口操作失败!')
            }
          })
        },
        fileCompleteFile(file) {
          this.fileList = file
          this.$set(this.formData,'attUrl',JSON.stringify(file))
        },
        onSelect(data){
          console.info(data)
          if(data) {
            const newData = data.map(item => ({
              id: item.id,
              idCard: item.idCard,
              name: item.name,
              orgCode: item.orgCode,
              orgName: item.orgName,
            }))
            console.log(newData,'newData');
            this.formData.userList = newData
          }
        },
        onCancel(){
        },
        onClear(){
        },
        toback() {
          this.showData = false
        },
        on_refresh_table () {
          this.$refs.grid.query_grid_data(1)
        }
    },
    created() {
      console.log(this.$route.query);
      if(this.$route.query && this.$route.query.type) {
        if(!this.$route.query.bussinId) {
          this.$Modal.error({
            title: '温馨提示',
            content: '业务数据不能为空!'
          })
          return;
        }
        
        this.saveType = 'handle'
        this.curData = {
          id: this.$route.query.bussinId,
          publish_type: this.$route.query.type
        }
        this.showData = true
      }
    }
} 
</script>

<style lang="less" scoped>
.com-sub-title{
  border-left: 4px solid #3491fa;
  padding-left: 8px;
  font-size: 16px;
  font-weight: bold;
  height: 20px;
  line-height: 20px;
  position: relative;
  margin-bottom: 16px;
}
/deep/.ivu-modal-body {
  height: 500px !important;
  overflow: auto;
}
.ivu-form-item{
    margin-bottom: 10px !important;
  }
 .InquiryTitle{
    border-bottom:1px solid #dcdee2;
    padding:16px;
 }
 .ivu-table-cell-slot button{
  margin:5px 0;
 }
</style>