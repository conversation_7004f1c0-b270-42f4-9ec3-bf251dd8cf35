<template>
  <div class="special-Meal-Audit">
    <header>
      <h3>{{ titleAudit }}</h3>
      <Icon @click="back" :size="20" type="md-close"/>
    </header>
    <div class="people-info">
      <div class="upload-img">
        <div class="front-Photo">
          <img :src="ryxxInfo.frontPhoto ? ryxxInfo.frontPhoto : personImg" alt="">
        </div>
      </div>
      <div class="people-msg">
        <Row style="margin-bottom: 1px;">
          <Col span="8">
                    <span class="Light-blue">
                        被监管人员
                    </span>
            <span class="ligth-gray">
                        {{ ryxxInfo.xm }}
                    </span>
          </Col>
          <Col span="8">
                    <span class="Light-blue">
                        监室号
                    </span>
            <span class="ligth-gray">
                        {{ ryxxInfo.roomName }}
                    </span>
          </Col>
          <Col span="8">
                    <span class="Light-blue">
                        入所时间
                    </span>
            <span class="ligth-gray">
                        {{ ryxxInfo.rssj }}
                    </span>
          </Col>
        </Row>
        <Row style="margin-bottom: 1px;">
          <Col span="8">
                    <span class="Light-blue">
                        诉讼环节
                    </span>
            <span class="ligth-gray">
                        {{ ryxxInfo.sshjName }}
                    </span>
          </Col>
          <Col span="8">
                    <span class="Light-blue">
                        关押期限
                    </span>
            <span class="ligth-gray">
                        {{ ryxxInfo.gyqx }}
                    </span>
          </Col>
          <Col span="8">
                    <span class="Light-blue">
                        涉嫌罪名
                    </span>
            <span class="ligth-gray">
                        {{ ryxxInfo.sxzmName }}
                    </span>
          </Col>
        </Row>
        <Row style="margin-bottom: 1px;">
          <Col span="8">
                    <span class="Light-blue">
                        风险等级
                    </span>
            <span class="ligth-gray">
                        {{ ryxxInfo.fxdjName }}
                    </span>
          </Col>
          <Col span="8">
                    <span class="Light-blue">
                        户籍地
                    </span>
            <span class="ligth-gray">
                        {{ ryxxInfo.hjdName }}
                    </span>
          </Col>
          <Col span="8">
                    <span class="Light-blue">

                    </span>
            <span class="ligth-gray">

                    </span>
          </Col>
        </Row>
        <Row style="margin-bottom: 1px;">
          <Col span="8">
                    <span class="Light-blue">
                        近期就诊时间
                    </span>
            <span class="ligth-gray">
                        {{ ryxxInfo.jqjzsj }}
                    </span>
          </Col>
          <Col span="8">
                    <span class="Light-blue">
                        诊断病情
                    </span>
            <span class="ligth-gray">
                        {{ ryxxInfo.zjzdbq }}
                    </span>
          </Col>
          <Col span="8">
                    <span class="Light-blue">

                    </span>
            <span class="ligth-gray">

                    </span>
          </Col>
        </Row>
      </div>
    </div>
    <div class="apply-for-registration">
      <lineInfo infoName="申请登记"/>
      <div class="registration-container">
        <div class="people-msg">
          <Row style="margin-bottom: 1px;">
            <Col span="12">
                        <span class="Light-blue">
                            配餐类型
                        </span>
              <span class="ligth-gray">
                            {{ specialApplydetail.mealTypeName }}
                        </span>
            </Col>
            <Col span="12">
                        <span class="Light-blue">
                            配餐时间
                        </span>
              <span class="ligth-gray">
                            {{
                  `${specialApplydetail.mealStartTime} - ${specialApplydetail.mealEndTime}，
                            共${this.specialApplydetail.daysDiff}天`
                }}
                        </span>
            </Col>
          </Row>
          <Row style="margin-bottom: 1px;">
            <Col span="24">
                        <span class="Light-blue">
                            用餐时段
                        </span>
              <span class="ligth-gray">
                            {{ specialApplydetail.mealPeriodName }}
                        </span>
            </Col>

          </Row>
          <Row style="margin-bottom: 1px;">
            <Col span="24">
                        <span class="Light-blue">
                            指定日期
                        </span>
              <span class="ligth-gray">
                            {{ specialApplydetail.specifiedDateName }}
                        </span>
            </Col>
          </Row>
          <Row style="margin-bottom: 1px;">
            <Col span="24">
                        <span class="Light-blue">
                            申请原因
                        </span>
              <span class="ligth-gray">
                            {{ specialApplydetail.reason }}
                        </span>
            </Col>
          </Row>
          <Row style="margin-bottom: 1px;">
            <Col span="12">
                        <span class="Light-blue">
                            申请人
                        </span>
              <span class="ligth-gray">
                            {{ specialApplydetail.regOperatorXm }}
                        </span>
            </Col>
            <Col span="12">
                        <span class="Light-blue">
                            申请时间
                        </span>
              <span class="ligth-gray">
                            {{ specialApplydetail.regTime }}
                        </span>
            </Col>
          </Row>
        </div>
      </div>
    </div>
    <!-- 审核审核功能 -->
    <div class="doctor-registration-form" v-if="auditType == 'doctor'">
      <lineInfo infoName="医生审批意见"/>
      <div class="form-container">
        <Form :model="formItem" :label-width="150" ref="formItem">
          <Row>
            <Col span="12">
              <FormItem label="审批结果" prop="doctorApprovalResult"
                        :rules="{ required: true, message: '请选择审批结果', trigger: 'change' }">
                <RadioGroup v-model="formItem.doctorApprovalResult">
                  <Radio label="1">同意</Radio>
                  <Radio label="0">不同意</Radio>
                </RadioGroup>
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem label="审批意见" prop="doctorApprovalComments"
                        :rules="{ required: true, message: '请输入审批意见', trigger: 'change' }">
                <Input v-model="formItem.doctorApprovalComments" type="textarea" style="width: 300px;"
                       :autosize="{ minRows: 2, maxRows: 5 }" placeholder="请输入审批意见"></Input>
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="12">
              <FormItem label="审批人">
                <Input v-model="doctorApproveName" :disabled="true" placeholder="审批人"></Input>
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem label="审批时间">
                {{ specialApplydetail.doctorApproverTime }}
              </FormItem>
            </Col>
          </Row>
        </Form>

      </div>
    </div>
    <!-- 所领导审核 -->
    <div class="led-registration-form" v-if="auditType == 'leader'">
      <lineInfo infoName="所领导审批意见"/>
      <div class="form-container">
        <Form :model="formLedItem" :label-width="150" ref="formLedItem">
          <Row>
            <Col span="12">
              <FormItem label="审批结果" prop="leaderApprovalResult"
                        :rules="{ required: true, message: '请选择审批结果', trigger: 'change' }">
                <RadioGroup v-model="formLedItem.leaderApprovalResult">
                  <Radio label="1">同意</Radio>
                  <Radio label="0">不同意</Radio>
                </RadioGroup>
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem label="审批意见" prop="leaderApprovalComments"
                        :rules="{ required: true, message: '请输入审批意见', trigger: 'change' }">
                <Input v-model="formLedItem.leaderApprovalComments" type="textarea" style="width: 300px;"
                       :autosize="{ minRows: 2, maxRows: 5 }" placeholder="请输入审批意见"></Input>
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="12">
              <FormItem label="审批人">
                <Input v-model="doctorApproveName" :disabled="true" placeholder="审批人"></Input>
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem label="审批时间">
                ---
              </FormItem>
            </Col>
          </Row>
        </Form>

      </div>
    </div>
    <div class="btn-sure" v-if="auditType == 'doctor'">
      <Button size="large" @click="handleReset('formItem')">取消</Button>
      <Button size="large" v-if="isSumbit === 'yes'" type="primary" style="margin-left: 10px;"
              @click="handleSubmit('formItem')">
        确认
      </Button>
    </div>

    <div class="btn-sure" v-if="auditType == 'leader'">
      <Button size="large" @click="handleReset('formLedItem')">取消</Button>
      <Button size="large" v-if="isSumbit === 'yes'" type="primary" style="margin-left: 10px;"
              @click="handleSubmit('formLedItem')">确认
      </Button>
    </div>
  </div>
</template>

<script>
import {mapActions} from 'vuex'
import lineInfo from '../component/lineInfo.vue'
import dayjs from 'dayjs'
import Cookies from 'js-cookie'

export default {
  name: "specialMealAudit",
  data() {
    return {
      ryxxInfo: {},
      titleAudit: '',
      isSumbit: 'yes',
      specialApplydetail: {},
      personImg: require("../../../../assets/images/cateringManage/default_Img.svg"),
      formItem: {
        doctorApprovalResult: "1",
        doctorApprovalComments: "",
      },
      formLedItem: {
        leaderApprovalResult: "1",
        leaderApprovalComments: "",
      },
      id: "",
      useMealList: [],
    }
  },

  watch: {
    'formItem.doctorApprovalResult': {
      handler(newVal) {
        if (newVal == 1) {
          this.formItem.doctorApprovalComments = "同意"
        } else {
          this.formItem.doctorApprovalComments = "不同意"
        }
      },
      immediate: true
    },
    'formLedItem.leaderApprovalResult': {
      handler(newVal) {
        if (newVal == 1) {
          this.formLedItem.leaderApprovalComments = "同意"
        } else {
          this.formLedItem.leaderApprovalComments = "不同意"
        }
      },
      immediate: true
    }
  },

  methods: {
    ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
    handleGetRyxxInfo(selectUseIds) {
      this.authGetRequest({url: this.$path.specialApply_people_search, params: {jgrybm: selectUseIds}}).then(res => {
        if (res.success) {
          this.ryxxInfo = res.data
        }
      })
    },
    handleGetSpecialApplydetail(id) {
      this.authGetRequest({url: this.$path.specialApply_people_detail, params: {id: id}}).then(res => {
        if (res.success) {
          this.specialApplydetail = res.data
          console.log(this.specialApplydetail)
          if (this.specialApplydetail.doctorApproverTime && this.auditType === 'doctor') {
            this.isSumbit = 'no'
          }
          if (this.specialApplydetail.leaderApproverTime && this.auditType === 'leader') {
            this.isSumbit = 'no'
          }
          let mealPeriod = this.specialApplydetail.mealPeriod.split(',')
          let specifiedDate = this.specialApplydetail.specifiedDate.split(',')
          let mealPeriodName = mealPeriod.map(item =>
            this.useMealList.find(meal => meal.code === item)?.name || item
          )
          let specifiedDateName = specifiedDate.map(item => {
            if (item == 1) {
              item = '星期一'
            } else if (item == 2) {
              item = '星期二'
            } else if (item == 3) {
              item = '星期三'
            } else if (item == 4) {
              item = '星期四'
            } else if (item == 5) {
              item = '星期五'
            } else if (item == 6) {
              item = '星期六'
            } else {
              item = '星期日'
            }
            return item
          })
          this.specialApplydetail['mealPeriodName'] = mealPeriodName.join(',')
          this.specialApplydetail['specifiedDateName'] = specifiedDateName.join(',')
          const startDate = dayjs(this.specialApplydetail.mealStartTime);
          const endDate = dayjs(this.specialApplydetail.mealEndTime);
          const daysDiff = endDate.diff(startDate, 'day');
          this.specialApplydetail['daysDiff'] = daysDiff
        }
      })
    },
    handleSubmit(name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          let url = ""
          let params = {}
          if (name == 'formItem') {
            url = this.$path.specialApply_doctorApprove
            params = this.formItem
          } else {
            url = this.$path.specialApply_leaderApprove
            params = this.formLedItem
          }
          params.id = this.id
          this.authPostRequest({url: url, params: params}).then(res => {
            if (res.success) {
              this.$Message.success('审核成功')
              this.back()
            } else {
              this.$Message.error(res.message)
            }
          })
        } else {
          this.$Message.error('验证失败');
        }
      })
    },
    handleReset(name) {
      this.$refs[name].resetFields();
      this.back();
    },
    // 获取用餐时段字段
    handleGetZD_PCGL_DSLX() {
      this.authGetRequest({url: "/bsp-com/static/dic/pam/ZD_PCGL_DSLX.js"}).then(res => {
        let useMeal = eval('(' + res + ')')
        this.useMealList = useMeal()
      })
    },
    back() {
      // 返回上一页（更推荐）
      console.log("上一页")
      this.$router.go(-1);
    }
  },

  components: {
    lineInfo
  },

  created() {
    const {jgrybm, id, audit} = this.$route.query
    this.id = id
    this.auditType = audit
    if (this.auditType === 'doctor') {
      this.titleAudit = '医生审核'
    } else {
      this.titleAudit = '所领导审核'
    }
    this.handleGetRyxxInfo(jgrybm)
    this.handleGetZD_PCGL_DSLX()
    this.handleGetSpecialApplydetail(id)
    this.doctorApproveName = this.$store.state.common.userName
  },

  computed: {},

}

</script>

<style scoped lang="less">
.special-Meal-Audit {
  width: 100%;
  // height: 94%;
  padding: 10px;
  overflow: auto;
  background-color: #fff;

  header {
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: solid 1px #f5f5f5;
  }

  .people-info {
    display: flex;
    align-items: flex-end;
    padding-top: 20px;
    padding-bottom: 20px;
    border-bottom: solid 1px #f5f5f5;

    .upload-img {
      width: 167px;
      height: 165px;
      padding: 0 5px 0 10px;

      .add-svg {
        display: flex;
        align-items: flex-end;
        cursor: pointer;

        img {
          width: 34px;
          height: 24px;
        }

        span {
          display: inline-block;
          color: #2B5FD9;
          margin-left: 5px;
        }
      }

      .front-Photo {
        padding-top: 5px;

        img {
          width: 143px;
          height: 136px;
        }
      }
    }

    .people-msg {
      flex: 1;

    }
  }

  .apply-for-registration {
    padding: 20px 0;
    border-bottom: solid 1px #f5f5f5;

    .registration-container {
      padding: 20px 0;
    }
  }

  .doctor-registration-form {
    padding: 20px 0;
    border-bottom: solid 1px #f5f5f5;
  }

  .doctor-registration-detail {
    padding: 20px 0;
    border-bottom: solid 1px #f5f5f5;

    .people-msg {
      padding-top: 20px;
    }
  }

  .led-registration-form {
    padding: 20px 0;
  }

  .btn-sure {
    display: flex;
    justify-content: center;
    padding: 10px 0;
  }
}

/deep/ .ivu-col-span-8 {
  display: flex;
}

/deep/ .ivu-col-span-12 {
  display: flex;
}

/deep/ .ivu-col-span-24 {
  display: flex;
}


.Light-blue {
  display: inline-block;
  line-height: 40px;
  padding-left: 5px;
  width: 120px;
  height: 40px;
  flex-shrink: 0;
  background-color: #e4eefc;
  margin: 0 1px;
}

.ligth-gray {
  flex: 1;
  height: 40px;
  line-height: 40px;
  padding-left: 5px;
  background-color: #f5f7fa;
}
</style>
