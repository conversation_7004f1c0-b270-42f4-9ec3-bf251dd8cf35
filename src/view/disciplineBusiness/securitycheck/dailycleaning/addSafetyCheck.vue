<template>
    <div>
        <div class="add-safety-check">
            <Form ref="formValidate" :model="formValidate" :rules="ruleValidate" :label-width="140" :label-colon="true">
                <div class="fm-content-wrap">
                    <p class="fm-content-wrap-title">
                        <Icon type="md-list-box" size="24" color="#2b5fda" />日常清监登记
                    </p>

                    <Row>
                        <Col span="8">
                        <FormItem label="检查时间" prop="checkTime">
                            <DatePicker type="datetime" v-model="formValidate.checkTime" placeholder="请选择检查时间"
                                style="width: 100%;">
                            </DatePicker>
                        </FormItem>
                        </Col>
                        <Col span="8">
                        <FormItem label="带队领导" prop="leaderUserSfzh">
                            <user-selector v-model="formValidate.leaderUserSfzh" tit="用户选择"
                                :text.sync="formValidate.leaderUserName" returnField="idCard" numExp='num >= 1'
                                msg="至少选中1人">
                            </user-selector>
                        </FormItem>
                        </Col>

                    </Row>
                    <Row>
                        <Col span="8">
                        <FormItem label="参加民辅警" prop="involvementUserSfzh">
                            <user-selector v-model="formValidate.involvementUserSfzh" tit="用户选择"
                                :text.sync="formValidate.involvementUserName" returnField="idCard" numExp='num >= 1'
                                msg="至少选中1人" @onClear="clearinvolvementUser()">
                            </user-selector>
                        </FormItem>
                        </Col>
                        <Col span="8">
                        <FormItem label="其他参加人" prop="otherParticipants">
                            <user-selector v-model="formValidate.otherParticipants" tit="用户选择"
                                :text.sync="formValidate.otherParticipantsName" returnField="idCard" numExp='num >= 1'
                                msg="至少选中1人">
                            </user-selector>
                        </FormItem>
                        </Col>

                    </Row>



                </div>
                <div class="fm-content-wrap" style="margin-top: 15px;">
                    <p class="fm-content-wrap-title">
                        <Icon type="md-list-box" size="24" color="#2b5fda" />检查明细
                    </p>
                    <Row type="flex" justify="space-between">
                        <Col span="8">
                        <FormItem label="检查监室" prop="checkRoomId">
                            <input v-model="formValidate.checkRoomId" type="hide" style="display: none;"></input>
                            <Button type="default" @click="handleSelectRoomId" long
                                :style="{ textAlign: 'left', borderColor: formValidate.checkRoomId ? '#2090d3' : '#cacaca', color: formValidate.checkRoomId ? '#2090d3' : '#cacaca' }">{{
                                    roomInfo.roomName ? "已选择监室: " +
                                        roomInfo.roomName : "请选择监室" }}</Button>
                        </FormItem>
                        </Col>
                        <Col span="2"><Button type="primary" @click="addRow" style="margin-top: 5px;">增行</Button></Col>
                    </Row>
                    <Table :columns="columns" :data="dataTable" border>
                        <template slot-scope="{ row, index }" slot="qcqy">
                            <s-dicgrid v-model="row.qcqy" dicName="ZD_RCQJ_QCWY"
                                @values="changeQcqy($event, index, 'qcqy', row)" />
                        </template>
                        <template slot-scope="{ row, index }" slot="jcr">
                            <user-selector v-model="row.jcr" tit="用户选择" :text.sync="row.jcrName" returnField="idCard"
                                numExp='num >= 1' msg="至少选中1人" @onSelect="changeUs($event, index, row)"
                                @onClear="clearUs(index, row)">
                            </user-selector>
                        </template>
                        <template slot-scope="{ row, index }" slot="jcjg">
                            <RadioGroup v-model="row.jcjg" @on-change="changeEnd(index, 'jcjg', row)">
                                <Radio label="0">正常</Radio>
                                <Radio label="1" style="margin-left: 10px;">异常</Radio>
                            </RadioGroup>
                        </template>
                        <template slot-scope="{ row, index }" slot="qksm">
                            <Input v-model="row.qksm" @on-change="changeEnd(index, 'qksm', row)" />
                        </template>
                        <template slot-scope="{ row,index }" slot="action" v-if="index !== 0">
                            <Button type="error" size="small" @click="remove(row, index)">删除</Button>
                        </template>
                    </Table>
                    <Row>
                        <Col span="8">
                        <FormItem label="是否岗位协同" prop="isJoin">
                            <RadioGroup v-model="formValidate.isJoin" @on-change="changeisJoin">
                                <Radio label="1">是</Radio>
                                <Radio label="0">否</Radio>
                            </RadioGroup>
                        </FormItem>
                        </Col>
                    </Row>
                    <Row v-if="formValidate.isJoin === '1'">
                        <Col span="24">
                        <FormItem label="岗位协同" prop="joinGw">
                            <CheckboxGroup v-model="formValidate.joinGw" style="width: 100%;" @on-change="changejoinGw">
                                <Checkbox v-for="(item, index) in joinGwList" :key="item.code" :label="item.code">{{
                                    item.name }}</Checkbox>
                            </CheckboxGroup>
                        </FormItem>
                        </Col>
                    </Row>
                    <Row v-if="formValidate.isJoin === '1' && formValidate.joinGw.includes('09')">
                        <Col span="8">
                        <FormItem label="指定人员" prop="joinRySfz">
                            <user-selector v-model="formValidate.joinRySfz" tit="用户选择"
                                :text.sync="formValidate.joinRyName" returnField="idCard">
                            </user-selector>
                        </FormItem>
                        </Col>
                    </Row>
                    <Row v-if="formValidate.isJoin === '1'">
                        <Col span="16">
                        <FormItem label="推送内容" prop="remarks">
                            <Input placeholder="请输入推送内容" v-model="formValidate.remarks" type="textarea"></Input>
                        </FormItem>
                        </Col>
                    </Row>


                </div>
            </Form>
        </div>
        <div class="bsp-base-fotter">
            <Button @click="onCancel">取 消</Button>
            <Button :loading="loadingSub" @click="handleSubmit" type="primary">提 交</Button>
        </div>
        <Modal v-model="openModal" :mask-closable="false" :closable="true" class-name="select-use-modal" width="1360"
            title="监室列表">
            <div class="select-use">
                <roomSelect ref="prisonSelect" ryzt="ALL" :isMultiple="false"
                    :selectRoomIds="formValidate.checkRoomId" />
            </div>
            <div slot="footer">
                <Button @click="openModal = false" class="save">关 闭</Button>
                <Button type="primary" @click="useSelect" class="save">确 定</Button>
            </div>
        </Modal>
    </div>
</template>

<script>
import { userSelector } from 'sd-user-selector'
import { roomSelect } from "sd-room-select";
import { getUserCache } from "@/libs/util.js"
export default {
    name: 'addSafetyCheck',
    components: { userSelector, roomSelect },
    props: {
        rowData: {
            type: Object,
            default: () => { }
        }
    },
    data() {
        return {
            formValidate: {
                checkTime: new Date(),
                involvementUserSfzh: getUserCache.getIdCard(),
                involvementUserName: getUserCache.getUserName(),
                leaderUserSfzh:'',
                leaderUserName:'',
                isJoin: '0',
                joinGw: [],
                checkRoomId:'',
                remarks:'',
                joinRyName:'',
                joinRySfz:'',
                dataSources:'1',//数据来源 1:实战平台
                isViolation:'0' //测试可删除

            },
            // 存储参加的民警避免重复添加
            involvementUser: [
                {
                    idCard: getUserCache.getIdCard(),
                    name: getUserCache.getUserName(),
                    init: true
                }
            ],
            ruleValidate: {

                checkTime: [
                    { required: true, message: '请选择检查时间', trigger: 'change', type: 'date' }
                ],
                involvementUserSfzh: [
                    { required: true, message: '请选择参加民辅警', trigger: 'change' }
                ],
                checkRoomId: [
                    { required: true, message: '请选择检查监室', trigger: 'change' }
                ],
                joinGw: [
                    { required: true, message: '请选择岗位协同', trigger: 'change', type: 'array' },
                    // { type: 'array', message: '请选择岗位协同', trigger: 'change' }
                ],
                remarks: [
                    { required: true, message: '请填写推送内容', trigger: 'change' }
                ],
            },
            openModal: false,
            loadingSub: false,
            roomInfo: {},
            dataTable: [
                {
                    qcqy: '',
                    qcqyName: '',
                    jcr: '',
                    jcrName: '',
                    jcjg: '0',
                    qksm: ''
                }
            ],
            columns: [
                {
                    type: 'index',
                    width: 80,
                    align: 'center',
                    title: '序号'
                },
                {
                    title: '*清查区域',
                    slot: 'qcqy',
                    align: 'center',
                },
                {
                    title: '*检查人',
                    slot: 'jcr',
                    align: 'center',
                },
                {
                    title: '*检查结果',
                    slot: 'jcjg',
                    align: 'center',
                },
                {
                    title: '情况说明',
                    slot: 'qksm',
                    align: 'center',
                },
                {
                    title: '操作',
                    slot: 'action',
                    width: 150,
                    align: 'center'
                }
            ],
            joinGwList: []

        }
    },
    mounted() {
        this.handleGetZD_RCQJGWXT();
    },
    methods: {
        onCancel() {
            this.$emit('on_show_table')
        },
        handleSelectRoomId() {
            this.openModal = true
        },
        useSelect() {
            this.roomInfo = this.$refs.prisonSelect.checkedRoom[0];
            this.formValidate.checkRoomId = this.roomInfo.id;
            this.openModal = false;
            this.pushText();
        },
        addRow(row) {
            let obj = {
                qcqy: '',
                qcqyName: '',
                jcr: '',
                jcrName: '',
                jcjg: '0',
                qksm: ''

            }
            this.dataTable.push(obj)
        },
        remove(row, index) {
            this.dataTable.splice(index, 1)
        },
        handleGetZD_RCQJGWXT() {
            this.$store
                .dispatch("authGetRequest", {
                    url: "/bsp-com/static/dic/acp/ZD_RCQJGWXT.js",
                })
                .then((res) => {
                    let scales = eval("(" + res + ")");
                    this.joinGwList = scales();
                });
        },
        handleSubmit() {

            this.$refs.formValidate.validate((valid) => {
                if (!this.checkTable()) {
                    return;
                }
                this.getviolationContent();
                if (valid) { 
                    this.loadingSub = true;
                    let params = {...this.formValidate};
                    params.checkTime = this.dayjs(this.formValidate.checkTime).format('YYYY-MM-DD HH:mm:ss');
                    params.cleanDailyDetails = this.dataTable;
                    params.joinGw = this.formValidate.joinGw.join(',');
                    this.$store.dispatch("authPostRequest", { url: this.$path.pam_createCleanDaily, params: params }).then((res) => {
                        this.loadingSub = false;
                        if (res.success) {
                            this.$Message.success("操作成功");
                            this.onCancel();
                        } else {
                            this.$Message.error(res.msg);
                        }
                    })
                }
            });
        },
        checkTable() {
            console.log("this.dataTable", this.dataTable);

            if (this.dataTable.length === 0) {
                this.$Message.error("请至少添加一行检查明细");
                return false;
            }
            for (let i = 0; i < this.dataTable.length; i++) {
                if (!this.dataTable[i].qcqy) {
                    this.$Message.error("请选择清查区域");
                    return false;
                }
                if (!this.dataTable[i].jcrName) {
                    this.$Message.error("请选择检查人");
                    return false;
                }
                if (!this.dataTable[i].jcjg) {
                    this.$Message.error("请选择检查结果");
                    return false;
                }
            }
            return true;
        },
        changeEnd(index, filed, row) {
            this.$set(this.dataTable[index], filed, row[filed]);
            this.pushText()
        },
        changeQcqy(event, index, filed, row) {
            this.$set(this.dataTable[index], "qcqyName", event[0].name);
            this.$set(this.dataTable[index], filed, row[filed]);
            this.pushText()

        },
        changeUs(event, index, row) {
            
            let name = event.map(item => item.name);
            let idCard = event.map(item => item.idCard);
            this.$set(this.dataTable[index], 'jcrName', name.join(','));
            this.$set(this.dataTable[index], 'jcr', idCard.join(','));
            const allSfzh = this.dataTable.reduce((acc, item) => {
                if (item.jcr) {
                    acc.push(...item.jcr.split(','));
                }
                return acc;
            }, []);
            const res = event.map(item => {
                return {
                    name: item.name,
                    idCard: item.idCard
                }
            });
            // 过滤掉重复的身份证号
            this.involvementUser.push(...res);
            this.involvementUser = this.involvementUser.filter(item => allSfzh.includes(item.idCard) || item.init === true);
            // 去重
            this.involvementUser = this.involvementUser.reduce((acc, current) => {
                const isDuplicate = acc.some(item => item.idCard === current.idCard);
                if (!isDuplicate) {
                    acc.push(current);
                }
                return acc;
            }, []);
       
            this.formValidate.involvementUserSfzh = this.involvementUser.map(item => item.idCard).join(',');
            this.formValidate.involvementUserName = this.involvementUser.map(item => item.name).join(',');
        },
        clearUs(index, row) {
            this.$set(this.dataTable[index], 'jcr', '');
            this.$set(this.dataTable[index], 'jcrName', '');
            const allSfzh = this.dataTable.reduce((acc, item) => {
                if (item.jcr) {
                    acc.push(...item.jcr.split(','));
                }
                return acc;
            }, []);
            // 清除参与人员
            this.involvementUser = this.involvementUser.filter(item => allSfzh.includes(item.idCard) || item.init === true);
            this.formValidate.involvementUserSfzh = this.involvementUser.map(item => item.idCard).join(',');
            this.formValidate.involvementUserName = this.involvementUser.map(item => item.name).join(',');

        },
        clearinvolvementUser() {
            this.involvementUser = [];
        },
        pushText() {
            console.log(this.formValidate.checkTime);
            if (formValidate.isJoin == '0') { 
                return
            }
            let time = this.dayjs(this.formValidate.checkTime).format('YYYY年MM月DD日');
            let listStr = this.dataTable.map(item => {
                return `${item.qcqyName}检查结果${item.jcjg == '0' ? '正常' : '异常'}`;
            }
            );
            console.log(this.formValidate.joinGw);
            if (this.formValidate.joinGw.length > 0) {
                // 1. 过滤出匹配的对象
                let matchedItems = this.joinGwList.filter(item => this.formValidate.joinGw.includes(item.code));
                let joinGwStr = matchedItems.map(item => item.name).join(',');
                this.formValidate.remarks = `${time}，${this.roomInfo?.roomName || ''}${listStr.join(',')}，请${joinGwStr}关注`;
            } else {
                this.formValidate.remarks = `${time}，${this.roomInfo?.roomName || ''}${listStr.join(',')}`;
            }


        },
        changejoinGw() {
            this.pushText();
        },
        //违禁情况 
        getviolationContent() {
            let listStr = this.dataTable.map(item => {
                return `${item.qcqyName}检查结果(${item.jcjg == '0' ? '正常' : '异常'})`;
            }
            );
            this.formValidate.violationContent = listStr.join(',');
        },
        changeisJoin() {
            this.formValidate.joinGw = [];
            this.formValidate.joinRyName = '';
            this.formValidate.joinRySfz = '';
             this.pushText();
        }
    }
}

</script>

<style scoped lang="less">
.add-safety-check {
    .fm-content-wrap {
        box-shadow: 0 1px 3px rgba(0, 0, 0, .1);
        padding: 0;

        /deep/.ivu-form-item-content {
            width: 70%;
        }

        /deep/ .ivu-form-item {
            margin-bottom: 24px !important;
        }

    }
}
</style>