<template>
    <div>
        <s-DataGrid ref="grid" funcMark="szpt-xsgk-jjb-djlb" :customFunc="true">
            <template slot="customHeadFunc" slot-scope="{ func }">
                <Button type="primary" icon="ios-add" v-if="func.includes(globalAppCode + ':szpt-xsgk-jjb-djlb:xz')"
                    @click.native="handleAdd('add')">新增</Button>
            </template>
            <template slot="customRowFunc" slot-scope="{ func,row,index }">
                <Button type="primary" @click.native="handleDetails(row)" size="small"
                    v-if="func.includes(globalAppCode + ':szpt-xsgk-jjb-djlb:xq')">详情</Button>
            </template>
        </s-DataGrid>
        <Modal v-model="openRegister" :mask-closable="false" :closable="true" width="60%" title="交班登记">
            <Form ref="formData" :model="formData" :label-width="130" :rules="rules"
                :label-colon="isAdd ? false : true">
                <p class="detail-title">人员分布情况</p>
                <div class="person-list">
                    <div class="itme">
                        <div class="title">总人数</div>
                        <div class="num">{{ PersonDistributionLsit.totalNumber || '0' }}</div>
                    </div>
                    <div class="itme">
                        <div class="title">点名人数</div>
                        <div class="num">{{ PersonDistributionLsit.rollCall || '0' }}</div>
                    </div>
                    <div class="itme">
                        <div class="title">出所就医</div>
                        <div class="num">{{ PersonDistributionLsit.outForMedicalTreatment || '0' }}</div>
                    </div>
                    <div class="itme">
                        <div class="title">提讯/询</div>
                        <div class="num">{{ PersonDistributionLsit.arraignment || '0' }}</div>
                    </div>
                    <div class="itme">
                        <div class="title">律师会见</div>
                        <div class="num">{{ PersonDistributionLsit.lawyerToMeet || '0' }}</div>
                    </div>
                    <div class="itme">
                        <div class="title">家属会见</div>
                        <div class="num">{{ PersonDistributionLsit.familyMeeting || '0' }}</div>
                    </div>
                    <div class="itme">
                        <div class="title">提解</div>
                        <div class="num">{{ PersonDistributionLsit.suggestion || '0' }}</div>
                    </div>
                    <div class="itme">
                        <div class="title">请假未归</div>
                        <div class="num">{{ PersonDistributionLsit.leaveButNotReturn || '0' }}</div>
                    </div>
                </div>
                <p class="detail-title">重点关注人员情况</p>
                <div class="personImg-list">
                    <Tabs>
                        <TabPane v-for="(item, index) in roomList" :key="item.roomId" :label="item.roomName"
                            :name="item.roomId">
                            <div class="prisoner-list">
                                <div class="item" v-for="(ele, index) in item.prisonerList" :key="ele.id">
                                    <div class="item-top">
                                        <img class="item-img" :src="ele.frontPhoto" />
                                        <div class="item-name">{{ ele.jgryxm }}</div>
                                    </div>
                                    <p class="ms">{{ ele.attentionReason }}</p>
                                </div>
                            </div>

                        </TabPane>
                    </Tabs>
                    <!-- <Icon type="ios-trash" size="30" style="margin-top: 25px;" /> -->
                </div>
                <p class="detail-title">巡控登记内容</p>
                <div class="person-table">
                    <Table border :columns="columns1" :data="registerData">
                        <template slot-scope="{ row }" slot="recordType">
                            <div>{{ row.recordType == '01'? '自动' :' 手动' }}</div>
                        </template>
                    </Table>
                </div>
                <p class="detail-title">交班信息登记</p>

                <Row class="show-form" v-if="isAdd">
                    <template v-if="globalAppCode != 'bjsdskss'">
                        <Col span="8">
                        <FormItem :label="globalAppCode == 'bjsjls' ? '电台数' : '交班总人数'" prop="totalPersons">
                            <Input placeholder="请输入" v-model="formData.totalPersons"></Input>
                        </FormItem>
                        </Col>
                        <Col span="8">
                        <FormItem :label="globalAppCode == 'bjsjls' ? '执法记录仪数' : '交班点名人数'" prop="rollCallTotal">
                            <Input placeholder="请输入" v-model="formData.rollCallTotal"></Input>
                        </FormItem>
                        </Col>
                    </template>
                    <Col span="8">
                    <FormItem label="交班对象" prop="handoverObjectSfzh">
                        <user-selector v-model="formData.handoverObjectSfzh" tit="用户选择"
                            :text.sync="formData.handoverObject" returnField="idCard" numExp='num==1' :selectPost="true" post="02"  msg="至少选中1人">
                        </user-selector>
                    </FormItem>
                    </Col>
                    <Col span="24">
                    <FormItem label="待处理问题" prop="pendingIssues">
                        <Input v-model="formData.pendingIssues" placeholder="请输入" type="textarea" :rows="2"></Input>
                    </FormItem>
                    </Col>
                    <Col span="8">
                    <FormItem label="交班人" prop="handoverPerson">
                        <Input disabled v-model="formData.handoverPerson"></Input>
                    </FormItem>
                    </Col>
                    <Col span="8">
                    <FormItem label="交班时间" prop="handoverTime">
                        <DatePicker v-model="formData.handoverTime" type="datetime" placeholder="请选择"></DatePicker>
                    </FormItem>
                    </Col>
                </Row>
                <div class="fm-content-box" v-else>
                    <Row v-if="globalAppCode != 'bjsdskss'">
                        <Col span="3"><span>{{ globalAppCode == 'bjsjls' ? '电台数' : '交班总人数' }}</span></Col>
                        <Col span="5"><span>{{ msg.totalPersons }}</span></Col>
                        <Col span="3"><span>{{ globalAppCode == 'bjsjls' ? '执法记录仪数' : '交班点名人数' }}</span></Col>
                        <Col span="13"><span>{{ msg.rollCallTotal }}</span></Col>
                    </Row>
                    <Row>
                        <Col span="3"><span>交班对象</span></Col>
                        <Col span="5"><span>{{ msg.takeoverPerson }}</span></Col>
                        <Col span="3"><span>待处理问题</span></Col>
                        <Col span="13"><span>{{ msg.pendingIssues }}</span></Col>
                        <Col span="3"><span>交班人</span></Col>
                        <Col span="5"><span>{{ msg.handoverPerson }}</span></Col>
                        <Col span="3"><span>交班时间</span></Col>
                        <Col span="13"><span>{{ msg.handoverTime }}</span></Col>
                    </Row>
                </div>
            </Form>
            <div slot="footer">
                <Button class="save" @click="closeRegister">关 闭</Button>
                <Button v-if="isAdd" type="primary" class="save" @click="submitForm">确 定</Button>
            </div>
        </Modal>
    </div>
</template>
<script>
import { mapActions } from 'vuex'
import { sDataGrid } from 'sd-data-grid'
import { userSelector } from 'gs-user-selector'
import { formatDateparseTime, getUserCache } from "@/libs/util.js"
import Cookies from "js-cookie";
export default {
    name: 'shifthandover',
    components: {
        sDataGrid,
        userSelector
    },
    data() {
        return {
            isAdd: true,
            roomList: [],
            openRegister: false,
            formData: {

                handoverObjectSfzh: '',
                handoverObject: '',
                handoverPerson: getUserCache.getUserName(),
                handoverPersonSfzh: getUserCache.getIdCard(),
                handoverTime: '',
                rollCallTotal: '',
                totalPersons: '',
                ryqk: '',
                zdgzry: '',
                xkdj: ''

            },
            columns1: [
                {
                    title: '序号',
                    type: 'index',
                    width: 70,
                    align: 'center'
                },
                {
                    title: '登记人',
                    key: 'operatorXm',
                    align: 'center',
                },
                {
                    title: '登记时间',
                    key: 'operatorTime',
                    align: 'center',
                    width: 200
                },
                {
                    title: '登记类型',
                    // key: 'recordTypeName',
                    slot: 'recordType',
                    align: 'center',
                },
                {
                    title: '登记内容',
                    key: 'recordContent',
                    align: 'center',
                },
                {
                    title: '监室号',
                    key: 'roomIdName',
                    align: 'center',
                },
                {
                    title: '登记来源',
                    key: 'todoSourceName',
                    align: 'center',
                },
            ],
            registerData: [],
            rules: {
                totalPersons: [
                    { required: true, message: '请填写交班总人数', trigger: 'blur' },
                    {
                        validator: (rule, value, callback) => {
                            if (isNaN(Number(value))) {
                                callback(new Error('请输入数字'));
                            }
                            else {
                                callback();
                            }
                        },
                        trigger: 'blur'
                    }
                ],
                rollCallTotal: [
                    { required: true, message: '请填写交班点名人数', trigger: 'blur' },
                    {
                        validator: (rule, value, callback) => {
                            if (isNaN(Number(value))) {
                                callback(new Error('请输入数字'));
                            }
                            else {
                                callback();
                            }
                        },
                        trigger: 'blur'
                    }
                ],
                handoverPerson: [
                    { required: true, message: '请填写交班人', trigger: 'blur' },
                ],
                handoverTime: [
                    { required: true, message: '请设置交班时间' },
                ],

                handoverObjectSfzh: [
                    { required: true, message: '请选择交班对象' },
                ],
                pendingIssues: [
                    { required: true, message: '请填写待处理问题', trigger: 'blur' },
                ],
            },
            PersonDistributionLsit: {},
            msg: {}
        }
    },

    mounted() {
        // this.getPersonDistribution()
    },
    methods: {
        ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
        handleAdd(type) {
            this.openRegister = true
            this.formData.handoverTime = new Date()
            this.getRoomData()
            this.getRegisterData()
            this.getPersonDistribution()
            this.isAdd = true
        },
        closeRegister() {
            this.openRegister = false
            this.formData = {
                handoverObjectSfzh: '',
                handoverObject: '',
                handoverPerson: getUserCache.getUserName(),
                handoverPersonSfzh: getUserCache.getIdCard(),
                handoverTime: '',
                rollCallTotal: '',
                totalPersons: '',
                ryqk: '',
                zdgzry: '',
                xkdj: ''

            }
            this.PersonDistributionLsit = {}
            this.registerData = []
            this.roomList = []
            this.$refs.grid.query_grid_data(1)
            if (this.$refs.formData) {
                this.$refs.formData.resetFields();
            }
        },
        getRegisterData() {
            this.authGetRequest({ url: this.$path.represent_getPatrolRecordList, params: {} }).then(res => {
                if (res.success) {

                    this.registerData = res.data
                    if (res.data.length > 0) {
                        this.formData.xkdj = res.data.map(item => item.id).join(',')
                    }
                }
            }
            )
        },
        getPersonDistribution() {
            this.authGetRequest({ url: this.$path.represent_getPersonDistribution, params: {} }).then(res => {
                if (res.success) {

                    this.PersonDistributionLsit = res.data
                    this.formData.ryqk = JSON.stringify(res.data)
                }
            }
            )
        },
        submitForm() {

            this.$refs.formData.validate(valid => {
                if (valid) {
                    const params = { ...this.formData };
                    params.handoverTime = formatDateparseTime(this.formData.handoverTime)
                    console.log("start", this.formData, params);
                    this.authPostRequest({ url: this.$path.represent_shiftReg, params: params }).then(res => {
                        if (res.success) {
                            this.$Message.success('新增成功')
                            this.closeRegister()
                        } else {
                            this.$Message.error(res.message)
                        }

                    }
                    )
                }
            })
        },
        getRoomData() {
            this.authGetRequest({ url: this.$path.represent_attention, params: { orgCode: getUserCache.getOrgCode() } }).then(res => {
                if (res.success) {
                    this.roomList = res.data
                    this.formData.zdgzry = JSON.stringify(res.data)

                }
            }
            )
        },
        handleDetails(row) {
            this.getShiftHandover(row.id)
            // this.getRoomData()
            // this.getRegisterData()
            console.log(row);


            this.formData.handoverTime = new Date()
            this.isAdd = false
            this.openRegister = true

        },
        getShiftHandover(id) {
            this.authGetRequest({ url: this.$path.represent_shiftHandoverGet, params: { id: id } }).then(res => {
                if (res.success) {
                    this.msg = res.data
                    this.roomList = JSON.parse(res.data.zdgzry)
                    this.PersonDistributionLsit = JSON.parse(res.data.ryqk)
                    this.registerData = res.data.xkdjList
                }
            }
            )
        }


    }
}

</script>

<style scoped lang="less">
.person-list {
    display: flex;
    gap: 20px;
    margin-bottom: 16px;
    justify-content: space-between;

    .itme {
        border: 1px solid #dcdfe6;
        width: 80px;
        height: 80px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        border-radius: 2px;

        .title {
            color: #1F2533;
            font-size: 16px;
        }

        .num {
            font-size: 16px;
            color: #4b81ff;
        }
    }
}

.personImg-list {
    display: flex;
    gap: 20px;
    margin-bottom: 16px;

    .personImg-item {
        width: 70px;

        .img {
            width: 70px;
            height: 90px;
        }

        .name {
            text-align: center;
        }

    }


    .prisoner-list::-webkit-scrollbar {
        // width: 1px;
        height: 5px;
    }

    .prisoner-list::-webkit-scrollbar-thumb {
        background-color: #ddd;
        border-radius: 5px;
    }

    .prisoner-list {
        display: flex;
        overflow: auto;
        gap: 15px;
        padding: 10px 0;
        margin: 0 10px;

        .item {
            .item-top {
                width: 82px;
                height: 110px;

                .item-img {
                    width: 82px;
                    height: 110px;


                }

                .item-name {
                    position: relative;
                    top: -24px;
                    height: 24px;
                    display: inline-block;
                    background: #e9f0ff;
                    border-radius: 0 0 4px 4px;
                    color: #4b81ff;
                    width: 100%;
                    text-align: center;
                }
            }

            .ms {
                text-align: center;
                font-size: 12px;
                margin-top: 5px;
            }
        }
    }

}

.person-table {
    margin-bottom: 16px;
}

.show-form {
    .item {
        line-height: 1;
        padding: 10px 0;
        font-size: 16px;
    }
}
</style>