<template>
    <div>
        <s-DataGrid ref="grid" funcMark="szpt-xsgk-jb-djlb" :customFunc="true">
            <template slot="customRowFunc" slot-scope="{ func,row,index }">
                <Button type="primary" @click.native="handleRegister(row)" size="small"
                    v-if="func.includes(globalAppCode + ':szpt-xsgk-jb-djlb') && !row.takeover_person">接班登记</Button>

                <Button type="primary" @click.native="handleDetails(row)" size="small"
                    v-if="func.includes(globalAppCode + ':szpt-xsgk-jb-xqlb') && row.takeover_person"
                    style="margin-left: 10px;">详情</Button>

            </template>
        </s-DataGrid>

        <Modal v-model="openRegister" :mask-closable="false" :closable="true" width="60%" title="接班确认">
            <Form ref="formData" :model="formData" :label-width="130" :rules="rules" :label-colon="true">
                <p class="detail-title">人员分布情况</p>
                <div class="person-list">
                    <div class="itme">
                        <div class="title">总人数</div>
                        <div class="num">{{ PersonDistributionLsit?.totalNumber || '0' }}</div>
                    </div>
                    <div class="itme">
                        <div class="title">点名人数</div>
                        <div class="num">{{ PersonDistributionLsit?.rollCall || '0' }}</div>
                    </div>
                    <div class="itme">
                        <div class="title">出所就医</div>
                        <div class="num">{{ PersonDistributionLsit?.outForMedicalTreatment || '0' }}</div>
                    </div>
                    <div class="itme">
                        <div class="title">提讯/询</div>
                        <div class="num">{{ PersonDistributionLsit?.arraignment || '0' }}</div>
                    </div>
                    <div class="itme">
                        <div class="title">律师会见</div>
                        <div class="num">{{ PersonDistributionLsit?.lawyerToMeet || '0' }}</div>
                    </div>
                    <div class="itme">
                        <div class="title">家属会见</div>
                        <div class="num">{{ PersonDistributionLsit?.familyMeeting || '0' }}</div>
                    </div>
                    <div class="itme">
                        <div class="title">提解</div>
                        <div class="num">{{ PersonDistributionLsit?.suggestion || '0' }}</div>
                    </div>
                    <div class="itme">
                        <div class="title">请假未归</div>
                        <div class="num">{{ PersonDistributionLsit?.leaveButNotReturn || '0' }}</div>
                    </div>
                </div>
                <p class="detail-title">重点关注人员情况</p>
                <div class="personImg-list">
                    <Tabs>
                        <TabPane v-for="(item, index) in roomList" :key="item.roomId" :label="item.roomName"
                            :name="item.roomId">
                            <div class="prisoner-list">
                                <div class="item" v-for="(ele, index) in item.prisonerList" :key="ele.id">
                                    <div class="item-top">
                                        <img class="item-img" :src="ele.frontPhoto" />
                                        <div class="item-name">{{ ele.jgryxm }}</div>
                                    </div>
                                    <p class="ms">{{ ele.attentionReason }}</p>
                                </div>
                            </div>

                        </TabPane>
                    </Tabs>
                    <!-- <Icon type="ios-trash" size="30" style="margin-top: 25px;" /> -->
                </div>
                <p class="detail-title">巡控登记内容</p>
                <div class="person-table">
                    <Table border :columns="columns1" :data="registerData">
                        <template slot-scope="{ row }" slot="recordType">
                            <div>{{ row.recordType == '01'? '自动' :' 手动' }}</div>
                        </template>
                    </Table>
                </div>
                <p class="detail-title">交班信息登记</p>
                <div class="fm-content-box">
                    <Row v-if="globalAppCode != 'bjsdskss'">
                        <Col span="3"><span>{{ globalAppCode == 'bjsjls' ? '电台数' : '交班总人数' }}</span></Col>
                        <Col span="9"><span>{{ msg.totalPersons }}</span></Col>
                        <Col span="3"><span>{{ globalAppCode == 'bjsjls' ? '执法记录仪数' : '交班点名人数' }}</span></Col>
                        <Col span="9"><span>{{ msg.rollCallTotal }}</span></Col>
                    </Row>
                    <Row>
                        <Col span="3"><span>待处理问题</span></Col>
                        <Col span="21"><span>{{ msg.pendingIssues }}</span></Col>
                        <Col span="3"><span>交班人</span></Col>
                        <Col span="9"><span>{{ msg.handoverPerson }}</span></Col>
                        <Col span="3"><span>交班时间</span></Col>
                        <Col span="9"><span>{{ msg.handoverTime }}</span></Col>
                    </Row>
                </div>
                <p class="detail-title" style="margin-top: 16px;">接班信息登记</p>

                <Row class="show-form" v-if="isAdd">
                    <Col span="8">
                    <FormItem label="换班实际人数">
                        <Input placeholder="请输入" v-model="formData.takeoverTotalPersons"></Input>
                    </FormItem>
                    </Col>
                    <Col span="8">
                    <FormItem label="接班人" prop="takeoverPerson">
                        <Input disabled v-model="formData.takeoverPerson"></Input>
                    </FormItem>
                    </Col>
                    <Col span="8">
                    <FormItem label="接班时间" prop="takeoverTime">
                        <DatePicker v-model="formData.takeoverTime" type="datetime" placeholder="请选择">
                        </DatePicker>
                    </FormItem>
                    </Col>
                </Row>
                <div class="fm-content-box" v-else>
                    <Row>
                        <Col span="3"><span>换班实际人数</span></Col>
                        <Col span="5"><span>{{ msg.takeoverTotalPersons }}</span></Col>
                        <Col span="3"><span>接班人</span></Col>
                        <Col span="4"><span>{{ msg.takeoverPerson }}</span></Col>
                        <Col span="3"><span>接班时间</span></Col>
                        <Col span="6"><span>{{ msg.takeoverTime }}</span></Col>
                    </Row>
                </div>
            </Form>
            <div slot="footer">

                <Button class="save" @click="closeRegister">关 闭</Button>
                <Button type="primary" v-if="isAdd" class="save" @click="submitForm">确 定</Button>
            </div>
        </Modal>
    </div>
</template>
<script>
import { mapActions } from 'vuex'
import { sDataGrid } from 'sd-data-grid'
import Cookies from "js-cookie";
import { formatDateparseTime, getUserCache } from "@/libs/util.js"
export default {
    name: 'takeOver',
    components: {
        sDataGrid
    },
    data() {
        return {
            openRegister: false,
            rules: {
                takeoverTotalPersons: [
                    { required: true, message: '请输入接班实际人数', trigger: 'blur' },
                    {
                        validator: (rule, value, callback) => {
                            if (isNaN(Number(value))) {
                                callback(new Error('请输入数字'));
                            }
                            else {
                                callback();
                            }
                        },
                        trigger: 'blur'
                    }
                ],
                takeoverPerson: [
                    { required: true, message: '请输入接班人', trigger: 'blur' }
                ],
                takeoverTime: [
                    { required: true, message: '请选择接班时间' }
                ]

            },
            columns1: [
                {
                    title: '序号',
                    type: 'index',
                    width: 70,
                    align: 'center'
                },
                {
                    title: '登记人',
                    key: 'operatorXm',
                    align: 'center',
                },
                {
                    title: '登记时间',
                    key: 'operatorTime',
                    align: 'center',
                    width: 200
                },
                {
                    title: '登记类型',
                    // key: 'recordTypeName',
                    slot: 'recordType',
                    align: 'center',
                },
                {
                    title: '登记内容',
                    key: 'recordContent',
                    align: 'center',
                },
                {
                    title: '监室号',
                    key: 'roomIdName',
                    align: 'center',
                },
                {
                    title: '登记来源',
                    key: 'todoSourceName',
                    align: 'center',
                },
            ],
            PersonDistributionLsit: {},
            roomList: [],
            registerData: [],
            formData: {
                takeoverPerson: getUserCache.getUserName(),
                takeoverPersonSfzh: getUserCache.getIdCard(),
                takeoverTime: '',
                takeoverTotalPersons: '',
                id: ''
            },
            msg: {},
            isAdd: true,
        }
    },
    mounted() {
    },
    methods: {
        ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
        handleRegister(row) {
            this.getData(row.id)
            this.formData.takeoverTime = new Date()
            this.openRegister = true
            this.formData.id = row.id
            this.isAdd = true
        },
        handleDetails(row) {
            this.getData(row.id)
            this.formData.takeoverTime = new Date()
            this.openRegister = true
            this.isAdd = false
        },
        getRegisterData() {
            this.authGetRequest({ url: this.$path.represent_getPatrolRecordList, params: {} }).then(res => {
                if (res.success) {
                    this.registerData = res.data
                }
            }
            )
        },
        getData(id) {
            this.authGetRequest({ url: this.$path.represent_shiftHandoverGet, params: { id: id } }).then(res => {
                if (res.success) {
                    this.roomList = JSON.parse(res.data.zdgzry)
                    this.PersonDistributionLsit = JSON.parse(res.data.ryqk)
                    this.msg = res.data
                    this.registerData = res.data.xkdjList
                }
            }
            )
        },
        closeRegister() {
            this.openRegister = false
            this.formData = {
                takeoverPerson: getUserCache.getUserName(),
                takeoverPersonSfzh: getUserCache.getIdCard(),
                takeoverTime: '',
                takeoverTotalPersons: '',
                id: ''
            }
            this.msg = {}
            this.PersonDistributionLsit = {}
            this.roomList = []
            this.registerData = []
            this.$refs.grid.query_grid_data(1)
        },
        submitForm() {
            this.$refs.formData.validate(valid => {
                if (valid) {
                    console.log("start", this.formData);
                    const params = { ...this.formData };
                    params.takeoverTime = formatDateparseTime(this.formData.takeoverTime)
                    this.authPostRequest({ url: this.$path.represent_successionReg, params: params }).then(res => {
                        if (res.success) {
                            this.$Message.success('成功接班')
                            this.closeRegister()
                        } else {
                            this.$Message.error(res.message)
                        }

                    }
                    )
                }
            })

        }
    }
}

</script>

<style scoped lang="less">
.person-list {
    display: flex;
    gap: 20px;
    margin-bottom: 16px;
    justify-content: space-between;

    .itme {
        border: 1px solid #dcdfe6;
        width: 80px;
        height: 80px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        border-radius: 2px;

        .title {
            color: #1F2533;
            font-size: 16px;
        }

        .num {
            font-size: 16px;
            color: #4b81ff;
        }
    }
}

.personImg-list {
    display: flex;
    gap: 20px;
    margin-bottom: 16px;

    .personImg-item {
        width: 70px;

        .img {
            width: 70px;
            height: 90px;
        }

        .name {
            text-align: center;
        }

    }


    .prisoner-list::-webkit-scrollbar {
        // width: 1px;
        height: 5px;
    }

    .prisoner-list::-webkit-scrollbar-thumb {
        background-color: #ddd;
        border-radius: 5px;
    }

    .prisoner-list {
        display: flex;
        overflow: auto;
        gap: 15px;
        padding: 10px 0;
        margin: 0 10px;

        .item {
            .item-top {
                width: 82px;
                height: 110px;
                // position: relative;

                .item-img {
                    width: 82px;
                    height: 110px;
                    // display: block;
                }

                .item-name {
                    position: relative;
                    top: -24px;
                    height: 24px;
                    display: inline-block;
                    background: #e9f0ff;
                    border-radius: 0 0 4px 4px;
                    color: #4b81ff;
                    width: 100%;
                    text-align: center;
                }
            }

            .ms {
                text-align: center;
                font-size: 12px;
                margin-top: 5px;
            }
        }
    }

}

.person-table {
    margin-bottom: 16px;
}


.show-form {
    .item {
        line-height: 1;
        padding: 10px 0;
        font-size: 16px;
    }
}
</style>