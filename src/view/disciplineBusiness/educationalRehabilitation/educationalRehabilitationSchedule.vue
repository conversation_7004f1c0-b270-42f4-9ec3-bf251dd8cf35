<template>
    <div style="width: 100%;height: 100%;">
        <div class="table-container" v-if="tableContainer">
            <s-DataGrid ref="grid1" funcMark="kfjykb" :customFunc="true">
                <!-- 设备报修登记 -->
                <template slot="customHeadFunc" slot-scope="{ func }">
                    <Button type="primary" icon="ios-add" v-if="func.includes(globalAppCode + ':kfjykb:add')"
                        @click.native="handleAddKb('add')">创建课表</Button>
                </template>
                <template slot="customRowFunc" slot-scope="{ func, row, index }">
                    <Button type="primary" style="margin-left: 10px;"
                        v-if="func.includes(globalAppCode + ':kfjykb:sp') && row.status == '01'"
                        @click.native="handleSpKb(index, row)">审批</Button>
                    <Button style="margin-left: 10px;"
                        v-if="func.includes(globalAppCode + ':kfjykb:xq') && row.status != '01'"
                        @click.native="handleXqKb(index, row)">详情</Button>
                </template>
            </s-DataGrid>
        </div>
        <div v-if="handleAddKbContainer" class="handle-add-kb-container">
            <div class="header-container">
                <div class="left-kb-name">
                    <!-- <span>课表名称</span> -->
                    <Form ref="formKb" :model="formValidate" :label-width="120">
                        <FormItem label="课表名称：" prop="planName"
                            :rules="{ required: true, message: '课表名称不能为空', trigger: 'blur' }">
                            <Input v-model="formValidate.planName" style="width: 350px;"></Input>
                        </FormItem>
                    </Form>
                </div>
                <div class="kb-time-through">
                    <Button>
                        <Icon type="ios-arrow-back" />
                        上一周
                    </Button>
                    <DatePicker type="daterange" v-model="dateThrough" placement="bottom-end" placeholder="选择日期"
                        style="width: 200px;margin: 0 10px;" />
                    <Button>
                        下一周
                        <Icon type="ios-arrow-forward" />
                    </Button>
                </div>
                <div class="setting-kb-time-through">

                    <Button @click="handleAddTimeSet">
                        课表时段管理
                        <Icon type="md-settings" />
                    </Button>
                </div>
                <Modal v-model="addTimeSet" :mask-closable="false" :closable="false" class-name="select-use-modal"
                    width="900" title="新增课程">
                    <Form ref="form" :model="formDynamic" :label-width="120">
                        <div class="cover-time-set">
                            <template v-for="(item, index) in formDynamic.items">
                                <FormItem v-if="item.status" :key="index" label="课程时段"
                                    :prop="'items.' + index + '.time'"
                                    :rules="{ required: true, type: 'array', message: '课程时段不能为空', trigger: 'blur' }">
                                    <Row>
                                        <Col span="18">
                                        <TimePicker v-model="item.time" format="HH:mm" type="timerange"
                                            placement="bottom-end" style="width: 560px" />
                                        <!-- <DatePicker type="daterange" v-model="item.time"  placement="bottom-end" style="width: 560px" /> -->
                                        </Col>
                                        <Col span="4" offset="1" v-if="index > 0">
                                        <Icon type="ios-trash-outline" @click="handleRemove(index)" />
                                        </Col>
                                    </Row>
                                </FormItem>
                            </template>
                        </div>

                        <FormItem>
                            <Row>
                                <Col span="18">
                                <Button type="dashed" long @click="handleAdd" icon="md-add">新增</Button>
                                </Col>
                            </Row>
                        </FormItem>
                    </Form>
                    <div slot="footer">
                        <Button type="primary" @click="handleSumbit('form')" class="save">确 定</Button>
                        <Button @click="handleCancel('form')" class="save">关 闭</Button>
                    </div>
                </Modal>
            </div>

            <div class="content-container">
                <div class="left-add-kb-type">
                    <div class="kb-type">课程类型</div>
                    <div class="search-kb-type">
                        <Input placeholder="评估课程" style="width: auto">
                        <template #suffix>
                            <Icon type="ios-search" />
                        </template>
                        </Input>
                    </div>
                    <div class="draggle-container-kb-type">
                        <div class="add-input">
                            <Input v-model="form.coursesName" placeholder="输入新课程名称，添加课程" />
                            <Button @click="isOpen = true">
                                <Icon type="md-add" />
                                添加课程
                            </Button>
                        </div>
                        <div class="draggle-container">
                            <draggable id="left-draggable" :list="isAddKbList"
                                :group="{ name: 'eduList', pull: 'clone', put: false }" :move="checkMove"
                                drag-class="dragging-item" ghost-class="ghost-item" :touch-start-threshold="5"
                                @choose="onChoose" animation="200" @start="onDragStart" @end="onDragEnd"
                                @dragstart="dragstart">
                                <div v-for="(item, index) in isAddKbList" :key="index" class="kb-list-cover-container">
                                    <div :style="{ background: item.coursesColor }">{{ item.coursesName }}</div>
                                </div>
                            </draggable>
                            <addCourseModal :isShowhandleModal="isOpen" :form="form" @updateTable="updateTable" />
                        </div>

                    </div>
                </div>
                <div class="right-table" style="margin-bottom: 50px;">
                    <table>
                        <thead>
                            <tr class="fixed-header">
                                <th style="height: 46px;line-height: 46px;">时段 日期</th>
                                <th colspan="3" v-for="(dateItem, index) in newDatesWithDay" :key="index"
                                    style="text-align: center;">
                                    <p v-if="dateItem.day" style="font-size: larger;font-weight: 700;color: #555;">
                                        {{ dateItem.day }}
                                    </p>
                                    <p v-if="dateItem.date" style="font-size: smaller;font-weight: 300;color: #7f7f7f;">
                                        {{ dayjs(dateItem.date).format('MM-DD') }}
                                    </p>
                                </th>
                            </tr>
                        </thead>
                        <!-- 数据行：时间段作为首列 -->
                        <tbody>
                            <template v-if="newAreaList1.length > 0">
                                <tr >
                                    <td style="background: #f59a23;color: #fff;height: 80px;">
                                        <p>戒区</p>
                                    </td>
                                    <td style="height: 80px;" v-for="(item, idx) in newAreaList1" :key="idx">
                                        <p style="width: 120px;">{{ item.areaName }}</p>
                                        <Button size="small" v-if="idx > 2" :disabled="copyDayFlag(idx)"
                                            @click="copyDay(idx)">
                                            <Icon type="md-copy" />
                                            复制昨日
                                        </Button>
                                    </td>
                                </tr>
                            </template>
                            <template v-if="areaWithTimeSoltList1.length > 0">
                                <tr v-for="(rowItem, rowIndex) in areaWithTimeSoltList1" :key="rowItem.id">
                                    <td class="fixed-col" style="background-color: #7991c8;color: #fff;">{{
                                        rowItem.timeSlotCode }}</td>
                                    <td v-for="(item, index) in rowItem.areaList" class="dish-cell" :class="{
                                        // 'drop-zone-active': isDragOver && !isDragOverInvalid,
                                        'drop-zone-invalid': isDragOverInvalid,
                                        // 'has-dishes': item.cooks && item.cooks.length > 0
                                    }" :key="`${rowIndex}-${index}-${item.id}`">
                                        <draggable :list="item.coursesNameList" :options="options"
                                            :key="`${rowIndex}-${index}-${rowIndex}`" animation="200"
                                            ghost-class="ghost-item" @start="onDragStartTab" @end="onDragEndTab"
                                            @change="onChange" @dragstart="dragstart"
                                            @dragover.native.prevent="onDragOver" @dragleave.prevent.stop="onDragLeave"
                                            @drop.native.prevent.stop="onDrop" :move="checkMove2"
                                            @add="(evt) => onDishAdded(evt, rowIndex, index)" class="drop-zone-full"
                                            :data-cell-id="`cell-${rowIndex}-${index}`"
                                            v-if="item && item.coursesNameList !== undefined">
                                            <div class="dish-item" v-for="(info, idx) in (item.coursesNameList || [])"
                                                :key="`${rowIndex}-${index}-${item.id}-${idx}`">
                                                <div class="dish-item-content">
                                                    <div class="dish-item-name"
                                                        :style="{ background: info.coursesColor, width: '60px', margin: '5px', padding: '5px', fontSize: '12px', borderRadius: '2px' }"
                                                        :title="info.coursesName">
                                                        <span>
                                                            {{ info.coursesName }}
                                                        </span>
                                                        <Tooltip content="移除课程" placement="bottom">
                                                            <Icon type="md-close" class="remove-dish-icon"
                                                                @click="delSub(rowIndex, index, idx)" />
                                                        </Tooltip>
                                                    </div>

                                                </div>
                                            </div>
                                        </draggable>
                                    </td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="bsp-base-fotter">
                <Button @click="handleResetKb" style="margin-right: 10px;">取消</Button>
                <Button type="primary" @click="handleCreatedKb('formKb')">确认</Button>
            </div>
        </div>
        <div v-if="detailContainer" class="handle-detail-kb-container">
            <header class="header-container">
                <div>
                    <h2>{{ detailInfo.planName }}</h2>
                    <span>{{ detailInfo.statusName }}</span>
                </div>
                <ul>
                    <li>
                        <span>课表时间：</span>
                        <span>{{ kbsj }}</span>
                    </li>
                    <li>
                        <span>创建人：</span>
                        <span>{{ detailInfo.addUserName }}</span>
                    </li>
                    <li>
                        <span>创建时间：</span>
                        <span>{{ detailInfo.addTime }}</span>
                    </li>
                </ul>
            </header>
            <div class="detail-table">
                <table>
                    <!-- 表头：日期行 -->
                    <thead>
                        <tr>
                            <th style="height: 46px;line-height: 46px;">时段 日期</th>
                            <th colspan="3" v-for="(dateItem, index) in newDatesWithDay" :key="index"
                                style="text-align: center;">
                                <p v-if="dateItem.day" style="font-size: larger;font-weight: 700;color: #555;">
                                    {{ dateItem.day }}
                                </p>
                                <p v-if="dateItem.date" style="font-size: smaller;font-weight: 300;color: #7f7f7f;">
                                    {{ dayjs(dateItem.date).format('MM-DD') }}
                                </p>
                            </th>
                        </tr>
                    </thead>
                    <!-- 数据行：时间段作为首列 -->
                    <tbody>
                        <template v-if="newAreaList.length > 0">
                            <tr>
                                <td style="background: #f59a23;color: #fff;">
                                    <p>戒区</p>
                                </td>
                                <td style="height: 80px;" v-for="(item, idx) in newAreaList" :key="idx">
                                    <p style="width: 120px;">{{ item.areaName }}</p>
                                </td>
                            </tr>
                        </template>
                        <!--    -->
                        <template v-if="newEdurehabList.length > 0">
                            <tr v-for="(rowItem, rowIndex) in newEdurehabList" :key="rowIndex">
                                <td style="background-color: #7991c8;color: #fff;">{{ rowItem.timeSlotCode }}</td>
                                <td v-for="(item, index) in rowItem.areaList" :key="index">
                                    <div class="dish-item" v-for="(info, idx) in (item.coursesNameList || [])"
                                        :key="idx">
                                        <div class="dish-item-content">
                                            <div class="dish-item-name"
                                                :style="{ background: info.coursesColor, width: '60px', margin: '5px', padding: '5px', fontSize: '12px', borderRadius: '2px' }"
                                                :title="info.coursesName">{{ info.coursesName }}
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        </template>
                    </tbody>
                </table>
            </div>
            <div class="bsp-base-fotter">
                <Button @click="handleBack" style="margin-right: 10px;">返回</Button>
            </div>
        </div>
        <Modal v-model="spModal" :mask-closable="false" :closable="false" class-name="select-use-modal" width="900"
            title="课程审批">
            <Form ref="formSp" :model="formSp" :label-width="120">
                <FormItem label="课表时间：" prop="coursesName">
                    {{ kbsj }}
                </FormItem>
                <FormItem label="审批结果：" prop="status"
                    :rules="{ required: true, message: 'Please select gender', trigger: 'change' }">
                    <RadioGroup v-model="formSp.status">
                        <Radio label="04">通过</Radio>
                        <Radio label="02">不通过</Radio>
                    </RadioGroup>
                </FormItem>
                <FormItem label="审批意见：" prop="approvelComment"
                    :rules="{ required: true, message: '请输入课程名称', trigger: 'blur' }">
                    <Input v-model="formSp.approvelComment" placeholder="请输入审批意见" type="textarea"
                        :autosize="{ minRows: 2, maxRows: 5 }">
                    </Input>
                </FormItem>
            </Form>
            <div slot="footer">
                <Button type="primary" @click="handleSpSumbit('formSp')" class="save">确 定</Button>
                <Button @click="handleSpCancel('formSp')" class="save">关 闭</Button>
            </div>
        </Modal>

    </div>
</template>

<script>
import { sDataGrid } from 'sd-data-grid'
import { mapActions, mapState } from 'vuex'
import draggable from "vuedraggable";
import addCourseModal from './addCourseModal.vue';
export default {
    name: "educationalRehabilitationSchedule",
    data() {

        return {
            options: {
                group: {
                    name: 'eduList',//组名为itxst
                    pull: false,//'clone',
                    put: true,
                },
                setData: this.setData()
            },
            tableContainer: true,
            handleAddKbContainer: false,
            detailContainer: false,
            handleModal: false,
            spModal: false,
            addkcName: "",
            dateThrough: [],
            isAddKbList: [],
            isOpen: false,
            form: {
                coursesColor: "",
                coursesName: "",
                isEnabled: 1
            },
            formSp: {
                approvelComment: "",
                status: "",
            },
            kbsj: "",
            addTimeSet: false,
            index: 1,
            formDynamic: {
                items: [
                    {
                        time: [],
                        index: 1,
                        status: 1
                    }
                ]
            },
            datesWithDay: [],
            timeSlotData: [],
            areaList: [],
            newAreaList: [],
            newAreaList1: [],
            newDatesWithDay: [],
            areaWithTimeSoltList: [],
            areaWithTimeSoltList1: [],
            drupAreaWithTimeSoltList: [],
            detailInfo: {},
            rowId: "",
            newEdurehabList: [],
            isDragOverInvalid: false,
            listRecord: [],
            formValidate: {
                planName: ""
            }
        }

    },

    methods: {
        ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
        setData(data) {
            console.log(data, '1212')
        },
        handleAddKb() {
            this.handleAddKbContainer = true
            this.tableContainer = false
        },
        handleResetKb() {
            this.handleAddKbContainer = false
            this.tableContainer = true
        },
        updateTable() {
            this.isOpen = false
            this.handleGetedurehabCoursesList()
        },
        handleSpKb(idx, row) {
            this.rowId = row.id
            this.kbsj = row.kbsj

            this.formSp.status = "04"
            this.formSp.approvelComment = "同意"
            this.spModal = true
        },
        mergeCoursesData(arrList) {
            // 使用 flatMap 进行三层数据提取和重组
            return arrList.flatMap(timeSlot =>


                timeSlot.areaList.flatMap(area =>


                    area.coursesNameList.map(course => {
                        // 解构课程对象，排除 id 字段
                        const { id, ...courseWithoutId } = course;
                        return {
                            // 时间槽层级数据
                            timeSlotCode: timeSlot.timeSlotCode,
                            timeSlotStartTime: timeSlot.startTime,
                            timeSlotEndTime: timeSlot.endTime,
                            coursesDate: area.date,
                            // 区域层级数据
                            areaId: area.areaId,
                            areaName: area.areaName,
                            // 课程数据（不含 id）
                            ...courseWithoutId
                        };
                    })
                )
            );
        },
        handleCreatedKb(name) {

            this.$refs[name].validate((valid) => {
                if (valid) {
                    let listRecord = this.mergeCoursesData(this.areaWithTimeSoltList1)
                    let params = {
                        endDate: this.dateThrough[1],
                        startDate: this.dateThrough[0],
                        planName: this.formValidate.planName,
                        listRecord,
                        jqAreaVOList: this.areaList,
                        timeList: this.timeSlotData,
                        id: ""
                    }

                    if (listRecord.length > 0) {
                        this.authPostRequest({ url: this.$path.edurehabCoursesPlan_create, params }).then(res => {
                            if (res.success) {
                                this.$Message.success("创建成功")
                                this.handleAddKbContainer = false
                                this.tableContainer = true
                                this.areaWithTimeSoltList1 = []
                            }
                        })
                    } else {
                        this.$Message.error("请创建课表后进行保存")
                    }
                } else {
                    this.$Message.error('验证未通过');
                }
            })
        },
        onDragStartTab() {
            console.log(this.areaWithTimeSoltList, 'this.areaWithTimeSoltListonDragStartTab')

        },
        onChoose(e, value) {
            console.log(e, value, 'this.e,value')
        },
        onChange(e, value) {
            console.log(e, value, 'this.e,value')
        },
        onDragEndTab() {
            console.log(this.areaWithTimeSoltList, 'this.areaWithTimeSoltListonDragStartTab2')

        },
        onDragStart() {
            //   console.log(this.areaWithTimeSoltList,'this.areaWithTimeSoltList')

        },
        dragstart(data, value) {
            console.log(data, value, 'dragstart')
        },
        onDragEnd() {
        },
        copyDayFlag() { },
        // 验证 3 6 9 
        isInFixedSequence(x, start, difference) {
            // 计算位置索引 n = (x - start)/difference + 1
            const n = (x - start) / difference + 1;
            // 验证：n是正整数且公差匹配
            return n >= 1 && Number.isInteger(n) && (x - start) % difference === 0;
        },
        // 验证  4 7 10
        isInDynamicSequence(x, start, difference, maxLength = 100) {
            // 动态生成数列：a1, a1+d, a1+2d, ..., 直至maxLength
            const sequence = Array.from({ length: maxLength }, (_, i) =>
                start + i * difference
            );
            return sequence.includes(x);
        },
        // 验证  5 8 11
        isInSequence5_8_11(x) {
            const start = 5;      // 首项
            const difference = 3; // 公差

            // 1. 检查是否满足通项公式：x = start + n * difference (n为正整数)
            const steps = (x - start) / difference;

            // 2. 验证条件：
            //    - steps 是非负整数（n ≥ 0）
            //    - 差值能被公差整除（避免浮点数误差）
            return (
                steps >= 0 &&
                Number.isInteger(steps) &&
                (x - start) % difference === 0
            );
        },
        copyDay(index) {
            let is369 = this.isInFixedSequence(index + 1, 3, 3)
            let is4710 = this.isInDynamicSequence(index + 1, 4, 3)
            let is5811 = this.isInSequence5_8_11(index + 1)

            for (let i = 0; i < this.areaWithTimeSoltList1.length; i++) {
                const item = this.areaWithTimeSoltList1[i];
                if (is4710 && index + 1 == 4) {
                    item.areaList[index].coursesNameList = Object.assign([], item.areaList[0].coursesNameList)
                }

                if (is4710 && index + 1 > 4) {
                    item.areaList[index].coursesNameList = Object.assign([], item.areaList[index - 3].coursesNameList)
                }

                if (is5811 && index + 1 == 5) {
                    item.areaList[index].coursesNameList = Object.assign([], item.areaList[1].coursesNameList)
                }

                if (is5811 && index + 1 > 5) {
                    item.areaList[index].coursesNameList = Object.assign([], item.areaList[index - 3].coursesNameList)
                }

                if (is369 && index + 1 == 6) {
                    item.areaList[index].coursesNameList = Object.assign([], item.areaList[2].coursesNameList)
                }

                if (is369 && index + 1 > 6) {
                    item.areaList[index].coursesNameList = Object.assign([], item.areaList[index - 3].coursesNameList)
                }

            }
        },
        checkMove(evt) {
            if (evt.to.id === "left-draggable") {
                return false;
            }
            // 检查是否重复菜品 - 在拖拽过程中进行检查
            if (evt.draggedContext && evt.relatedContext) {
                const draggedItem = evt.draggedContext.element;
                const targetList = evt.relatedContext.list;

                if (draggedItem && targetList) {

                    // 检查目标列表中是否已存在相同菜品
                    const isDuplicate = targetList.some(item =>
                        (item.coursesCode && draggedItem.coursesCode && item.coursesCode === draggedItem.coursesCode) ||
                        (item.coursesColor && draggedItem.coursesColor && item.coursesColor === draggedItem.coursesColor) ||
                        (item.id && draggedItem.id && item.id === draggedItem.id)
                    );

                    if (isDuplicate) {
                        // 设置无效拖拽状态，用于显示红色边框
                        this.isDragOverInvalid = true;
                        return false;
                    } else {
                        // 重置无效状态
                        this.isDragOverInvalid = false;
                    }
                }
            }

            return true;
        },
        checkMove2(evt) {
            // 防止拖拽回左侧菜品列表

        },
        onDishAdded(evt, rowIndex, cellIndex) {
            // 获取拖入的数据（深拷贝避免引用污染）
            let timeSlotInfo = this.areaWithTimeSoltList1[rowIndex]
            let areaInfo = this.areaWithTimeSoltList1[rowIndex].areaList[cellIndex]
            let newItem = JSON.parse(JSON.stringify(evt.item._underlying_vm_));
            newItem['startTime'] = timeSlotInfo.startTime
            newItem['endTime'] = timeSlotInfo.endTime
            newItem['timeSlotCode'] = timeSlotInfo.timeSlotCode
            newItem['areaId'] = areaInfo.areaId
            newItem['areaName'] = areaInfo.areaName
            delete newItem.id


            // this.record  .push(newItem)

            // console.error(...this.areaWithTimeSoltList1[rowIndex].areaList[cellIndex].coursesNameList, '...this.areaWithTimeSoltList1[rowIndex].areaList[cellIndex]');
            // console.error(this.areaWithTimeSoltList1[rowIndex].areaList[cellIndex]);

            // this.$set(
            //     this.areaWithTimeSoltList1[rowIndex].areaList[cellIndex],
            //     'coursesNameList',
            //     Object.assign([], [newItem])
            // );
            // console.error(this.areaWithTimeSoltList1);

            // 阻止默认整列更新
            // evt.preventDefault();
            // evt.stopPropagation();
        },
        onDragOver(evt) {
            evt.preventDefault();
            evt.stopPropagation();
        },
        onDragLeave(evt) {
            evt.preventDefault();
            evt.stopPropagation();
        },
        onDrop(evt) {
            evt.preventDefault();
            evt.stopPropagation();
        },
        handleGetedurehabCoursesList() {
            let params = {
                "coursesCode": "",
                "coursesColor": "",
                "coursesName": "",
                "isEnabled": 1,
                "orgCode": this.$store.state.common.orgCode
            }
            this.authPostRequest({ url: this.$path.edurehabCourses_list, params }).then(res => {
                if (res.success) {
                    this.isAddKbList = res.data
                }
            })
        },
        handleAddTimeSet() {
            this.addTimeSet = true
            if (this.timeSlotData && this.timeSlotData.length > 0) {
                this.formDynamic.items = []
                this.timeSlotData.forEach((ele, index) => {
                    ele.status = 1
                    ele.index = Number(index + 1)
                    ele.time = [ele.startTime, ele.endTime]
                    this.formDynamic.items.unshift(ele)
                })
            }
        },
        handleAdd() {
            this.index++;
            this.formDynamic.items.push({
                time: [],
                index: this.index,
                status: 1,
            });
        },
        handleRemove(index) {
            this.formDynamic.items[index].status = 0;
        },
        handleSumbit(name) {
            this.$refs[name].validate((valid) => {
                if (valid) {
                    this.timeList = this.formDynamic.items.map(item => {
                        return {
                            startTime: item.time[0],
                            endTime: item.time[1],
                        }
                    })
                    this.authPostRequest({ url: this.$path.edurehabTimeSlot_batchCreate, params: this.timeList }).then(res => {
                        if (res.success) {
                            this.addTimeSet = false
                            this.handleGetPlanAreaList()
                            this.$refs[name].resetFields();
                            this.handleGetTimeList()
                            this.$Message.success('添加成功')

                        } else {
                            this.$Message.error(res.message)
                        }
                    })
                } else {
                    this.$Message.error('验证未通过');
                }
            })
        },
        handleCancel(name) {
            this.addTimeSet = false
            this.$refs[name].resetFields();
        },
        handleSpCancel(name) {
            this.$refs[name].resetFields();
            this.spModal = false
        },
        handleSpSumbit(name) {
            this.$refs[name].validate((valid) => {
                if (valid) {
                    this.authPostRequest({ url: this.$path.edurehabCoursesPlan_approval, params: { approvelComment: this.formSp.approvelComment, status: this.formSp.status, id: this.rowId } }).then(res => {
                        if (res.success) {
                            this.spModal = false
                            this.rowId = ""
                            this.$refs[name].resetFields();
                            this.$refs.grid1.query_grid_data(1)
                            this.$Message.success('审批成功')
                        } else {
                            this.$Message.error(res.message)
                        }
                    })
                } else {
                    this.$Message.error('验证未通过');
                }
            })
        },
        handleGetTimeList() {
            this.authPostRequest({ url: this.$path.edurehabTimeSlot_list, params: {} }).then(res => {
                if (res.success) {
                    this.timeSlotData = res.data
                }
            })
        },
        handleGetPlanTime() {
            this.authGetRequest({ url: this.$path.edurehabCoursesPlan_getPlanTime, params: { orgCode: this.$store.state.common.orgCode } }).then(res => {
                if (res.success) {
                    const { startDateStr, endDateStr } = res.data
                    this.dateThrough[0] = startDateStr
                    this.dateThrough[1] = endDateStr
                    this.datesWithDay = this.getDatesWithDays(startDateStr, endDateStr)
                }
            })
        },
        getDatesWithDays(startDate, endDate) {
            // 1. 初始化日期对象并验证
            const start = this.dayjs(startDate).startOf('day');
            const end = this.dayjs(endDate).endOf('day');

            if (start.isAfter(end)) {
                throw new Error('开始日期不能晚于结束日期');
            }

            // 2. 星期映射表（中文）
            const weekdays = [
                '星期日', '星期一', '星期二',
                '星期三', '星期四', '星期五', '星期六'
            ];

            // 3. 遍历日期范围
            const result = [];
            let current = start;

            while (current.isBefore(end) || current.isSame(end, 'day')) {
                result.push({
                    date: current.format('YYYY-MM-DD'),
                    day: weekdays[current.day()]  // 0-6转中文星期
                });
                current = current.add(1, 'day');
            }

            return result;
        },

        handleGetPlanAreaList() {
            this.authGetRequest({ url: this.$path.edurehabCoursesPlan_getPlanArea, params: { orgCode: this.$store.state.common.orgCode } }).then(res => {
                if (res.success) {
                    this.areaList = res.data;
                    this.newAreaList = Array.from({ length: this.datesWithDay.length }, () => [...this.areaList]).flat();
                    this.newAreaList1 = JSON.parse(JSON.stringify(this.newAreaList))
                    this.areaWithTimeSoltList = this.combineScheduleData(this.timeSlotData, this.newAreaList, this.datesWithDay)

                    this.areaWithTimeSoltList1 = JSON.parse(JSON.stringify(this.areaWithTimeSoltList))
                    // console.error(this.areaWithTimeSoltList1);

                }
            })
        },
        combineScheduleData(test1, test2, dates) {
            // 1. 生成基础区域列表（21项：7组×3区域）
            const baseAreaList = Array(1).fill().flatMap(() =>
                test2.map(area => ({
                    areaId: area.areaId,
                    areaName: area.areaName,
                    timeSlotList: null,
                    coursesNameList: []
                }))
            );

            // 2. 每3项合并一个日期（共7组日期）
            const mergedList = baseAreaList.map((item, index) => {
                // 计算当前项所属的日期组索引（0-6）
                const groupIndex = Math.floor(index / 3);
                // 获取当前组对应的日期
                const dateValue = dates[groupIndex]?.date || null;

                // 为每项添加日期属性
                return {
                    ...item,
                    date: dateValue
                };
            });

            // 3. 组合最终数据结构
            return test1.map(timeSlot => ({
                id: timeSlot.id,
                timeSlotId: timeSlot.id,
                timeSlotCode: timeSlot.timeSlotCode,
                startTime: timeSlot.startTime,
                endTime: timeSlot.endTime,
                areaList: [...mergedList] // 为每个时间段创建独立副本
            }));
        },
        // combineScheduleData(test1, test2, dates) {
        //     // 1. 生成基础区域列表（21项：7组×3区域）
        //     const baseAreaList = Array(1).fill().flatMap(() =>
        //         test2.map(area => ({
        //             areaId: area.areaId,
        //             areaName: area.areaName,
        //             timeSlotList: null,
        //             coursesNameList: []
        //         }))
        //     );

        //     // 2. 每3项合并一个日期（共7个日期）
        //     const mergedList = [];
        //     for (let i = 0; i < baseAreaList.length; i += 3) {
        //         // 获取当前组对应的日期（每3项对应一个日期）                
        //         const dateObj = dates[Math.floor(i / 3)];                

        //         // 前两项保持原样
        //         mergedList.push(baseAreaList[i]);
        //         mergedList.push(baseAreaList[i + 1]);

        //         // 第三项合并日期属性
        //         mergedList.push({
        //             ...baseAreaList[i + 2],  // 保留原区域属性
        //             date: dateObj?.date       // 注入日期属性
        //         });
        //     }

        //     // 3. 组合最终数据结构
        //     return test1.map(timeSlot => ({
        //         id: timeSlot.id,
        //         timeSlotId: timeSlot.id,
        //         timeSlotCode: timeSlot.timeSlotCode,
        //         startTime: timeSlot.startTime,
        //         endTime: timeSlot.endTime,
        //         areaList: [...mergedList] // 深拷贝避免数据污染
        //     }));
        // },

        // combineScheduleData(test1, test2, dates) {
        //     // 创建重复7次的基础区域列表（每组包含test2的所有区域）
        //     const baseAreaList = Array(1).fill().flatMap(() =>
        //         test2.map(area => ({
        //             areaId: area.areaId,
        //             areaName: area.areaName,
        //             timeSlotList: null,
        //             coursesNameList: [] // 确保每个区域都有空数组
        //         }))
        //     );
        //     console.error(baseAreaList, '[baseAreaList]');


        //     // 组合最终数据结构
        //     return test1.map(timeSlot => ({
        //         id: timeSlot.id++,
        //         timeSlotId: timeSlot.id,
        //         timeSlotCode: timeSlot.timeSlotCode,
        //         startTime: timeSlot.startTime,
        //         endTime: timeSlot.endTime,
        //         areaList: [...baseAreaList] // 为每个时间段创建独立副本
        //     }));
        // },
        delSub(menuIndex, index, idx) {
            this.areaWithTimeSoltList1[menuIndex].areaList[index].coursesNameList.splice(idx, 1);
            this.$Message.success('课程已移除');
        },
        // 将后端返回的详情数据进行重新整合
        transformToArrList(edurehabList) {
            // 1. 创建时间段索引映射表
            const timeSlotMap = new Map();

            // 2. 遍历原始数据结构
            edurehabList.forEach(dateGroup => {
                dateGroup.jqAreaVOList.forEach(area => {
                    area.timeSlotList.forEach(timeSlot => {
                        const key = `${timeSlot.timeSlotCode}-${timeSlot.startTime}-${timeSlot.endTime}`;

                        // 3. 初始化时间段结构
                        if (!timeSlotMap.has(key)) {
                            timeSlotMap.set(key, {
                                id: this.generateUniqueId(), // 生成唯一ID的方法
                                timeSlotCode: timeSlot.timeSlotCode,
                                startTime: timeSlot.startTime,
                                endTime: timeSlot.endTime,
                                areaList: []
                            });
                        }

                        // 4. 处理课程数据
                        const coursesNameList = timeSlot.listRecord
                            // .filter(record => record.coursesCode) // 过滤有效课程
                            .map(record => ({
                                id: record.id,
                                coursesCode: record.coursesCode,
                                coursesName: record.coursesName,
                                coursesColor: record.coursesColor
                            }));

                        // 5. 构建区域数据
                        const areaObj = {
                            areaId: area.areaId,
                            areaName: area.areaName,
                            timeSlotList: null, // 按目标结构设为null
                            coursesNameList
                        };

                        // 6. 添加到时间段
                        timeSlotMap.get(key).areaList.push(areaObj);
                    });
                });
            });
            // 7. 返回最终数组
            return Array.from(timeSlotMap.values());
        },
        generateUniqueId() {
            return `id_${Date.now()}_${Math.floor(Math.random() * 10000)}`;
        },
        handleXqKb(idx, row) {
            this.kbsj = row.kbsj
            this.authGetRequest({ url: this.$path.edurehabCoursesPlan_get, params: { id: row.id } }).then(res => {
                if (res.success) {
                    this.detailInfo = res.data
                    this.newEdurehabList = this.transformToArrList(res.data.edurehabList)
                    this.tableContainer = false
                    this.detailContainer = true
                }
            })
        },
        handleBack() {
            this.detailContainer = false
            this.tableContainer = true
        },
    },

    components: {
        sDataGrid,
        draggable,
        addCourseModal
    },

    created() {
        this.handleGetedurehabCoursesList()

        this.handleGetTimeList()
        this.handleGetPlanTime()
        setTimeout(() => {
            this.formValidate.planName = `戒毒人员教育康复课表(${this.dateThrough[0]} - ${this.dateThrough[1]})`
            this.handleGetPlanAreaList()
        }, 1000)
    },

    computed: {},

    watch: {
        datesWithDay: {
            handler(newVal) {
                // this.newAreaList = Array.from({ length: newVal.length }, () => [...this.areaList]).flat();
                this.newDatesWithDay = newVal.reduce((acc, curr) => {
                    acc.push(curr); // 添加当前日期对象
                    acc.push({});   // 添加空对象
                    return acc;
                }, [])
                // console.error(this.newDatesWithDay, '[this.newDatesWithDay===>>>>>>>>>this.newDatesWithDay]');

            },
        },
        areaWithTimeSoltList: {
            handler(newVal) {

            },
            immediate: true,
            deep: true
        },
        'formSp.status': {
            handler(newVal) {
                if (newVal == '02') {
                    this.formSp.approvelComment = "不同意"
                } else {
                    this.formSp.approvelComment = "同意"
                }
            }
        }
    }

}

</script>

<style scoped lang="less">
.handle-add-kb-container {
    width: 100%;
    height: 100%;
    background: #e8eaec;

    .header-container {
        display: flex;
        justify-content: space-around;
        align-items: center;
        padding: 20px;
        width: 100%;
        background: #fff;
    }

    .content-container {
        margin-top: 10px;
        width: 100%;
        height: calc(~'100% - 80px');
        background-color: #fff;
        display: flex;

        .left-add-kb-type {
            width: 358px;

            .kb-type {
                height: 30px;
                width: 100%;
                text-align: center;
                height: 44px;
                line-height: 44px;
                background-color: rgba(43, 95, 218, 1);
                font-weight: 700;
                font-style: normal;
                font-size: 14px;
                color: #FFFFFF;
            }


            .search-kb-type {
                height: 57px;
                line-height: 57px;
                text-align: center;
                background-color: rgba(242, 246, 252, 1);
            }

            .draggle-container-kb-type {
                overflow-y: auto;
                height: calc(~'100% - 150px');

                .add-input {
                    margin-top: 5px;
                    display: flex;
                    justify-content: space-between;
                }

                #left-draggable {
                    max-height: 320px;
                    display: flex;
                    flex-flow: wrap;

                    .kb-list-cover-container {
                        margin: 10px 10px;

                        div {
                            width: 150px;
                            height: 55px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            box-sizing: border-box;
                            border-width: 1px;
                            border-style: solid;
                            border-color: rgba(215, 215, 215, 1);
                            border-radius: 4px;
                        }
                    }
                }

            }

        }


    }
}

.detail-table {
    max-width: 1800px;
    max-height: 460px;
    overflow: auto;
    // margin-bottom: 30px;

    table {
        border-collapse: collapse;
        margin-left: 10px;
        margin-top: 10px;
        // table-layout: auto;
    }



    thead tr {
        background-color: #fff;
    }

    thead tr th:nth-child(2n) {
        border-right: solid 1px #ddd;
    }

    thead tr th:nth-child(2n+1) {
        display: none;
    }

    thead tr th:nth-child(1) {
        display: block;
        border-left: solid 1px #ddd;
    }

    th,
    td {
        border: 1px solid #ddd;
        text-align: center;
        width: 128px;
    }

    th {
        background-color: #f8f9fa;
        font-weight: bold;
    }

    /* 表头背景 */
    td:first-child {
        background-color: #fff;
    }
}

.right-table {
    flex: 1;
    // max-width: 900px;
    width: calc(~'100% -400px');
    overflow: auto;
    padding-bottom: 1px; /* 防止边框被裁剪 */

    table {
        border-collapse: collapse;
        margin-left: 10px;
        margin-top: 10px;
        // table-layout: auto;
    }

    .fixed-header {
        position: sticky;
        top: 0;
        height: 46px;
        /* 固定表头在顶部 */
        z-index: 5;
        /* 确保表头在顶层 */
    }

    .fixed-col {
        position: sticky;
        left: 0;
        /* 固定首列在左侧 */
        z-index: 1;
        /* 低于表头，避免覆盖 */
    }

    thead tr {
        background-color: #fff;
    }

    thead tr th:nth-child(2n) {
        border-right: solid 1px #ddd;
    }

    thead tr th:nth-child(2n+1) {
        display: none;
    }

    thead tr th:nth-child(1) {
        display: block;
        border-left: solid 1px #ddd;
    }

    th,
    td {
        border: 1px solid #ddd;
        text-align: center;
        width: 128px;
    }

    th {
        background-color: #f8f9fa;
        font-weight: bold;
    }

    /* 表头背景 */
    td:first-child {
        background-color: #fff;
    }
}

.handle-detail-kb-container {
    width: 100%;
    height: 100%;

    header {
        background: #f8f8f9;
        border-radius: 4px;
        margin: 0 10px 10px 10px;
        padding: 10px;

        div {
            display: flex;
            align-items: center;
        }

        ul {
            list-style: none;
            display: flex;
            align-items: center;

            li {
                margin-right: 20px;
            }
        }
    }
}

.dish-item-content {
    position: relative;

    .dish-item-name {
        position: relative;

        /deep/.ivu-tooltip {
            position: absolute;
            right: 0;
            top: -5px;
        }
    }

}

.dish-cell {
    &.drop-zone-invalid {
        // background: #fff2f0;
        border-color: #ed4014;

        .drop-zone-full {
            border-color: #ed4014;
            background: rgba(237, 64, 20, 0.05);
            cursor: not-allowed;
        }
    }
}

/deep/.ivu-btn-default {
    height: 32px;
}

/deep/.ivu-form-item {
    margin-bottom: 0;
}
</style>
