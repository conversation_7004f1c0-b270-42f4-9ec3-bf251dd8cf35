<template>
  <div>
    <div class="bsp-base-form">
      <div class="prison-select">
        <div class="prison-select-left">
          <div v-for="(item,index) in jsData" :key="index">
            <div :class="['prison-select-left-child',openCurRoom==index?'active':''] " @click="openRoom(item,index)">
              <div style="width: 70%;display: flex;align-items: center;">
                <div class="prison-select-left-childDiv">
                  <img src="@/assets/images/roomManage/监室信息管理.svg" style="width:40px;height: 40px;"
                    v-if="item.areaName=='全部'" />
                  <img src="@/assets/images/roomManage/icon_room.png" style="width:35px;height: 35px;" v-else />
                  <!-- <Icon type="ios-calculator" :size="30" color="#027bff" v-else /> -->
                </div>
                <span class="jsName" :style="{'color':item.areaName !='全部'?'':'#3398fe',}">{{ item.areaName }}</span>
              </div>
              <Icon :type="openCurRoom==index?'ios-arrow-down':'ios-arrow-forward'" :size="20"
                v-if="item.areaName !='全部'" style="padding-right: 10px;" />
            </div>
            <div class="roomNameWrap" v-if="openCurRoom==index">
              <p :class="['roomName',page.roomCode==ele.roomCode?'active':'']" v-for="(ele,i) in item.children"
                :key="i+'areaName'" @click="getUserData(item,ele,index,i)">
                {{ ele.roomName }}
              </p>
            </div>
          </div>
        </div>
        <div class="prison-select-center">
          <div class="prison-select-center-top">
            <div class="carousel" :class="{
                'type-1': formData.roomSex === '1' || formData.roomSex === '5',
                'type-2': formData.roomSex === '2' || formData.roomSex === '6'
              }">
              <div v-if="carouselRooms && carouselRooms.length > 0">
                <div class="carousel-container">
                  <div class="carousel-content">
                    <div class="carousel-info">
                      <h3>{{ carouselRooms[currentCarouselIndex] && carouselRooms[currentCarouselIndex].roomName ||
                        formData.roomName }}</h3>
                    </div>
                  </div>
                  <div class="room-switch">
                    <i class="switch-prev" @click="prevRoom"></i>
                    <i class="switch-next" @click="nextRoom"></i>
                  </div>
                </div>
              </div>

              <div v-else class="carousel-empty">
                暂无监室数据
              </div>
            </div>
            <div class="ryInfo">
              <div class="ryInfo-item">
                <img src="@/assets/images/roomArchive/icon_man1.png" class="ryInfo-img" alt="">
                <div style="line-height: 24px;display: flex; flex-direction: column;">
                  <span class="ryInfo-title">主管民警</span>
                  <Tooltip placement="top">
                    <p class="textEllipsis">{{ formData.sponsorNames ? formData.sponsorNames : '-' }}</p>
                    <template #content>
                      <p style="white-space: wrap;">{{ formData.sponsorNames }}</p>
                    </template>
                  </Tooltip>
                </div>
              </div>
              <div class="vertical"></div>
              <div class="ryInfo-item">
                <img src="@/assets/images/roomArchive/icon_man2.png" class="ryInfo-img" alt="">
                <div style="line-height: 22px;display: flex; flex-direction: column;">
                  <span class="ryInfo-title">协管民警</span>
                  <Tooltip placement="top">
                    <p class="textEllipsis">{{ formData.assistNames ? formData.assistNames : '-' }}</p>
                    <template #content>
                      <p style="white-space: wrap;">{{ formData.assistNames }}</p>
                    </template>
                  </Tooltip>
                </div>
              </div>
              <div class="vertical"></div>
              <div class="ryInfo-item">
                <img src="@/assets/images/roomArchive/icon_capacity.png" class="ryInfo-img" alt="">
                <div style="line-height: 22px;">
                  <span class="ryInfo-title">监室容量</span>
                  <p class="rtInfo-content"> {{ formData.imprisonmentAmount }}</p>
                </div>
              </div>
              <div class="vertical"></div>
              <div class="ryInfo-item">
                <img src="@/assets/images/roomArchive/icon_prisoner.png" class="ryInfo-img" alt="">
                <div style="line-height: 22px;">
                  <span class="ryInfo-title">在押人数</span>
                  <p class="rtInfo-content">{{ formData.prisonerNum }}人</p>
                </div>
              </div>
              <div class="vertical"></div>
              <div class="ryInfo-item">
                <img src="@/assets/images/roomArchive/icon_area.png" class="ryInfo-img" alt="">
                <div style="line-height: 22px;">
                  <span class="ryInfo-title">监室面积</span>
                  <p class="rtInfo-content">{{ formData.roomArea ? formData.roomArea + 'm²' : '-' }}</p>
                </div>
              </div>
              <div class="vertical"></div>
              <div class="ryInfo-item">
                <img src="@/assets/images/roomArchive/icon_average.png" class="ryInfo-img" alt="">
                <div style="line-height: 22px;">
                  <span class="ryInfo-title">人均铺位面积</span>
                  <p class="rtInfo-content">{{ formData.avgBedsArea ? formData.avgBedsArea : '-' }}</p>
                </div>
              </div>
            </div>
          </div>
          <div class="prison-select-center-bottom">
            <div class="informationPreview">
              <div class="zyryInfo">
                <p class="zyryInfo-title">
                  <span style="color: #2591ff; font-weight: 600;">{{ zyryData.count }}</span>
                  <span>在押人数</span>
                </p>
                <div class="vertical-line"></div>
                <div class="zyryInfo-cont">
                  <p>今日新入所：<span>{{ zyryData.toDayInCount }}</span></p>
                  <p>重大安全风险：<span>{{ zyryData.importantCount }}</span></p>
                  <p>关注人员：<span>{{ zyryData.attentionCount }}</span></p>
                  <p>重病号：<span>{{ zyryData.diseaseCount }}</span></p>
                  <p>紧急风险：<span>{{ zyryData.emergencyCount }}</span></p>
                </div>
              </div>
              <div class="jstzInfo">
                <p class="jstzInfo-title">
                  <span style="color: #2591ff; font-weight: 600;">{{ rybdData.count }}</span>
                  <span>监室调整人员变动</span>
                </p>
                <div class="vertical-line"></div>
                <div class="jstzInfo-cont">
                  <p>减少：<span>{{ rybdData.reduceCount }}</span></p>
                  <p>新增：<span>{{ rybdData.addCount }}</span></p>
                </div>
              </div>
              <div class="zyryInfo">
                <p class="zyryInfo-title">
                  <span style="color: #2591ff; font-weight: 600;">{{ wgrsData.count }}</span>
                  <span>未归总人数</span>
                </p>
                <div class="vertical-line"></div>
                <div class="zyryInfo-cont">
                  <p>所外就医：<span>{{ wgrsData.hospitalizeCount }}</span></p>
                  <p>提讯：<span>{{ wgrsData.arraignCount }}</span></p>
                  <p>家属会见：<span>{{ wgrsData.relation }}</span></p>
                  <p>提解：<span>{{ wgrsData.escortCount }}</span></p>
                  <p>律师会见：<span>{{ wgrsData.attorneyCount }}</span></p>
                </div>
              </div>
            </div>
            <div class="ryDetail" v-if="prisonerList && prisonerList.length > 0">
              <div class="ryDetail-item" v-for="(item,index) in prisonerList" :key="index">
                <img :src="item.frontPhoto" alt="" style="width: 80px; height: 110px; margin-right: 10px;">
                <div style="height: 100%;">
                  <p style="display: flex; align-items: center; color: #666; font-size: 16px; margin-bottom: 10px;">{{
                    item.xm }}<img v-if="item.xb == '2' || item.xb == '4'" src="@/assets/icons/xingbienan.svg"
                      style="width: 20px; height: 20px;" alt=""><img v-if="item.xb == '3' || item.xb == '5'"
                      src="@/assets/icons/xingbienv.svg" style="width: 20px; height: 20px;" alt=""></p>
                  <p style="margin-bottom: 6px;">{{ item.jsh }}</p>
                  <div style="display: flex; margin-bottom: 6px;justify-content: space-between;">
                    <p style=" white-space: nowrap; overflow: hidden;text-overflow: ellipsis;width: 5em;"
                      :title="item.sxzmName">{{ item.sxzmName }}</p>
                    <span class="verticalLine"></span>
                    <p style="width: 6em;white-space: nowrap; overflow: hidden;text-overflow: ellipsis;"
                      :title="item.sshjName">{{ item.sshjName }}</p>
                  </div>
                  <div class="ryDetail-tag">
                    <span>注</span>
                  </div>
                </div>
              </div>
            </div>
            <noData v-else />
          </div>
        </div>
      </div>
    </div>
    <div class="bsp-base-fotter">
      <Button @click="cancelRegion" class="save" style="margin-right: 10px;">返回</Button>
    </div>
  </div>
</template>

<script>
import noData from "@/components/noData"
export default {
  components: { noData },
	props: {
		rowData:{
			type: [Array,Object],
			default: () => ({})
		},
    modalTitle: {
      type: String,
      default: ""
    },
    syncAction: {
      type: String,
      default: ""
    },
	},
	data() {
		return {
      value: 0,
			formData: {
			},
			ruleValidate: {},
			currentData: {},
			orgCode: this.$store.state.common.orgCode,
			orgCodeList: [],
			storageSelected: [],
			sponsorList: [],
			assistList: [],
      treeData: [],
      spinShow: false,
      defaultImg: require('@/assets/images/main.png'),
      http: serverConfig.severHttp,
      women: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAEKADAAQAAAABAAAAEAAAAAA0VXHyAAABjklEQVQ4EcVSsUoDQRB9c3eJQbAwn6BFKlNYqAFtLQ0mGLERsRQLjZXaWgqxSilil4gJaa2FoEUKray1TgRFwuVux9nlclxiTCU4sLezM2/evZld4L+NxgnIV3iLoVIDGMvyYOGqlqc3HXcGkkMHRXwMxtxAWCmQMIid6c9YgnrBSmtQ1NYrvohSYV3oaNBug6feu+qIiZaltyTALdhWWeS2oiRR32jRgY0qpzpdfpLiUzlOyHoFaI19fshXeV9jRpkhkGLbA18TIUE2LYr0lVrBytqgGWI0FLiUu+X5XwkkkQHzgvzxICr3pkCfNtGOtNOBr/YMAdGzDPGlT2Zm4DEMe2ISd/1Ef9ckuapqCrnBDA/WtCCfti7we3pwP41loLIMZjhrCJw4mgRSbi+QGUEFvS8R830kHLrhS8xV/HO5gaJcXSkes8p2DO3uF1blfMGM7nSC0pdZ+ggrAyd8BzZZJz4rCdOh66oi3ABB9OgQbY8q1ohQQQDHZp1nPRcZoUo6BP2AmjJIv5//8/0bMieGFMlZxEQAAAAASUVORK5CYII=',
      men: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAEKADAAQAAAABAAAAEAAAAAA0VXHyAAABuElEQVQ4Eb2SPUsDQRCGZ/aSiIFYKwgWWhgQxMZSrexslCSoBLWQgCDEYDqNJirit2CjYCuSC/kB2hksAhZidTYidtoaUEzudpw1boxyYCNecbs78z5zs+8cwH88tJpvo7V8i9u30C2oY3bGHAMJOwTUrGKIcEdCzHgXQ+daI/Tm52qns9Mk6QSQLBQ4JQTGCPAZHHlWWckNar1rB7RrNjoleGJRwUiFhhCRFECm6bMtKCJQk2cp0qFi7h28GN1EFCAQBxpWYgyHywLhkAjatSfuBQB8CmCkXF2/3txKNUZVTa2AvZIbrqTNTW7TAK/nGhlGcGJfKF+BCNnUCc494sLIg8p5agIpA0CQdCzRagTfomTRFZ8jdtrkxThi3OdkcrM8kQE2dVxz30x0Mtl5KWFLoLiVJDv5/pdM9zL0cSU+l3iWCU8qdOxaQAV5fBdsUp8qIoLUBfcNfqjYPZwqg9+5wUT4VcNq/dZBZTm7x7E4/zAFVQRRnBpBirL7Tj1Uv6+ZqGFO7vOM+4WAJJEc5bmv1wM/9x8FaCPfyom4gr3LkTklMlKRbe5kkg0rqvOvz2eRX3V/LngHpeWkFKJkiGAAAAAASUVORK5CYII=',
      openCurRoom: 1,
      jsData: [],
      total: 0,
      page: {
        pageNo: 1,
        pageSize: 9,
        xm: '',
        rssj: []
      },
      userList: [],
      checkedUse: [],
      curRoom: '',
      pageSizeOpts:[9,18,27,45,72],
      room:{
        roomOrgCode: '',
        roomCode: ''
      },
      zyryData: {},
      rybdData: {},
      wgrsData: {},
      prisonerList:[],
      // prisonerList: [
      //   {
      //     age: 0,
      //     xm: '张三',
      //     sshj: '刑事拘留',
      //     sxzm: '贩卖毒品',
      //     frontPhoto: 'http://*************/file//sacp/202503171605569624.jpg',
      //     xb: '1',
      //     jsh: 'A101监室'
      //   },
      //   {
      //     age: 0,
      //     xm: '张三2',
      //     sshj: '刑事拘留',
      //     sxzm: '贩卖毒品',
      //     frontPhoto: 'http://*************/file//sacp/202503171605569624.jpg',
      //     xb: '3',
      //     jsh: 'A101监室'
      //   },
      //   {
      //     age: 0,
      //     xm: '张三3',
      //     sshj: '刑事拘留',
      //     sxzm: '贩卖毒品',
      //     frontPhoto: 'http://*************/file//sacp/202503171605569624.jpg',
      //     xb: '2',
      //     jsh: 'A101监室'
      //   },
      //   {
      //     age: 0,
      //     xm: '张三2',
      //     sshj: '刑事拘留',
      //     sxzm: '贩卖毒品',
      //     frontPhoto: 'http://*************/file//sacp/202503171605569624.jpg',
      //     xb: '3',
      //     jsh: 'A101监室'
      //   },
      //   {
      //     age: 0,
      //     xm: '张三',
      //     sshj: '刑事拘留',
      //     sxzm: '贩卖毒品',
      //     frontPhoto: 'http://*************/file//sacp/202503171605569624.jpg',
      //     xb: '1',
      //     jsh: 'A101监室'
      //   },
      //   {
      //     age: 0,
      //     xm: '张三3',
      //     sshj: '刑事拘留',
      //     sxzm: '贩卖毒品',
      //     frontPhoto: 'http://*************/file//sacp/202503171605569624.jpg',
      //     xb: '2',
      //     jsh: 'A101监室'
      //   },
      //   {
      //     age: 0,
      //     xm: '张三2',
      //     sshj: '刑事拘留',
      //     sxzm: '贩卖毒品',
      //     frontPhoto: 'http://*************/file//sacp/202503171605569624.jpg',
      //     xb: '3',
      //     jsh: 'A101监室'
      //   },
      //   {
      //     age: 0,
      //     xm: '张三',
      //     sshj: '刑事拘留',
      //     sxzm: '贩卖毒品',
      //     frontPhoto: 'http://*************/file//sacp/202503171605569624.jpg',
      //     xb: '1',
      //     jsh: 'A101监室'
      //   },
      //   {
      //     age: 0,
      //     xm: '张三3',
      //     sshj: '刑事拘留',
      //     sxzm: '贩卖毒品',
      //     frontPhoto: 'http://*************/file//sacp/202503171605569624.jpg',
      //     xb: '2',
      //     jsh: 'A101监室'
      //   },
      //   {
      //     age: 0,
      //     xm: '张三2',
      //     sshj: '刑事拘留',
      //     sxzm: '贩卖毒品',
      //     frontPhoto: 'http://*************/file//sacp/202503171605569624.jpg',
      //     xb: '3',
      //     jsh: 'A101监室'
      //   },
      //   {
      //     age: 0,
      //     xm: '张三1',
      //     sshj: '刑事拘留',
      //     sxzm: '贩卖毒品',
      //     frontPhoto: 'http://*************/file//sacp/202503171605569624.jpg',
      //     xb: '4',
      //     jsh: 'A101监室'
      //   },
      //   {
      //     age: 0,
      //     xm: '张三5',
      //     sshj: '刑事拘留',
      //     sxzm: '贩卖毒品',
      //     frontPhoto: 'http://*************/file//sacp/202503171605569624.jpg',
      //     xb: '4',
      //     jsh: 'A101监室'
      //   },
      //   {
      //     age: 0,
      //     xm: '张三58',
      //     sshj: '刑事拘留',
      //     sxzm: '贩卖毒品',
      //     frontPhoto: 'http://*************/file//sacp/202503171605569624.jpg',
      //     xb: '3',
      //     jsh: 'A101监室'
      //   },
      //   {
      //     age: 0,
      //     xm: '张三0',
      //     sshj: '刑事拘留',
      //     sxzm: '贩卖毒品',
      //     frontPhoto: 'http://*************/file//sacp/202503171605569624.jpg',
      //     xb: '5',
      //     jsh: 'A101监室'
      //   },
      // ],
      carouselRooms: [], // 当前监区的所有监室，用于轮播
      currentCarouselIndex: 0, // 当前轮播图索引
      defaultRoom: null 
		}
	},
	watch: {
	},
	methods: {
    getTreeData() {
      let params = {
        orgCode: this.$store.state.common.orgCode // '110000113',//
      }
      this.$store.dispatch('authGetRequest', {
        url: '/acp-com/base/area/getAreaListByOrgCode',
        params: params
      }).then(res => {
        if(res.code == 0) {
          console.log(res);
          this.treeData = res.data
        } else {
          this.$Modal.error({
            title: '温馨提示',
            content: res.msg || '操作失败'
          })
        }
      })
    },
    renderContent (h, { root, node, data }) {
      console.log(root, node, data,'root, node, data');
      return;
      // data.expand = data.expand !== false // 使用数据中的expand状态
      let type = 'ios-paper-outline'
      let texts = ''
      if (data.name.length > 16) {
        texts = data.name.slice(0, 16) + '...' // 进行数字截取
      } else {
        texts = data.name
      }
      if (data.children && data.children.length > 0) {
        // console.log(node, '8989898')
        if (node.expand) {
          type = 'ios-folder-open-outline'
        } else {
          type = 'ios-folder-outline'
        }
      }
      return h('span', [
        h('span', [
          h('Icon', {
            props: {
              type: type,
              size: '16',
              color: '#2B5FD9'
            },
            style: {
              marginRight: '8px'
            }
          }),
          h('span', {
            style: {
              fontSize: '16px',
              color: '#333'
            },
            attrs: {
              title: data.title
            }
          }, texts)
          // h('span', {
          //   style: {
          //     fontSize: '16px',
          //     color: '#333'
          //   }
          // }, data.title)
        ])
      ])
    },
    onClickTree() {

    },

    // 修改getJqData方法
    getJqData() {
      return new Promise((resolve) => {
        let params = {
          orgCode: this.$store.state.common.orgCode
        }
        this.$store.dispatch('authGetRequest', {
          url: '/acp-com/base/area/getAreaListByOrgCode',
          params: params
        }).then(resp => {
          if (resp.code == 0) {
            this.jsData = resp.data
            let all = {
              areaCode: 'all', 
              areaName: '全部'
            }
            this.jsData.unshift(all)
            
            // 如果是详情页，初始化默认选择
            if (this.syncAction === 'detail') {
              this.initDefaultSelection();
            }
            resolve();
          } else {
            this.$Notice.error({
              title: '错误提示',
              desc: resp.msg
            })
            resolve();
          }
        })
      })
    },
    
    // 初始化默认选择
    initDefaultSelection() {
      if (!this.jsData.length || !this.rowData.roomCode) return;
      
      // 查找包含当前roomCode的监区
      const foundArea = this.jsData.find(area => 
        area.children && area.children.some(room => room.roomCode === this.rowData.roomCode)
      );
      
      if (foundArea) {
        // 设置当前展开的监区
        const areaIndex = this.jsData.indexOf(foundArea);
        this.openCurRoom = areaIndex;
        
        // 设置轮播图数据
        this.carouselRooms = foundArea.children;
        
        // 找到默认监室
        this.defaultRoom = foundArea.children.find(
          room => room.roomCode === this.rowData.roomCode
        );
        
        // 设置当前索引
        if (this.defaultRoom) {
          this.currentCarouselIndex = foundArea.children.indexOf(this.defaultRoom);
          // 初始化获取数据
          this.getUserData(foundArea, this.defaultRoom);
        }
      }
    },
    // 修改getUserData方法
    // getUserData(item, ele, index, i) {
    //   if (ele && ele.roomCode) {
    //     this.page.roomCode = ele.roomCode;
    //     this.room.roomOrgCode = this.$store.state.common.orgCode;
    //     this.room.roomCode = ele.roomCode;
        
    //     // 更新轮播图索引
    //     if (item && item.children) {
    //       this.currentCarouselIndex = item.children.findIndex(
    //         room => room.roomCode === ele.roomCode
    //       );
    //     }
        
    //     // 获取数据
    //     this.getPrisonerNum();
    //     this.getAdjustPrisonerNum();
    //     this.getOutPrisonerNum();
    //     this.getPrisoner();
    //   }
    // },
    getUserData(item, ele, index, i) {
      this.page.roomCode = ele.roomCode
      console.log(item, ele, index, i, 'item,ele');

      if (ele && ele.roomCode) {
        this.room.roomOrgCode = this.$store.state.common.orgCode;
        this.room.roomCode = ele.roomCode;
        
        // 更新数据
        this.getPrisonerNum();
        this.getAdjustPrisonerNum();
        this.getOutPrisonerNum();
        this.getPrisoner();
        this.editYp(ele.roomCode)
        
        // 更新轮播图
        if (item && item.children) {
          this.initCarousel(item.children);
          this.currentCarouselIndex = item.children.findIndex(room => room.roomCode === ele.roomCode);
          this.updateCarouselDisplay();
        }
      }
    },
    editYp(roomCode) {
      console.log('rowData', this.rowData)
      this.$store.dispatch('getRequest',{
        url: this.$path.bsp_pam_infoManage_get,
        params: {
          orgCode: this.room.roomOrgCode,
          roomCode: roomCode
        }
      }).then(res => {
        if(res.success) {
          console.log(res,'res');
          // this.action = 'edit'
          this.formData = res.data
          // this.showFormCompnent = false
          // this.component = 'detail'
          // this.modalTitle = '编辑监室信息'
        } else {
          this.$Modal.error({
            title: '温馨提示',
            content: res.msg || '操作失败!'
          })
        }
      })
    },
    
    // 轮播图切换方法
    prevRoom() {
      if (this.carouselRooms.length === 0) return;
      this.currentCarouselIndex = (this.currentCarouselIndex - 1 + this.carouselRooms.length) % this.carouselRooms.length;
      this.getUserData(
        this.jsData[this.openCurRoom], 
        this.carouselRooms[this.currentCarouselIndex]
      );
    },
    
    nextRoom() {
      if (this.carouselRooms.length === 0) return;
      this.currentCarouselIndex = (this.currentCarouselIndex + 1) % this.carouselRooms.length;
      this.getUserData(
        this.jsData[this.openCurRoom], 
        this.carouselRooms[this.currentCarouselIndex]
      );
    },
    openRoom (item, index) {
      console.log(item,index);
      if (this.openCurRoom == index) {
        this.openCurRoom = 0
      } else {
        this.openCurRoom = index
      }
      // if (item.areaName == '全部') {
      //   this.page.areaCode = ''
      //   this.page.roomCode = ''
      //   this.page.pageNo = 1
      //   this.page.pageSize = 10
      //   this.getUserData()
      // }
    },
    // getUserData (item, ele) {
    //   console.log(item,ele,'item,ele');
    // },
    // 初始化轮播图
    initCarousel(rooms) {
      this.carouselRooms = rooms || [];
      this.currentCarouselIndex = 0;
      
      if (this.carouselRooms.length > 0) {
        this.updateCarouselDisplay();
      }
    },
    
    updateCarouselDisplay() {
      const currentRoom = this.carouselRooms[this.currentCarouselIndex];
      console.log(currentRoom);
    },
    
    // // 切换到上一个监室
    // prevRoom() {
    //   this.currentCarouselIndex = (this.currentCarouselIndex - 1 + this.carouselRooms.length) % this.carouselRooms.length;
    //   this.updateCarouselDisplay();
    //   this.getUserData(this.jsData[this.openCurRoom], this.carouselRooms[this.currentCarouselIndex]);
    // },
    
    // // 切换到下一个监室
    // nextRoom() {
    //   this.currentCarouselIndex = (this.currentCarouselIndex + 1) % this.carouselRooms.length;
    //   this.updateCarouselDisplay();
    //   this.getUserData(this.jsData[this.openCurRoom], this.carouselRooms[this.currentCarouselIndex]);
    // },
    getRoomImage(room) {
      // 这里可以根据roomCode或其他属性返回不同的图片
      // 如果没有特定图片，可以返回默认图片
      return room.imageUrl || this.defaultImg;
    },
    // 修改getUserData方法
    // getUserData(item, ele, index, i) {
    //   this.page.roomCode = ele.roomCode
    //   // this.openCurRoom = index
    //   console.log(item, ele, index, i, 'item,ele');

    //   if (ele && ele.roomCode) {
    //     this.room.roomOrgCode = this.$store.state.common.orgCode;
    //     this.room.roomCode = ele.roomCode;
        
    //     // 更新数据
    //     this.getPrisonerNum();
    //     this.getAdjustPrisonerNum();
    //     this.getOutPrisonerNum();
    //     this.getPrisoner();
        
    //     // 更新轮播图
    //     if (item && item.children) {
    //       this.initCarousel(item.children);
    //       this.currentCarouselIndex = item.children.findIndex(room => room.roomCode === ele.roomCode);
    //       this.updateCarouselDisplay();
    //     }

        
    //   }
    // },
    cancelRegion() {
      if( this.$route.query  && this.$route.query.org_code){
           this.$router.push('/homePage')
      }else{
			  this.$emit('on_show_table')
      }
		},
    getPrisonerNum() {
      this.$store.dispatch('getRequest',{
        url: this.$path.bsp_pam_infoManage_getPrisonerNum,
        params: {
          orgCode: this.room.roomOrgCode,
          roomCode: this.room.roomCode,
        }
      }).then(res => {
        if(res.success) {
          this.zyryData = res.data
        }
      })
    },
    getAdjustPrisonerNum() {
      this.$store.dispatch('getRequest',{
        url: this.$path.bsp_pam_infoManage_getAdjustPrisonerNum,
        params: {
          orgCode: this.room.roomOrgCode,
          roomCode: this.room.roomCode,
        }
      }).then(res => {
        if(res.success) {
          this.rybdData = res.data
        }
      })
    },
    getOutPrisonerNum() {
      this.$store.dispatch('getRequest',{
        url: this.$path.bsp_pam_infoManage_getOutPrisonerNum,
        params: {
          orgCode: this.room.roomOrgCode,
          roomCode: this.room.roomCode,
        }
      }).then(res => {
        if(res.success) {
          this.wgrsData = res.data
        }
      })
    },
    getPrisoner() {
      this.$store.dispatch('getRequest',{
        url: this.$path.bsp_pam_infoManage_getPrisoner,
        params: {
          orgCode: this.room.roomOrgCode,
          roomCode: this.room.roomCode,
        }
      }).then(res => {
        if(res.success) {
          this.prisonerList = res.data
        } else {
          this.$Modal.error({
            title: '温馨提示',
            content: res.msg || '接口操作失败'
          })
        }
      })
    },
    
	},
  mounted() {
    this.getTreeData();
    this.getJqData().then(() => {
      if(this.syncAction == 'detail') {
        this.formData = this.rowData;
        this.room.roomOrgCode = this.rowData.orgCode;
        this.room.roomCode = this.rowData.roomCode;
      } else if(this.syncAction == 'add') {
        this.formData = {};
        this.formData.status = '1';
      }
    });
  },
}
</script>

<style lang="less" scoped>
@import url('./index.less');
</style>
