<template>
  <div class="restraintsUsedApply" style="display: flex; flex-direction: column; height: 100%">
    <div class="title" style="
        border-bottom: 1px solid #efefef;
        padding-bottom: 10px;
        font-weight: bold;
      ">
      戒具使用呈批
    </div>
    <Row style="margin-top: 10px; flex: 1">
      <Col :span="6" style="display: flex; flex-direction: column">
      <div style="flex: 1; display: flex; flex-direction: column; padding: 10px">
        <PersonnelSelector @change="selectUser" />
        <Record :jgrybm="formData.jgrybm" :id="formData.id" style="flex: 1; display: flex; flex-direction: column" />
      </div>
      </Col>
      <Col :span="18" style="
          border-left: 1px solid #efefef;
          display: flex;
          flex-direction: column;
        ">
      <div class="form-container" style="padding: 10px; display: flex; flex-direction: column; flex: 1">
        <div class="form-title" style="
              text-align: center;
              background: #f5f7fa;
              line-height: 2.2;
              border: 1px solid #d7d7d7;
              border-top-left-radius: 0.2em;
              border-top-right-radius: 0.2em;
            ">
          戒具使用呈批
        </div>
        <div class="form-content" style="
              flex: 1;
              padding: 15px;
              border: 1px solid #d7d7d7;
              border-top: unset;
              border-bottom-left-radius: 0.2em;
              border-bottom-right-radius: 0.2em;
            ">
          <Form :model="formData" ref="form" :rules="ruleValidate" :label-width="120" @submit.native.prevent>
            <Row>
              <Col :span="24">
              <FormItem label="使用情形" prop="useSituation">
                <s-dicgrid v-model="formData.useSituation" dicName="ZD_GJ_EQUIPMENT_SCENE" :isSearch="false"
                  :multiple="false" />
              </FormItem>
              </Col>
              <Col :span="12">
              <FormItem label="戒具类型" prop="punishmentToolType">
                <s-dicgrid v-model="formData.punishmentToolType" dicName="ZD_GJ_EQUIPMENT_TYPE" :isSearch="false"
                  :multiple="true" />
              </FormItem>
              </Col>
              <Col :span="12">
              <FormItem label="使用天数" prop="useDays">
                <Select v-model="formData.useDays">
                  <Option v-for="item in 15" :value="'' + item" :key="item">{{ item }}天</Option>
                </Select>
              </FormItem>
              </Col>
              <Col :span="12">
              <FormItem label="临时固定" prop="isTempFixation">
                <RadioGroup v-model="formData.isTempFixation" @on-change="() => (formData.fixationDays = '')">
                  <Radio :label="1">是</Radio>
                  <Radio :label="0">否</Radio>
                </RadioGroup>
              </FormItem>
              </Col>
              <Col :span="12">
              <FormItem label="固定天数" prop="fixationDays" :rules="{
                      required: formData.isTempFixation === 1,
                      message: '该项为必填项',
                      trigger: 'blur',
                    }">
                <Select :disabled="formData.isTempFixation === 0" v-model="formData.fixationDays">
                  <Option v-for="item in +formData.useDays" :value="'' + item" :key="item">{{ item }}天</Option>
                </Select>
              </FormItem>
              </Col>
              <Col :span="24">
              <FormItem label="使用理由" prop="useReason">
                <Input v-model="formData.useReason" type="textarea" :autosize="{ minRows: 2, maxRows: 5 }"
                  placeholder="请填写使用理由"></Input>
              </FormItem>
              </Col>
              <Col :span="24">
              <FormItem label="是否关联惩罚" prop="isAssociatedPunishment">
                <i-switch v-model="formData.isAssociatedPunishment" size="default" :false-value="0" :true-value="1"
                  @on-change="() => (formData.punishmentMeasure = [])">
                </i-switch>
              </FormItem>
              </Col>
              <Col :span="24">
              <FormItem label="惩罚措施" prop="punishmentMeasure" :key="formData.isAssociatedPunishment" :rules="{
                      required: formData.isAssociatedPunishment === 1,
                      message: '该项为必填项',
                      type: 'array',
                      min: 1,
                      trigger: 'change',
                    }">
                <Checkbox style="margin-left: 15px" :disabled="formData.isAssociatedPunishment !== 1" label="全选"
                  @on-change="handleAllChoose">全选</Checkbox>
                <CheckboxGroup v-model="formData.punishmentMeasure">
                  <Checkbox v-for="item in punishmentMeasuresDict" :disabled="formData.isAssociatedPunishment !== 1"
                    :key="item.code" :label="item.code" style="margin-left: 15px">{{ item.name }}
                  </Checkbox>
                </CheckboxGroup>
              </FormItem>
              </Col>
              <Col :span="24">
              <FormItem label="备注" prop="remark">
                <Input v-model="formData.remark" type="textarea" :autosize="{ minRows: 2, maxRows: 5 }"
                  placeholder="请填写备注"></Input>
              </FormItem>
              </Col>
            </Row>
          </Form>
        </div>
        <div style="display: flex; justify-content: flex-end; padding: 10px 0">
          <Button style="margin-right: 15px" @click="handleCancel">取 消</Button>
          <Button type="primary" :loading="loading" @click="handleSubmit">确 定</Button>
        </div>
      </div>
      </Col>
    </Row>
  </div>
</template>

<script>
import api from "./api.js";
import { Record } from "./components";
import { mapActions } from "vuex";
export default {
  components: {
    Record,
  },
  data() {
    return {
      formData: {
        fixationDays: "",
        isAssociatedPunishment: 0,
        isTempFixation: 0,
        id: "",
        jgrybm: "",
        jgryxm: "",
        punishmentMeasures: "",
        punishmentMeasure: [],
        punishmentToolType: [],
        remark: "",
        useDays: "",
        useReason: "",
        useSituation: [],
      },
      ruleValidate: {
        useSituation: [
          {
            required: true,
            message: "该项为必填项",
            trigger: "blur",
          },
        ],
        punishmentToolType: [
          {
            required: true,
            message: "该项为必填项",
            trigger: "blur",
          },
        ],
        useDays: [
          {
            required: true,
            message: "该项为必填项",
            trigger: "blur",
          },
        ],
        // isTempFixation: [
        //   {
        //     required: true,
        //     message: "该项为必填项",
        //     trigger: "change",
        //   },
        // ],
        useReason: [
          {
            required: true,
            message: "该项为必填项",
            trigger: "blur",
          },
        ],
        // isAssociatedPunishment: [
        //   {
        //     required: true,
        //     message: "该项为必填项",
        //     trigger: "change",
        //   },
        // ],
      },
      loading: false,
      punishmentMeasuresDict: [],
    };
  },
  computed: {},
  created() {
    this.getDict();
  },
  methods: {
    ...mapActions([
      "postRequest",
      "authGetRequest",
      "authPostRequest",
      "getRequest",
    ]),
    getDict() {
      this.authGetRequest({
        url: "/bsp-uac/ops/dic/code/getDicCodeTreeData",
        params: { dicName: "ZD_GJCFNR" },
      }).then((res) => {
        if (res.success) {
          this.punishmentMeasuresDict = res.data;
        }
      });
    },
    selectUser(data) {
      this.formData.jgrybm = data.jgrybm;
      this.formData.jgryxm = data.xm;
      this.formData.id = data.id;
    },
    handleAllChoose(e) {
      this.formData.punishmentMeasure = e ? this.punishmentMeasuresDict.map(
        (item) => item.code
      ) : [];
    },
    handleCancel() {
      this.$refs.form.resetFields();
      this.$router.replace({ name: "restraintsUsedList" });
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.loading = true;
          this.formData.punishmentMeasures =
            this.formData.punishmentMeasure.join(",");
          this.authPostRequest({
            url: api.create,
            params: this.formData,
          }).then((res) => {
            this.loading = false;
            if (res.success) {
              this.$router.replace({ name: "restraintsUsedList" });
            } else {
              this.errorModal({ content: res.msg || "保存失败" });
            }
          });
        } else {
        }
      });
    },
  },
};
</script>
