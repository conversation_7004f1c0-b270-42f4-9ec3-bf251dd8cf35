<template>
    <div style="height: 100%;">
        <header class="header-container" v-if="bdNameShow">{{ bdName }}</header>
        <div class="form-container" v-if="showPgnr">
            <ul class="header-ul">
                <li>
                    {{ pgnr }}
                </li>
                <li>
                    {{ fsStatus }}
                </li>
            </ul>
            <slot name="formContainer"></slot>
        </div>
    </div>
</template>

<script>
export default {
    name: "pdbdForm",
    props: {
        bdName: {
            type: String,
            default: "",
        },
        bdNameShow: {
            type: Boolean,
            default: true
        },
        pgnr: {
            type: String,
            default: "* 评估内容"
        },
        fsStatus: {
            type: String,
            default: "是/否"
        },
        showPgnr: {
            type: Boolean,
            default: true
        }
    },
    data() {

        return {

        }

    },

    methods: {},

    components: {},

    created() { },

    computed: {},

}

</script>

<style scoped lang="less">
.header-container {
    width: 100%;
    height: 40px;
    background: inherit;
    background-color: rgba(245, 247, 250, 1);
    box-sizing: border-box;
    border-width: 1px;
    border-style: solid;
    border-color: rgba(215, 215, 215, 1);
    border-bottom: 0px;
    border-radius: 4px;
    text-align: center;
    line-height: 40px;
}

.form-container {
    width: 100%;
    // width: 1265px;
    // box-sizing: border-box;
    // border-width: 1px;
    // border-style: solid;
    // border-color: rgba(215, 215, 215, 1);
    // // border-bottom: 0px;
    // border-radius: 4px;

    .header-ul{
        margin: 10px 10px;
        list-style: none;
        display: flex;

        li {
            width: 50%;
            height: 40px;
            text-align: center;
            color: #fff;
            line-height: 40px;
            background: url('../../../assets/images/pdBg.png');
            background-size: 100% 100%;
            /* 或 cover */
            background-repeat: no-repeat;
            &:first-child {
                border-right: solid 1px #ccc;
            }
        }
    }
}
</style>
