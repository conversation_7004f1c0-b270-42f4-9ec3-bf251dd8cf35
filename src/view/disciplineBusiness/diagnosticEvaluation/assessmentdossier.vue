<template>
    <div class="disciplineMedical-container">
        <div class="table-container" v-if="tableContainer">
            <s-DataGrid ref="grid" funcMark="jds-zdpgda" :customFunc="true" :params="params">
                <template slot="customRowFunc" slot-scope="{ func, row, index }">
                    <Button style="margin-left: 10px;" v-if="func.includes(globalAppCode + ':jdszdpgda:xq1')"
                        @click.native="handleDczXq(index, row, 'haveActInstId')">详情</Button>
                </template>
            </s-DataGrid>
        </div>

        <div v-if="PgDetailContainer">
            <div class="max-container">
                <!-- 人员信息参加情况 -->
                <div class="content-container">
                    <div class="left-ryxx-cjqk">
                        <div class="person-ryxx">
                            <!-- <Ryxx :jgrybm="jgrybm" /> -->
                            <personnel-selector :value="jgrybm" mode="detail" title="被监管人员" :show-case-info="true" />
                        </div>
                        <div class="pgjl-pgsc">
                            <div class="tab-container" v-for="(tab, index) in tabs" :key="index"
                                :class="{ active: currentTab === tab.component }" @click="currentTab = tab.component">
                                <img :src="getImageUrl(tab.url)" alt="">
                                <h4>{{ tab.name }}</h4>
                            </div>
                        </div>
                    </div>
                    <!-- 评估表单 -->
                    <div class="right-pgbd">
                        <div class="pgbd-container">

                            <div class="tab-content-continer" style="height: 100%;">
                                <keep-alive>
                                    <component :is="currentTab" :jgrybm="jgrybm" @detailTable="detailTable"></component>
                                </keep-alive>
                            </div>
                            <div class="bsp-base-fotter">
                                <Button @click="handleDetailBack" style="margin-right: 10px;">返回</Button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div v-if="havaActContainer">
            <div class="max-container">
                <!-- 人员信息参加情况 -->
                <div class="content-container">
                    <div class="left-ryxx-cjqk">
                        <div class="person-ryxx">
                            <!-- <Ryxx :jgrybm="jgrybm" /> -->
                            <personnel-selector :value="jgrybm" mode="detail" title="被监管人员" :show-case-info="true" />
                        </div>
                        <div class="tab-cjqk">
                            <Tabs :value="cjqkTab">
                                <TabPane label="流程轨迹" name="lcgj">
                                    <s-general-history v-if="actInstId" :key="timer" :showModifyBtn="false"
                                        :showRevokeBtn="false" :actInstId="actInstId">
                                    </s-general-history>
                                </TabPane>
                                <TabPane label="教育康复参加情况" name="jykfcjqk">
                                    <ul>
                                        <li>
                                            <span>累计参加教育康复活动</span><span class="ljcj-Num">{{ ljcjNum }}次</span>
                                        </li>
                                        <li>
                                            展示最新10条记录
                                        </li>
                                    </ul>
                                    <div class="ljcj-list">
                                        <div v-for="(item, index) in ljcjList" :key="index" class="card-step-circle">
                                            <div class="step-circle"></div>
                                            <Card style="width:100%" class="card-container">
                                                <template #title>
                                                    <h4>{{ item.hdName }}</h4>
                                                </template>
                                                <template #extra>
                                                    <Icon type="ios-alarm-outline" />
                                                    {{ item.hdTime }}
                                                </template>
                                                <div class="rate-demo">
                                                    <div>
                                                        <span class="fixed-width">活动详情：</span>
                                                        <span>{{ item.dhDetail }}</span>
                                                    </div>
                                                    <div class="have-preview-img">
                                                        <span class="fixed-width">附 件：</span>
                                                        <img :src="item.fjurl1" alt="">
                                                        <img :src="item.fjurl2" alt="">
                                                    </div>
                                                </div>
                                            </Card>
                                        </div>
                                    </div>
                                </TabPane>
                            </Tabs>
                        </div>
                    </div>
                    <!-- 评估表单 -->
                    <div class="right-pgbd">
                        <Form ref="formData" inline style="margin: 10px 0 20px 0px;">
                            <div class="fm-content-box">
                                <p class="fm-content-wrap-title">
                                    <Icon type="md-list-box" size="24" color="#2b5fda" />诊断评估信息
                                </p>
                                <Row>
                                    <Col span="3" class="col-title"><span>评估类型</span></Col>
                                    <Col span="9"><span>
                                        {{ detailInfo.assmtTypeName }}
                                    </span>
                                    </Col>
                                    <Col span="3" class="col-title"><span>评估周期</span></Col>
                                    <Col span="9"><span>
                                        {{ detailInfo.assmtPeriodName }}
                                    </span></Col>
                                </Row>

                                <Row>
                                    <Col span="3" class="col-title"><span>评估结果</span></Col>
                                    <Col span="9"><span>
                                        {{ detailInfo.assmtResultValue }}
                                    </span>
                                    </Col>
                                    <Col span="3" class="col-title"><span>状态</span></Col>
                                    <Col span="9"><span>
                                        {{ detailInfo.statusName }}
                                    </span></Col>
                                </Row>
                                <Row>
                                    <Col span="3" class="col-title"><span>评估人</span></Col>
                                    <Col span="9"><span>
                                        {{ detailInfo.assmtUser }}
                                    </span>
                                    </Col>
                                    <Col span="3" class="col-title"><span>评估时间</span></Col>
                                    <Col span="9"><span>
                                        {{ detailInfo.assmtTime }}
                                    </span></Col>
                                </Row>

                            </div>
                        </Form>
                        <div class="pgbd-container">

                            <pdbdForm :bdName="bdName" :fsStatus="pgnr">
                                <div slot="formContainer">
                                    <div class="slot-form-container1">
                                        <p v-for="(item, index) in nrObj['pgnr']" :key="item.keyCode"
                                            v-if="assmt_type != '04'">
                                            <span>{{ item.keyName }}</span>
                                            <span>
                                                <RadioGroup v-model="item.keyValue">
                                                    <Radio label="1">{{ pgnrName1 }}</Radio>
                                                    <Radio label="0">{{ pgnrName2 }}</Radio>
                                                </RadioGroup>
                                            </span>
                                        </p>
                                        <p v-for="(item, index) in nrObj['pgnr']" :key="item.keyCode"
                                            v-if="assmt_type == '04'">
                                            <span>{{ item.keyName }}</span>
                                            <span>
                                                {{ item.keyValue == null ? '0' : item.keyValue }}
                                            </span>
                                        </p>
                                    </div>
                                </div>
                            </pdbdForm>
                            <pdbdForm :bdNameShow="false" pgnr="评估结果" :fsStatus="fsStatus">
                                <div slot="formContainer">
                                    <div class="slot-form-container1">
                                        <p v-for="(item, index) in nrObj['pgjg']" :key="item.keyCode">
                                            <span>{{ item.keyName }}</span>
                                            <span>
                                                <RadioGroup v-model="item.keyValue">
                                                    <Radio label="1">{{ fsStatusName1 }}</Radio>
                                                    <Radio label="0">{{ fsStatusName2 }}</Radio>
                                                </RadioGroup>
                                            </span>
                                        </p>
                                    </div>
                                </div>
                            </pdbdForm>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bsp-base-fotter">
                <Button @click="handleHaveActBack" style="margin-right: 10px;">返回</Button>
            </div>
        </div>
    </div>
</template>

<script>
import { sDataGrid } from 'sd-data-grid'
import { mapActions, mapState } from 'vuex'
// import Ryxx from "@/components/personnel-selection/ryxx.vue";
import personnelSelector from "@/components/personnel-selector"

import pdbdForm from './pdbdForm.vue';
import { prisonSelect } from 'sd-prison-select'
import { sGeneralHistory } from 'sd-general-history'
import Tab1 from './tab1.vue';
import Tab2 from './tab2.vue';
export default {
    name: "assessmentdossier",

    data() {
        return {
            tableContainer: true,
            PgDetailContainer: false,
            havaActContainer: false,
            params: {},
            tabs: [
                { name: '诊断评估记录', url: 'zdpgjl.svg', component: 'Tab1' },
                { name: '诊断评估手册', url: 'zdpgsc.svg', component: 'Tab2' }
            ],
            currentTab: 'Tab1', // 默认激活的组件
            cjqkTab: 'lcgj',
            ljcjList: [],
            actInstId: "",
            ljcjNum: 0,
            timer: '',
            nrObj: {
                pgnr: []
            },
            bdName: "",
            assmt_type: '',
            pgnr: "",
            pgnrName1: "",
            pgnrName2: "",
            fsStatus: "",
            fsStatusName1: "",
            fsStatusName2: "",
            detailInfo: {
                assmtTypeName: "",
                assmtPeriodName: "",
                assmtResultValue: "",
                statusName: "",
                assmtUser: "",
                assmtTime: "",
            }
        }
    },

    methods: {
        ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
        getImageUrl(name) {
            return require(`@/assets/images/${name}`)
        },
        handleDczXq(idx, row) {
            this.jgrybm = row.jgrybm
            this.tableContainer = false
            this.PgDetailContainer = true
        },
        handleDetailBack() {
            this.tableContainer = true
            this.PgDetailContainer = false
        },
        detailTable(row) {
            console.error(row.assmtTypeName, '[rowrow]');
            this.assmt_type = row.assmtType
            this.bdName = row.assmtTypeName
            this.actInstId = row.actInstId
            this.tableContainer = false
            this.PgDetailContainer = false
            this.havaActContainer = true
            this.handleGetLjcjList()
            this.handleDiagnosisAssmtJdsGet(row.id)
            // 生理脱毒 01
            if (row.assmtType == '01' || row.assmtType == '02' || row.assmtType == '04') {
                if (row.assmtType == '01' || row.assmtType == '02') {
                    this.pgnr = "是/否"
                    this.pgnrName1 = "是"
                    this.pgnrName2 = "否"
                } else {
                    this.pgnr = "逐月累计得分（平均分）"
                }
                this.fsStatus = "合格/不合格"
                this.fsStatusName1 = "合格"
                this.fsStatusName2 = "不合格"
            } else if (row.assmtType == '03') {
                this.pgnr = "具备/不具备"
                this.pgnrName1 = "具备"
                this.pgnrName2 = "不具备"
                this.fsStatus = "良好/一般"
                this.fsStatusName1 = "良好"
                this.fsStatusName2 = "一般"
            }
        },
        handleGetLjcjList() {
            // this.authPostRequest({ url: '', params: {} }).then(res => {
            //     if(res.success) {
            //         this.ljcjList = res.data
            //     }
            // })

            this.ljcjList = [{ hdName: "晨会分享", hdTime: '2025-03-18 12:23:43', dhDetail: "这是一个晨会分享晨会分享晨会分享晨会分享晨会分享晨会分享", fjurl1: '../../../assets/images/fjurl.png', fjurl2: '../../../assets/images/fjurl.png' },
            { hdName: "晨会分享", hdTime: '2025-03-18 12:23:43', dhDetail: "这是一个晨会分享晨会分享晨会分享晨会分享晨会分享晨会分享", },
            { hdName: "晨会分享", hdTime: '2025-03-18 12:23:43', dhDetail: "这是一个晨会分享晨会分享晨会分享晨会分享晨会分享晨会分享", }
            ]
        },
        handleDiagnosisAssmtJdsGet(id) {
            this.authGetRequest({ url: this.$path.diagnosisAssmtJdsGet, params: { id } }).then(res => {
                if (res.success) {
                    console.error(res.data.assmtTypeName);
                    const { assmtTypeName, assmtPeriodName, assmtResultValue, statusName, assmtUser, assmtTime } = res.data
                    this.detailInfo.assmtTypeName = assmtTypeName
                    this.detailInfo.assmtPeriodName = assmtPeriodName
                    this.detailInfo.assmtResultValue = assmtResultValue
                    this.detailInfo.statusName = statusName
                    this.detailInfo.assmtUser = assmtUser
                    this.detailInfo.assmtTime = assmtTime
                    console.error(assmtTypeName, assmtPeriodName, assmtResultValue, statusName, assmtUser, assmtTime);

                    // this.$set(this.detailInfo, 'assmtTypeName', res.data.assmtTypeName)
                    // this.$set(this.detailInfo, 'assmtPeriodName', res.data.assmtPeriodName)
                    // this.$set(this.detailInfo, 'assmtResultValue', res.data.assmtResultValue)
                    // this.$set(this.detailInfo, 'statusName', res.data.statusName)
                    // this.$set(this.detailInfo, 'assmtUser', res.data.assmtUser)
                    // this.$set(this.detailInfo, 'assmtTime', res.data.assmtTime)
                    this.$set(this.nrObj, 'pgnr', JSON.parse(res.data.asstmContentJson))
                    this.$set(this.nrObj, 'pgjg', JSON.parse(res.data.assmtResultJson))
                }
            })
        },
        handleHaveActBack() {
            this.PgDetailContainer = true;
            this.havaActContainer = false;
        }
    },
    components: {
        sDataGrid,
        personnelSelector,
        pdbdForm,
        prisonSelect,
        sGeneralHistory,
        Tab1,
        Tab2,
    },

    created() {

    },

    computed: {

    },
    watch: {
        actInstId() {
            if (this.actInstId) {
                this.timer = new Date().getTime()
            }
        }
    },

}

</script>

<style scoped lang="less">
@import "~@/assets/style/formInfo.css";

/deep/.fm-content-wrap-title {
    border-bottom: 1px solid #cee0f0;
    background: #eff6ff;
    line-height: 40px;
    padding-left: 10px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: bold;
    font-size: 16px;
    color: #00244A;
    /* margin-bottom: 16px; */
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    border-bottom: 1px solid #CEE0F0;
}

/deep/.header-container {
    width: 100%;
    height: 40px;
    background: inherit;
    font-size: 18px;
    font-weight: 700;
    background-color: #f5f7fa;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    border-width: 1px;
    border-style: solid;
    border-color: #d7d7d7;
    /* border-bottom: 0px; */
    border-radius: 4px;
    text-align: center;
    line-height: 40px;
}

.disciplineMedical-container {
    width: 100%;
    height: 100%;

    .max-container {
        width: 100%;

        .content-container {
            display: flex;
            justify-content: space-between;
            overflow-y: auto;

            .left-ryxx-cjqk {

                // height: 100%;
                width: 400px;




                .person-ryxx {
                    padding: 10px 10px 5px 10px;
                    position: relative;

                    .add-prisin {
                        width: 324px;
                        height: 263px;
                        background: url('../../../assets/images/addPribg.svg');
                        background-size: 100% 100%;
                        text-align: center;
                        // line-height: 263px;
                        cursor: pointer;

                        img {
                            display: inline-block;
                            padding-top: 100px;
                        }
                    }



                    .selected-user {
                        position: absolute;
                        right: 0;
                        top: 0;
                    }
                }

                .pgjl-pgsc {
                    margin: 20px 15px 30px 15px;

                    .tab-container {
                        display: flex;
                        align-items: center;
                        margin-top: 20px;
                        cursor: pointer;
                        padding: 10px;
                        border-radius: 4px;

                        img {
                            display: block;
                            width: 33px;
                            height: 28px;
                            margin-right: 15px;
                        }
                    }

                    .tab-container.active {

                        h4 {
                            color: #2B5FD9;
                        }
                        font-weight: 700;
                        font-style: normal;
                        
                        background: rgba(105, 152, 245, 0.152941176470588);
                    }
                }

                .tab-cjqk {
                    ul {
                        list-style: none;
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        padding: 0 15px 0 10px;

                        li {
                            .ljcj-Num {
                                font-weight: 700;
                                font-size: 18px;
                                color: #F59A23;
                                margin-left: 8px;
                            }
                        }
                    }

                    .ljcj-list {
                        margin: 10px 10px 10px 0;

                        .card-step-circle {
                            margin-bottom: 10px;
                            display: flex;


                            .step-circle {
                                width: 8px;
                                /* 直径 */
                                height: 8px;
                                border: 2px solid #2B5FD9;
                                /* 边框宽度和颜色 */
                                border-radius: 50%;
                                /* 关键：圆形 */
                                background: none;
                                /* 确保内部透明 */

                                &::after {
                                    content: '';
                                    position: absolute;
                                    /* top: 100%; */
                                    /* left: 50%; */
                                    -webkit-transform: translateX(-50%);
                                    /* transform: translateX(-50%); */
                                    width: 1px;
                                    height: 170px;
                                    margin-top: 4px;
                                    left: 4px;
                                    /* top: 45px; */
                                    background-color: #d7d7d7;

                                    &:last-child {
                                        width: 1px;
                                        height: 0;
                                    }
                                }
                            }



                            .card-container {
                                margin-left: 10px;

                                h4 {
                                    padding-top: 5px;
                                }

                                .rate-demo {
                                    .fixed-width {
                                        display: inline-block;
                                        width: 70px;
                                        text-align: justify;
                                        /* 两端对齐 */
                                        word-break: break-all;
                                        /* 允许任意字符换行 */
                                    }

                                    .have-preview-img {
                                        display: flex;
                                        align-items: center;

                                        img {
                                            width: 102px;
                                            height: 43px;
                                            margin-right: 10px;
                                        }
                                    }

                                }
                            }
                        }
                    }
                }
            }

            .right-pgbd {
                flex: 1;
                margin-left: 20px;

                .pgbd-container {
                    // margin: 20px 5px 0px 5px;
                    box-sizing: border-box;
                    border-top: none;
                    border-radius: 4px;
                    border-width: 1px;
                    border-style: solid;
                    border-color: rgba(215, 215, 215, 1);
                    height: 88%;
                    .form-container {
                        padding: 10px 10px 30px 0px;
                    }
                }

                .sp-container {
                    margin: 20px 5px 0px 5px;

                    header {
                        width: 100%;
                        height: 45px;
                        line-height: 45px;
                        text-align: center;
                        border-width: 1px;
                        border-style: solid;
                        border-color: rgba(215, 215, 215, 1);
                    }

                    .form-container {
                        border-width: 1px;
                        border-style: solid;
                        border-color: rgba(215, 215, 215, 1);
                        padding: 10px;
                        border-top: none;
                        // margin-bottom: 30px;
                    }
                }

                .pdlx-container {
                    margin: 10px 0 0 10px;

                    ul {
                        list-style: none;
                        display: flex;
                        align-items: center;

                        li {
                            span:last-child {
                                margin-left: 15px;
                            }
                        }

                        li:last-child {
                            margin-left: 10px;
                        }
                    }
                }
            }
        }

    }
}

/deep/.personnel-info {
    margin-bottom: 5px;
}

.slot-form-container1 {
    margin: 0 10px;

    p {
        width: 100%;
        display: flex;
        align-items: center;

        span {
            display: inline-block;
            width: 50%;
            height: 45px;
            line-height: 45px;
            text-align: center;
        }

        &:nth-child(even) {
            background-color: #ecf3f5;
        }
    }
}
</style>
