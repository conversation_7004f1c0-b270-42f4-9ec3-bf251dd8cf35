<template>
    <div class="bug-template" style="height: 100%;">
        <div class="bus-name">
            <header>
                <h1>目录</h1>
                <Button type="primary" @click="printArchive" :loading="loading" loading-text="打印全部加载中"
                    class="fr">打印全部</Button>
            </header>
            <div class="business-list">
                <div v-for="(item, index) in businessList" :key="index" @click="handleSelectIndex(index, item)"
                    :class="{ active: currentIndex == index, }" class="disPriant">
                    <span class="priName" :title="item.name">{{ item.name }}</span>
                    <Icon type="ios-print-outline" style="padding-right: 10px;"
                        @click.stop="printArchiveOne(item.formId)" class="f-20" />
                </div>
            </div>
        </div>
        <div class="template-preview">
            <report style="height: 100%;" :parameter="parameter" ref="report"></report>
        </div>
    </div>
</template>

<script>
import { mapActions } from 'vuex'
import report from "@/components/archive/printContent/report.vue";
import print from 'print-js'
import vueToPdf from 'vue-to-pdf'
import Vue from 'vue'
import { getToken } from '@/libs/util'
Vue.use(vueToPdf)
export default {
    name: "Tab2",
    props: {
        jgrybm: {
            type: String,
            default: "",
        }
    },
    data() {

        return {
            businessList: [],
            currentIndex: 0,
            parameter: {
                businessId: "",
                formId: "",//'xxx',
            },
            loading: false,
            pldySpin: false,
        }

    },

    methods: {
        ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
        handleGetBusinessList() {
            this.authGetRequest({ url: this.$path.getFormIdBusinessId, params: { jgrybm: this.jgrybm } }).then(res => {
                if (res.success) {
                    this.businessList = res.data
                }
            })
        },
        handleSelectIndex(index, item) {
            this.currentIndex = index
            this.parameter.businessId = item.businessId ? item.businessId : "",
                this.parameter.formId = item.formId ? item.formId : ""
        },
        printArchive() {
            let _this = this
            _this.pldySpin = true
            let formIds = []
            this.businessList.forEach(ele => {
                formIds.push(ele.formId)
            })
            console.error(formIds);

            let iframeUrl = `${this.$path.pdf_getPdfBatch}?plugIns=MultipleRowTableRenderPolicy&formId=${formIds.join(',')}&businessId=${this.parameter.businessId}&access_token=${getToken()}` + '#toolbar=0'
            printJS({
                printable: iframeUrl,
                type: 'pdf',
                header: '打印',
                onLoadingEnd: async () => {
                    _this.pldySpin = false
                }
            })
        },
        printArchiveOne(formId) {
            let _this = this
            _this.pldySpin = true
            let iframeUrl = `${this.$path.pdf_getPdfBatch}?plugIns=MultipleRowTableRenderPolicy&formId=${formId
                }&businessId=${this.parameter.businessId}&access_token=${getToken()}` + '#toolbar=0'
            printJS({
                printable: iframeUrl,
                type: 'pdf',
                header: '打印',
                onLoadingEnd: async () => {
                    _this.pldySpin = false
                }
            })
        },
    },

    components: {
        report
    },

    created() {
        this.handleGetBusinessList()

    },
    computed: {},
    watch: {
        'businessList': {
            handler(newVal) {
                if (newVal.length > 0) {
                    this.parameter.businessId = newVal[0].businessId;
                    this.parameter.formId = newVal[0].formId
                }
            },
            immediate: true
        }
    }

}

</script>

<style scoped lang="less">
.disPriant {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.bug-template {
    display: flex;

    .bus-name {
        width: 326px;

        header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 10px;
        }

        .business-list {
            padding: 0 10px;

            div {
                height: 35px;
                margin-bottom: 20px;
                cursor: pointer;
                line-height: 35px;
                padding-left: 10px;
                border-radius: 4px;

                &.active {
                    font-weight: 700;
                    font-style: normal;
                    color: #2B5FD9;
                    background: rgba(105, 152, 245, 0.152941176470588);
                }
            }
        }
    }

    .template-preview {
        flex: 1;
        margin-left: 10px;
    }
}

.priName {
    width: 90%;
    display: inline-block;
    white-space: nowrap;
    /* 禁止换行 */
    overflow: hidden;
    /* 隐藏溢出内容 */
    text-overflow: ellipsis;
    /* 显示省略号 */
}
</style>
