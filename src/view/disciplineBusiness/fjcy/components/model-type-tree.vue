<template>
    <div>
        <div class="base-left-content2" style="">
            <div class="bsp-base-subtit no-border">
                <div class="search-box">
                    <Input suffix="ios-search" placeholder="搜索" v-model="keyword" />
                </div>
                <div class="button-group">
                    <Button size="small" v-if="!params.indicatorId" type="primary" @click="handleAdd">新增</Button>
                    <Button size="small" v-if="params.typeId" type="primary" @click="handleEdit">编辑</Button>
                    <Button size="small" v-if="params.typeId" type="error" @click="handleDelete">删除</Button>
                </div>
            </div>
            <div class="main-content" style=" top: 100px; padding:0 10px;">
                <Tree :data="currentTreeData" :render="renderContent"  @on-select-change="onClickTree"></Tree>
            </div>
        </div>
    </div>
</template>
<script>
import { mapActions } from 'vuex'
export default {
  name: 'AyflTree',
  props: {
    fllx: {
      fllx: String,
      default: ''
    },
    treeData: {
      type: Array,
      default: () => []
    }
  },
  data () {
    return {
      load: false,
      // 原始树数据（未构建成树的结构的数据）
      currentTreeData: [],
      // 树数据
      //   treeData: [],
      keyword: '',
      defaultSelectedId: null, // 存储默认选中的节点ID
      params: {},
    }
  },
  mounted () {
    this.initTreeData()
  },
  watch: {
    keyword (val) {
      if (val) {
        this.searchTree(val)
      } else {
        this.currentTreeData = this.treeData
      }
    },
    treeData: {
      deep: true,
      handler (newVal) {
        // this.originTreeData = newVal
        this.currentTreeData = newVal
        this.initTreeData(newVal)
      }
    }
  },
  methods: {
    ...mapActions(['postRequest', 'authGetRequest']),
    // 初始化树数据，默认选中根节点并展开
    initTreeData (data = this.treeData) {
      // 深拷贝原始数据避免污染
      const treeDataCopy = JSON.parse(JSON.stringify(data))

      if (treeDataCopy && treeDataCopy.length > 0) {
        // 默认选中第一个根节点
        treeDataCopy[0].selected = true
        treeDataCopy[0].expand = true
        this.defaultSelectedId = treeDataCopy[0].id

        // 默认展开所有节点
        const expandAll = (nodes) => {
          nodes.forEach(node => {
            // node.expand = true
            if (node.children && node.children.length > 0) {
              expandAll(node.children)
            }
          })
        }
        expandAll(treeDataCopy)

        // 触发选中事件
        this.$nextTick(() => {
          this.onClickTree([treeDataCopy[0]])
        })
      }

      this.currentTreeData = treeDataCopy
    },
    renderContent (h, { root, node, data }) {
      let type = 'ios-paper-outline'
      let texts = ''
      if (data.name.length > 16) {
        texts = data.name.slice(0, 16) + '...'
      } else {
        texts = data.name
      }
      if (data.children && data.children.length > 0) {
        if (node.expand) {
          type = 'ios-folder-open-outline'
        } else {
          type = 'ios-folder-outline'
        }
      }
      return h('span', [
        h('span', [
          h('Icon', {
            props: {
              type: type,
              size: '16',
              color: '#2B5FD9'
            },
            style: {
              marginRight: '8px'
            }
          }),
          h('span', {
            style: {
              fontSize: '16px',
              color: '#333'
            },
            attrs: {
              title: data.title
            }
          }, texts)
        ])
      ])
    },
    /**
     * 渲染树
     * @param arr 待渲染数据
     * @param parentId 父级ID
     * @param type 类型 1:第一层根节点，2：有parentId的子节点
     */
    createTree (arr, parentId, type) {
      let tree = []
      arr.forEach(e => {
        let item = null

        if (type == '1' && !e.sjflId) {
          // 根节点：parentId为空或者parentId不存在的
          item = e
        } else if (type == '2' && parentId == e.sjflId) {
          item = e
        }

        if (item != null) {
          let node = {}
          let { id, flmc, flbh, sjflId, flpx, flqp, fljp, fllx } = item
          node.editData = {
            id,
            flmc,
            flbh,
            sjflId,
            flpx,
            flqp,
            fljp,
            fllx
          }
          node.title = item.flmc
          node.id = item.id
          node.flbh = item.flbh
          node.parentId = item.sjflId
          node.children = this.createTree(arr, item.id, '2')
          if (JSON.stringify(node.children) != '[]') {
            node.expand = true
          }
          // 渲染被选中的状态
          if (this.curNodeId && item.id == this.curNodeId) {
            node.selected = true
          }
          tree.push(node)
        }
      })
      return tree
    },

    getParentNodes (arr1, id) {
      var temp = []
      let idKey = 'id'
      let pIdKey = 'parentId'

      var forFn = function (arr, id) {
        for (var i = 0; i < arr.length; i++) {
          var item = arr[i]
          if (item[idKey] === id && item[pIdKey]) {
            let o = Object.assign({}, item)
            delete o['children']
            temp.unshift(o)
            if (item[pIdKey]) {
              forFn(arr1, item[pIdKey]); // 继续向上查找
            }
            break
          } else {
            if (item.children) {
              forFn(item.children, id)
            }
          }
        }
      }
      forFn(arr1, id)
      return temp
    },
    onClickTree (data, item) {
      let params = {}

      // 点击节点
      if (data && data.length > 0) {
        const node = data[0]
        // 获取当前节点的所有父级路径节点（从根到当前节点的父节点）
        const parentNodes = this.getParentNodes(this.currentTreeData, node.id)
        // 判断层级并赋值
        if (parentNodes.length === 0) {
          // 当前是第一层节点
          params.modelId = node.id
          params.id = node.id
        } else if (parentNodes.length === 1) {
          // 当前是第二层节点
          params.modelId = parentNodes[0].riskModelId
          params.typeId = parentNodes[0].id
          params.id = parentNodes[0].id
        } else if (parentNodes.length >= 2) {
          // 当前是第三层节点
          params.modelId = parentNodes[1].riskModelId
          params.typeId = parentNodes[1].parentId
          params.indicatorId = parentNodes[1].id
          params.id = parentNodes[1].id
        }

        params.cancel = false
        params.isParent = node.children && node.children.length > 0
      } else {
        params.cancel = true
      }

      this.params = params
      this.$emit('selectChange', params, data)
    },
    // 新增按钮点击处理
    handleAdd() {
      this.$emit('addNode', this.params)
    },
    // 编辑按钮点击处理
    handleEdit() {
      this.$emit('editNode', this.params)
    },
    // 删除按钮点击处理
    handleDelete() {
      this.$emit('deleteNode', this.params)
    },
    /**
     * 刷新树
     * @param type 类型 update或者add
     * @param data 数据
     */
    refreshTree () {
    },
    searchTree (keyword) {
      if (keyword) {
        // 创建原始树的副本进行操作
        let filteredData = JSON.parse(JSON.stringify(this.currentTreeData))
        for (let i = 0; i < filteredData.length; i++) {
          let child = filteredData[i]
          let childResult = this.recursiveTree(child, keyword)
          if (!childResult) {
            filteredData.splice(i, 1)
            i--
          }
        }
        this.currentTreeData = filteredData
      }
    },
    recursiveTree (treeNode, keyword) {
      let result = 0
      if (treeNode.children && treeNode.children.length > 0) {
        for (let i = 0; i < treeNode.children.length; i++) {
          let child = treeNode.children[i]
          let childResult = this.recursiveTree(child, keyword)
          result |= childResult
          if (!childResult) {
            treeNode.children.splice(i, 1)
            treeNode.expand = true
            i--
          }
        }
      }
      if (treeNode.name.indexOf(keyword) > -1) {
        treeNode.expand = true
        result = 1
      }
      return result
    }
  }
}
</script>
<style lang="less" scoped>
.bsp-base-form .base-left-content .main-content, .bsp-base-form .base-left-content2 .main-content{
  overflow-x: hidden!important;
  overflow-y: auto!important;
}
.button-group {
  margin-left: 20px;
  padding: 10px 0;
  button {
    margin-right: 10px;
  }
}
.base-left-content2 {
  width:280px;
  bottom:10px;
  top: 10px;
  left: 10px;
  margin-right: 10px;
}
</style>
