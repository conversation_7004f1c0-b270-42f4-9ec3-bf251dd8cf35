<template>
  <div class="jgzsk-info">
    <div class="jgzsk-content">
        <div class="jgzsk-left">
            <div class="catalogue-header">
              <span>知识库目录</span>
            </div>
            <Input v-model="keyword" placeholder="输入关键字过滤目录" class="catalogue-input"></Input>
            <div class="catalogue-content">
                <p
                v-for="(item, index) in filteredFileList"
                :key="index"
                @click="toPage(item)"
                :class="[{ active: curTab == getOriginalIndex(item) }]"
              >
                <span>{{ item.name }}</span>
              </p>
            </div>
        </div>
        <div class="jgzsk-right">
            <div class="right-header">
                <p style="line-height: 40px; color: #415060; font-weight: bold;">{{currentItemTitle}}</p>
                <Button v-if="currentItem.type == 'image/png'" type="primary" :loading="loading" @click="downLoadImg(currentItem.url,currentItem.name)" class="fr">图片下载</Button>
                <Button v-if="fileType.indexOf(currentItem.type) != -1" type="primary" :loading="fileDownloading" @click="downloadFile(currentItem.url, currentItem.name)">文件下载</Button>
            </div>
            <div class="jgzsk-right-content">
                <!-- 图片 -->
                <div class="img-container" v-if="imgType.indexOf(currentItem.type) != '-1'">
                    <img :src="currentItem.url" style="width: 100%; height: 100%;" alt="">
                </div>
                <!-- word -->
                <div class="file-container" v-if="fileType.indexOf(currentItem.type) != '-1'">
                    <!-- <FileUpload
                        :default-list="fileList"
                        :width="320"
                        :height="60"
                        :auto-upload="true"
                        style="margin-top: 10px;"
                    /> -->
                    <vue-office-docx v-if="currentItem.type == 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'" :src="currentItem.url" style="height: 600px;" />
                    <vue-office-excel v-if="currentItem.type == 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'" :src="currentItem.url" style="width: 100%; height: 100%;" />
                </div>
                <!-- pdf -->
                <div class="pdf-container" v-if="currentItem.type == 'application/pdf'">
                    <iframe   
                        v-if="currentItem.type == 'application/pdf'"
                        :src="currentItem.url" 
                        id="iframePdf"
                        style="width: 100%;height: 100%;"></iframe>
                </div>
                <!-- 视频 -->
                <div class="video-container" v-if="currentItem.type == 'video/mp4'">
                    <video
                    ref="videoPlayer"
                    class="video-js vjs-default-skin"
                    controls
                    preload="auto"
                    width="100%"
                    height="450"
                    data-setup="{}"
                    >
                    <source :src="currentItem.url" type="video/mp4" />
                    <!-- <source src="http://172.26.0.22:9010/dam/689595eb6320cdc5d6821f6b.mp4" type="video/mp4" /> -->
                    <p class="vjs-no-js">
                        您的浏览器不支持视频播放，请
                        <a href="https://videojs.com/html5-video-support/" target="_blank">
                        升级您的浏览器
                        </a>
                    </p>
                    </video>
                </div>
            </div>
        </div>
    </div>

    <div class='bsp-base-fotter' style="text-algin: center;">
        <Button @click="toback">返 回</Button>
    </div>
  </div>
</template>

<script>
import { Img } from 'view-design'; 
import FileUpload from '@/components/bsp-upload/FileUpload.vue'
import VueOfficeDocx from '@vue-office/docx'
import VueOfficeExcel from '@vue-office/excel'
export default {
    components: {
        FileUpload,
        Img,
        VueOfficeDocx,
        VueOfficeExcel
    },
    props: {
        curId:String
    },
    data() {
        return {
            formData: {},
            fileList: [],
            filterTabList: [],
            loading: false,
            keyword: '',
            curTab: null,
            serviceMark: serverConfig.OSS_SERVICE_MARK,
            bucketName: serverConfig.bucketName,
            fileType: ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet','application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
            imgType: ['image/jpeg','image/png'],
            preview: true,
            isDownloading: false,
            fileDownloading: false,
        }
    },
    created(){
        this.getData()
    },
    computed: {
        currentItem() {
        return this.fileList[this.curTab] || {};
        },
        currentItemTitle() {
        return this.currentItem.fileName || this.currentItem.name || '';
        },
        filteredFileList() {
            if (!this.keyword) return this.fileList;
            return this.fileList.filter(item => {
            const fileName = item.fileName || item.name || '';
            return fileName.toLowerCase().includes(this.keyword.toLowerCase());
            });
        }
    },
    methods: {
        getOriginalIndex(item) {
            return this.fileList.findIndex(file => file === item);
        },
        getData() {
            this.$store.dispatch('authGetRequest',{
                url: this.$path.app_knowledgeBase_get,
                params: {
                    id: this.curId
                }
            }).then(res => {
                if(res.success) {
                    this.formData = res.data
                    this.fileList = res.data.attUrl ? JSON.parse(res.data.attUrl) : []
                    console.log(this.fileList,'fileList');
                    if (this.fileList.length > 0) {
                        this.curTab = 0;
                    }
                    
                } else {
                    this.$Modal.error({
                        title: '温馨提示',
                        content: res.msg || '接口操作失败!'
                    })
                }
            })
        },
        toPage(item){
            const index = this.getOriginalIndex(item);
            if (index !== -1) {
                this.curTab = index;
            }
        },
        downLoadImg(url,filename) {
            // 如果未指定文件名，则从URL中提取或生成一个
            if (!filename) {
                const urlParts = url.split('/');
                filename = urlParts[urlParts.length - 1];
                
                // 如果提取的文件名没有扩展名，添加.jpg
                if (!filename.includes('.')) {
                filename = `image_${Date.now()}.jpg`;
                }
            }
            
            this.loading = true;
            this.$Message.info('开始下载图片...');
            
            // 创建一个临时的img元素来处理图片
            const img = new Image();
            
            // 处理跨域图片
            img.crossOrigin = 'anonymous';
            
            img.onload = () => {
                // 创建canvas元素来绘制图片
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                
                // 设置canvas尺寸与图片一致
                canvas.width = img.width;
                canvas.height = img.height;
                
                // 绘制图片到canvas
                ctx.drawImage(img, 0, 0);
                
                // 将canvas内容转换为Blob
                canvas.toBlob((blob) => {
                if (!blob) {
                    this.$Message.error('图片转换失败', 'error');
                    this.loading = false;
                    return;
                }
                
                // 创建下载链接
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                
                // 触发下载
                document.body.appendChild(a);
                a.click();
                
                // 清理资源
                setTimeout(() => {
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                    this.loading = false;
                    this.$Message.error('图片下载成功', 'success');
                }, 100);
                }, 'image/jpeg'); // 指定图片格式
            };
            
            img.onerror = (error) => {
                console.error('图片加载失败:', error);
                this.$Message.error('图片下载失败，请检查图片URL是否有效', 'error');
                this.loading = false;
            };
            
            // 设置图片源，触发加载
            img.src = url;
        },
        // 文件下载方法（优化版）
        downloadFile(url, filename = '') {
            this.fileDownloading = true;
            this.$Message.info('开始下载文件...');
            
            // 处理文件名
            if (!filename) {
                const urlParts = url.split('/');
                filename = urlParts[urlParts.length - 1];
                
                // 如果没有文件扩展名，根据类型添加
                if (!filename.includes('.')) {
                if (this.currentItem.type.includes('word')) {
                    filename += '.docx';
                } else if (this.currentItem.type.includes('excel')) {
                    filename += '.xlsx';
                }
                }
            }
            
            // 创建XMLHttpRequest对象
            const xhr = new XMLHttpRequest();
            xhr.open('GET', url, true);
            xhr.responseType = 'blob';
            
            xhr.onload = () => {
                if (xhr.status === 200) {
                const blob = xhr.response;
                const downloadUrl = window.URL.createObjectURL(blob);
                
                // 处理不同浏览器的下载方式
                if (window.navigator.msSaveOrOpenBlob) {
                    // IE专用方法
                    window.navigator.msSaveOrOpenBlob(blob, filename);
                } else {
                    // 标准浏览器方法
                    const a = document.createElement('a');
                    a.href = downloadUrl;
                    a.download = filename;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                }
                
                // 释放内存
                window.URL.revokeObjectURL(downloadUrl);
                this.fileDownloading = false;
                this.$Message.success('文件下载成功');
                } else {
                this.fileDownloading = false;
                this.$Message.error('文件下载失败: 服务器响应异常');
                }
            };
            
            xhr.onerror = () => {
                this.fileDownloading = false;
                this.$Message.error('文件下载失败: 网络错误');
            };
            
            xhr.send();
        },
        toback(){
            this.$emit('toback')
        },
    }
}
</script>

<style lang="less" scoped>
.jgzsk-info{
    width: 100%;
    height: calc(~'100vh - 285px');
    margin-top: 10px;
    .jgzsk-content{
        width: 100%;
        // height: calc(~'100% - 136px');
        height: 100%;
        display: flex;
        background: #eee;
        padding: 10px;
        .jgzsk-left{
            flex: none;
            width: 350px;
            height: 100%;
            background: #fff;
            padding: 10px;
            margin-right: 10px;
            .catalogue-header {
                font-weight: bold;
                font-size: 18px;
                color: #415060;
                line-height: 40px;
                margin-bottom: 10px;
                padding-left: 6px;
                display: flex;
                align-items: center;
                justify-content: space-between;
            }
            .catalogue-input {
                margin-bottom: 10px;
            }
            .catalogue-content {
                height: ~"calc(100% - 128px)";
                overflow: auto !important;
                p {
                    width: 100%;
                    padding: 0 16px;
                    line-height: 40px;
                    border-radius: 4px;
                    color: #272c2c;
                    cursor: pointer;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    &.active {
                        color:#2b5fda;// #00afb8;
                        background:#cfe2fa;// #e5f7f7;
                    }
                    &.no-click {
                        cursor: not-allowed;
                    }
                    &:not(.no-click):hover {
                        // background: #e5f7f7;
                        background:#cfe2fa;
                    }
                    span {
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        display: inline-block; /* 或者 block，确保生效 */
                        max-width: 100%;
                        font-size: 14px;
                    }
                }
            }
            .catalogue-close {
                display: flex;
                justify-content: center;
            }
            .archive-content {
                flex: 1;
                position: relative;
                background: #f5f7fa;
                display: flex;
                flex-direction: column;
                align-items: center;
                padding: 16px;
                border-radius: 4px;
                overflow: auto;
                .archive-paper-wrapper {
                    margin-bottom: 16px;
                    // padding: 40px;
                    background: #ffffff;
                    border-radius: 4px;
                    position: relative;
                }
            }
        }
        .jgzsk-right{
            flex: 1;
            height: 100%;
            background: #fff;
            padding: 10px;
            .jgzsk-right-content{
                margin-top: 5px;
                width: 100%;
                height: calc(~'100% - 45px');
                overflow: auto;
                .img-container{
                    width: 100%;
                    height: 100%;
                    padding: 5px 130px;
                    background: #525659;
                    img{
                        width: 100%;
                        // height: 100%;
                    }
                }
                .video-container{
                    width: 100%;
                    height: 100%;
                    padding: 5px 130px;
                    background: #525659;
                    video{
                        width: 100%;
                        height: 100%;
                    }
                }
                .file-container{
                    width: 100%;
                    // height: 100%;
                    background: #fff;
                }
                .pdf-container{
                    width: 100%;
                    height: 100%;
                }
            }
        }
    }
}

/deep/.file-upload .ivu-upload{
    display: none !important;
}
.right-header{
    width: 100%; 
    display: flex; 
    justify-content: space-between; 
    align-items: center; 
    border-bottom: 1px solid #dcdee2;
}
.f-20{
    cursor: pointer;
}
</style>