<template>
  <div>
    <div>
      <Form :model="formData" ref="form" :label-width="80" @submit.native.prevent>
        <div style="padding:10px; border: 1px solid #efefef; display: flex; flex-direction: row;">
          <FormItem label="事件名称" prop="typeName" style="margin-bottom: 0;">
            <Input v-model="formData.typeName" placeholder="请输入事件名称"></Input>
          </FormItem>
          <div style="margin-left: 15px;">
            <Button type="primary" ghost @click="handleReset">重置</Button>
            <Button style="margin-left: 10px;" type="primary" @click="handleSearch">搜索</Button>
          </div>
        </div>
      </Form>
    </div>
    <div class="item-box">
      <div v-for="item in list" :key="item.id"
        style="border:1px solid #efefef; padding: 10px;border-radius: 5px;width: 300px">
        <div style="display: flex; justify-content: space-between;">
          <h3>{{ item.typeName }}</h3>
          <div style="display: flex; align-items: center;">
            <span :style="{ color: item.isEnabled === 1 ? 'green' : 'red', marginRight: '15px' }">已{{ item.isEnabled
              ===
              1 ? '开启' : '关闭' }}</span>
            <i-switch size="small" :true-value="1" :false-value="0" :value="item.isEnabled"
              @on-change="handleStatusChange(item)"></i-switch>
          </div>
        </div>
        <div style="display: flex; margin: 15px 0;">
          <div style="margin-right: 25px;">
            <Icon :size="48" style="color: #69beff" type="md-notifications" />
          </div>
          <div>
            <p>创建人：{{ item.addUserName }}</p>
            <p>创建时间：{{ item.addTime }}</p>
          </div>
        </div>
        <div class="btn-group">
          <Button type="primary" @click="toDetail(item)">查看</Button>
          <template v-if="item.isBuiltin === '0'">
            <Button type="primary" @click="toUpdate(item)">修改</Button>
            <Button type="error" @click="toDelete(item)">删除</Button>
          </template>
        </div>
      </div>
      <div @click="toAdd"
        style="border: 1px dashed #69beff;cursor:pointer;width:280px;padding: 10px;display: flex;flex-direction:column; justify-content: center; align-items: center;">
        <Icon :size="64" style="color: #69beff" type="md-add" />
        <span style="color: #69beff">新增所情类型</span>
      </div>
    </div>
    <Modal v-model="modal2Delete" title="提示" @on-ok="handleDelete" @on-cancel="() => modal2Delete = false">
      <p style="font-size: 16px; margin-bottom: 15px;">你确定要删除此条目么？</p>
    </Modal>
    <Operate ref="operateModal" @refresh="refresh" />
  </div>
</template>

<script>
import { mapActions } from "vuex";
import api from "./api";
import { Operate } from "./components";
export default {
  name: "institutionSituationMange",
  components: {
    Operate
  },
  data() {
    return {
      formData: {},
      list: [],
      selectedItem: {},
      modal2Delete: false,
    };
  },
  created() {
    this.getList()
  },
  mounted() { },
  methods: {
    ...mapActions([
      "authGetRequest",
      "authPostRequest",
    ]),
    getList(params) {
      this.loading = true;
      this.authPostRequest({
        url: api.list,
        params: params || {}
      }).then(res => {
        this.loading = false;
        if (res.success) {
          this.list = res.data;
        } else {
          this.$Message.error("获取所情类型配置失败");
        }
      })
    },
    handleReset() {
      this.formData.typeName = "";
      this.getList();
    },
    handleSearch() {
      this.getList({ typeName: this.formData.typeName })
    },
    toAdd() {
      this.toOperate('add', "新增所情事件类型", null);
    },
    toDetail(data) {
      this.toOperate('detail', "所情事件类型详情", data);
    },
    toUpdate(data) {
      this.toOperate('update', "修改所情事件类型", data);
    },
    toOperate(type, title, data) {
      this.$refs.operateModal.open(type, title, data);
    },
    refresh() {
      this.getList();
    },
    handleStatusChange(e) {
      this.authGetRequest({
        url: api.changeStatus,
        params: {
          id: e.id
        }
      }).then(res => {
        if (res.success) {
          this.getList();
        } else {
          this.$Message.error("修改所情类型配置失败");
          this.getList();
        }
      })
    },
    toDelete(item) {
      this.modal2Delete = true;
      this.selectedItem = item;
    },
    handleDelete() {
      this.authGetRequest({
        url: api.del,
        params: {
          ids: this.selectedItem.id
        },
      }).then((res) => {
        this.model2Delete = false;
        if (res.success) {
          this.$Message.success("已成功删除");
          this.getList();
        } else {
          this.errorModal({ content: res.msg || "删除失败" });
        }
      });
    },
  }
};
</script>

<style lang="less" scoped>
.item-box {
  padding: 20px 10px 10px;
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.btn-group {
  display: flex;
  justify-content: flex-end;

  >button:not(:first-of-type) {
    margin-left: 15px;
  }
}
</style>
