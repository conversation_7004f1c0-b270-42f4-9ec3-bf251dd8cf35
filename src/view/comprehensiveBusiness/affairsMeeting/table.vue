<template>
    <div>
       <rs-DataGrid ref="grid" funcMark="zhgl-swhylb" :customFunc="false" v-if="!showAdd">
            <template v-slot:zhgl-swhylb:xjhy="{ oper }">
                <Button type="primary" class="header-button" @click.native="handleAdd('add')">
                    <Icon :type="oper.iconPath ? oper.iconPath : 'md-add'" size="20" />
                    {{ oper.name }}
                </Button>
            </template>
            <template v-slot:zhgl-swhylb:bj="{ oper, row, index }">
                <Button type="primary" class="row-button" @click="handleEdit(row)">
                    <Icon v-if="oper.iconPath" :type="oper.iconPath" />
                    {{ oper.name }}
                </Button>&nbsp;&nbsp;
            </template>
            <template v-slot:zhgl-swhylb:ck="{ oper, row, index }">
                <Button type="error" size="small" class="row-button" @click="handleDetails(row)">
                    <Icon v-if="oper.iconPath" :type="oper.iconPath" />
                    {{ oper.name }}
                </Button>&nbsp;&nbsp;
            </template>
        </rs-DataGrid>

        <div v-if="showAdd" class="bsp-base-form" style="padding: 16px;">
            <component :is="component" @on_show_table="on_show_table" :dataMsg="dataMsg" :type="type" />
        </div>
    </div>
</template>
<script>
import addMeeting from './addMeeting.vue'
import meetingDetails from "./meetingDetails.vue"
export default {
    name: '',
    components: {
    addMeeting,meetingDetails
    },

    data() {
        return {
            showAdd: false,
            dataMsg: {},
            type: '',
            component:null
        }
    },
    methods: {
        on_show_table() {
            this.showAdd = false
            this.component = null
        },
        handleAdd() {
            this.component = 'addMeeting'
            this.showAdd = true
            this.type = 'add'
        },
        handleEdit(row) {
            this.component = 'addMeeting'
            this.showAdd = true
            this.type = 'edit'
            this.dataMsg = row
        },
        handleDetails(row) {
            this.component = 'meetingDetails'
            this.showAdd = true
            this.dataMsg = row
            if (row.attachment) {
                this.dataMsg.attachment = JSON.parse(row.attachment)
            }
            console.log(row);

        }
    }
}



</script>