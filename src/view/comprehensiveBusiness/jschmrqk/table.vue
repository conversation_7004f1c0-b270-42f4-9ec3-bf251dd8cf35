<template>
    <div style="width: 100%;">
        <rs-DataGrid ref="grid" funcMark="jschmrqk" :customFunc="false" v-if="!showAdd">
            <template v-slot:jschmrqk:add="{ oper }">
                <Button type="primary" class="header-button" @click.native="handleAdd('add')">
                    <Icon :type="oper.iconPath ? oper.iconPath : 'md-add'" size="20" />
                    {{ oper.name }}
                </Button>
            </template>
            <template v-slot:jschmrqk:edit="{ oper, row, index }">
                <Button type="primary" class="row-button" @click="handleEdit(row)">
                    <Icon v-if="oper.iconPath" :type="oper.iconPath" />
                    {{ oper.name }}
                </Button>&nbsp;&nbsp;
            </template>
            <template v-slot:jschmrqk:build="{ oper, row, index }">
                <Button type="info" class="row-button" @click="handleBuild(row)" v-if="row.status == '0'">
                    <Icon v-if="oper.iconPath" :type="oper.iconPath" />
                    {{ oper.name }}
                </Button>&nbsp;&nbsp;
            </template>
            <template v-slot:jschmrqk:rebuild="{ oper, row, index }">
                <Button type="info" class="row-button" @click="handleReBuild(row)" v-if="row.status == '1'">
                    <Icon v-if="oper.iconPath" :type="oper.iconPath" />
                    {{ oper.name }}
                </Button>&nbsp;&nbsp;
            </template>
            <template v-slot:jschmrqk:downloadword="{ oper, row, index }">
                <Button type="info" class="row-button" @click="handleDownloadWord(row)">
                    <Icon v-if="oper.iconPath" :type="oper.iconPath" />
                    {{ oper.name }}
                </Button>&nbsp;&nbsp;
            </template>
            <template v-slot:jschmrqk:downloadpdf="{ oper, row, index }">
                <Button type="info" class="row-button" @click="handleDownloadPdf(row)">
                    <Icon v-if="oper.iconPath" :type="oper.iconPath" />
                    {{ oper.name }}
                </Button>
            </template>
        </rs-DataGrid>
        <Modal v-model="openModal" :mask-closable="false" :closable="true" class-name="select--modal" width="800px"
            :title="modalTitle">
            <Form ref="chmrqkForm" v-if="openModal" :model="chmrqkData" :label-width="140" :label-icon="true"
                style="margin:0 16px;height: 100%;">
                <Row>
                    <Col span="24">
                    <FormItem label="汇报日期:" prop="reportDate" :rules="[
                        {
                            trigger: 'blur,change',
                            message: '请输入汇报日期',
                            required: true,
                        },
                    ]">
                        <el-date-picker v-model="chmrqkData.reportDate" type="date" placeholder="选择日期"
                            style="width: 300px" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
                        </el-date-picker>
                    </FormItem>
                    </Col>
                </Row>
                <Row>
                    <Col span="24">
                    <FormItem label="汇报人:" prop="reportUser" :rules="[
                        {
                            trigger: 'blur,change',
                            message: '请输入汇报人',
                            required: true,
                        },
                    ]">
                        <el-input v-model="chmrqkData.reportUser" style="width: 300px" />
                    </FormItem>
                    </Col>
                </Row>
                <Row>
                    <Col span="24">
                    <FormItem label="汇报单位:" prop="reportOrg" :rules="[
                        {
                            trigger: 'blur,change',
                            message: '请输入汇报单位',
                            required: true,
                        },
                    ]">
                        <el-input v-model="chmrqkData.reportOrg" style="width: 300px" />
                    </FormItem>
                    </Col>
                </Row>
                <Row>
                    <Col span="24">
                    <FormItem label="工作落实情况:" prop="gzlsqk" style="width: 100%">
                        <el-input type="textarea" v-model="chmrqkData.gzlsqk" :rows="5" />
                    </FormItem>
                    </Col>
                </Row>
                <Row>
                    <Col span="24">
                    <FormItem label="其他突出情况:" prop="tcqk" style="width: 100%">
                        <el-input type="textarea" v-model="chmrqkData.tcqk" :rows="5" />
                    </FormItem>
                    </Col>
                </Row>
            </Form>
            <div slot="footer">
                <Button @click="openModal = false">取消</Button>
                <Button type="primary" @click="saveChmrqk" :loading="loading">提交</Button>
            </div>
        </Modal>
    </div>
</template>

<script>

import { getUserCache } from "@/libs/util";

export default {
    name: 'Jschmrqk',
    data() {
        return {
            dataMsg: {},
            type: null,
            openModal: false,
            modalTitle: '',
            chmrqkData: {},
            loading: false,
            showAdd:false
        }
    },
    methods: {
        handleAdd() {
            this.modalTitle = '新建每日情况'
            this.openModal = true
            this.chmrqkData = {
                reportUser: getUserCache.getUserName() + 'abcdef',
                reportOrg: getUserCache.getOrgName()
            }
        },
        handleEdit(row) {
            this.modalTitle = '修改每日情况'
            let params = { id: row.id }
            this.$store
                .dispatch("getRequest", {
                    url: this.$path.zh_chmrqk_get,
                    params: params,
                })
                .then((resp) => {
                    if (resp.success) {
                        this.chmrqkData = resp.data
                        this.openModal = true
                    } else {}
                });
        },
        handleBuild(row) {
            alert(row.id)
        },
        handleReBuild(row) {
            alert(row.id)
        },
        handleDownloadWord(row){
            alert(row.id)
        },
        handleDownloadPdf(row){
            alert(row.id)
        },
        saveChmrqk() {
            this.loading = true
            this.$nextTick(() => {
                this.$refs['chmrqkForm'].validate((valid) => {
                    console.log(this.$refs['chmrqkForm'])
                    console.log(valid)
                    console.log(this.chmrqkData)
                    if(valid){
                        let params = this.chmrqkData
                        let saveUrl = this.chmrqkData.id ? this.$path.zh_chmrqk_update : this.$path.zh_chmrqk_create
                        let operName = (this.chmrqkData.id ? '创建' : '更新')
                        this.$store
                            .dispatch("authPostRequest", {
                                url: saveUrl,
                                params,
                            })
                            .then((res) => {
                                if (res.success) {
                                    this.on_refresh_table();
                                    this.$Message.success({content: "晨会每日情况" + operName + "成功", duration: 3});
                                    this.openModal = false
                                    this.loading = false
                                }
                                else{
                                    this.$Message.success({content: res.msg || "晨会每日情况" + operName + "成功", duration: 5});
                                    this.loading = false
                                }
                            });
                    } else {
                        this.$Message.error({content: "验证不通过", duration: 3});
                        this.loading = false
                    }
                })
            })
        },
        on_refresh_table() {
            this.$refs.grid.query_grid_data(1)
        }
    }
}
</script>