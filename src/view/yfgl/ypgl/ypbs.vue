<template>
    <div>
        <div class="bsp-base-form" style="background: #fff;">
            <div class="bsp-base-tit" >{{ modalTitle }}</div>
            <div class="bsp-base-content1">
                <div class="ypcg-header ypcgFlex">
                    <p>
                        <span><i class="total">报损人：</i></span><span><i class="total">{{ userName
                                }}</i></span>
                    </p>
                    <Button type="primary" style="font-size: 16px" @click="addRow">增&nbsp;行</Button>
                </div>
                <div style="padding: 0 16px">
                    <Table :key="tableKey" :columns="columnsInfo" :data="dataTable" height="590" tooltip-theme="light"
                        :tooltip-max-width="300" border>

                        <template slot-scope="{ row,index }" slot="dosageForm">
                            <s-dicgrid placeholder="请选择" style="margin-top: 8px;" v-model='row.dosageForm' disabled
                                ref="dicGrid" dicName='ZD_DOSAGE_FORM' />
                        </template>

                        <template slot-scope="{ row,index }" slot="medicineInId">
                            <Select v-model="row.medicineInId" @on-change="changeItem(index, row)" placeholder="请选择">
                                <Option v-for="item in yppcList" :value="item.id" :key="item.id">{{ item.batchCode }}
                                </Option>
                            </Select>
                        </template>



                        <template slot-scope="{ row,index }" slot="lossNum">
                            <div style="width: 100%;display: flex;justify-content: space-evenly;align-items: center;">
                                <InputNumber :min="0" v-model="row.num" @on-change="changeItem(index, row)"
                                    placeholder="请输入" type="number" style="width: 40%;"></InputNumber>
                                <Select v-model="row.dw" @on-change="changeItem(index, row)" placeholder="-（计量单位）"
                                    style="width: 50%;">
                                    <Option v-for="item in row.dwList" :key="item.value" :value="item.value">{{
                                        item.title }}</Option>
                                </Select>
                                <Tooltip placement="top" :content="row.content">
                                    <Icon type="ios-help-circle" size="24" color="#2d8cf0" />
                                </Tooltip>
                            </div>
                        </template>


                        <template slot-scope="{ row,index }" slot="lossTime">
                            <DatePicker @on-change="changeDateItem($event, index, row)" v-model="row.lossTime" transfer
                                type="datetime" format="yyyy-MM-dd HH:mm:ss" placeholder="请选择">
                            </DatePicker>
                        </template>

                        <template slot-scope="{ row,index }" slot="lossReason">
                            <s-dicgrid placeholder="请选择" style="margin-top: 8px;" @change="changeItem(index, row)"
                                v-model='row.lossReason' ref="dicGrid" dicName='ZD_LOSS_REASON' />
                        </template>

                        <template slot-scope="{ row,index }" slot="remark">
                            <div style="padding: 10px 0;">
                                <Input v-model="row.remark" @on-change="changeItem(index, row)" maxlength="125"
                                    type="textarea" show-word-limit :autosize="{ minRows: 3, maxRows: 5 }"
                                    placeholder="请输入" />
                            </div>
                        </template>

                        <template slot-scope="{ row, index }" slot="action">
                            <Button type="error" size="small" @click="remove(row, index, 'del')">删除</Button>
                        </template>
                    </Table>
                </div>
            </div>
            <div class="bsp-base-fotter">
                <Button @click="on_return_table(false)">返 回</Button>
                <Button type="primary" @click="handleSubmit" :loading="spinShow">保 存</Button>
            </div>
        </div>

    </div>
</template>

<script>
import { sDataGrid } from "sd-data-grid";
import { mapActions } from "vuex";
import { getUserCache } from '@/libs/util'


export default {
    components: {
        sDataGrid,
    },
    props: {
        modalTitle: String,
        detailId: String,
        syncAction: String
    },
    data() {
        return {
            spinShow: false,
            dicData: null,
            userName: getUserCache.getUserName(),
            paramentData: {},
            rowData: {},
            tableKey: Math.random(),
            columnsInfo: [
                {
                    title: '药品基本信息',
                    align: 'center',
                    children: [{
                        type: "index",
                        width: 80,
                        align: "center",
                        title: "序号",
                    },
                    {
                        title: "批准文号",
                        key: "approvalNum",
                        width: 150,
                        align: "center",
                        tooltip: true
                    },
                    {
                        title: "药品名称",
                        key: "medicineName",
                        width: 150,
                        align: "center",
                        tooltip: true
                    },
                    {
                        title: "剂型",
                        align: "center",
                        width: 150,
                        slot: "dosageForm"
                    },
                    {
                        title: "规格",
                        width: 150,
                        key: "specs",
                        align: "center",
                        tooltip: true
                    },
                    // {
                    //   title: "类型",
                    //   key: "specs",
                    //   align: "center",
                    // },
                    {
                        title: "生产单位",
                        key: "productUnit",
                        align: "center",
                        width: 150,
                        tooltip: true
                    },]
                },
                {
                    title: '报损信息',
                    align: 'center',
                    children: [
                        {
                            title: "药品批次",
                            slot: "medicineInId",
                            align: "center",
                            width: 200,
                        },
                        {
                            title: "数量",
                            slot: "lossNum",
                            align: "center",
                            width: 350,
                        },
                        {
                            title: "报损时间",
                            slot: "lossTime",
                            align: "center",
                            width: 200,
                        },
                        {
                            title: "报损原因",
                            slot: "lossReason",
                            align: "center",
                            width: 200,
                        },
                        {
                            title: "备注",
                            slot: "remark",
                            align: "center",
                            width: 200,
                        }

                    ]
                },
                {
                    title: "操作",
                    slot: "action",
                    align: "center",
                    width: 150,
                },
            ],
            dataTable: [],
            yppcList: []
        };
    },
    mounted() {
        this.$dicKit.getDataRows("ZD_MEASUREMENT_UNIT").then(res => {
            this.dicData = res
        })

        this.authPostRequest({
            url: this.$path.get_yppcList,
            params: {
                medicineId: this.detailId
            }
        }).then(res => {
            if (res.success) {
                this.yppcList = res.data
            }
        })


        this.getTableData()
    },

    methods: {
        ...mapActions(["postRequest", "authGetRequest", "authPostRequest"]),
        changeDateItem(e, index, row) {
            row.lossTime = e
            this.$set(this.dataTable, index, row)
        },
        changeItem(index, row) {
            if (row.dw == 1) {
                row.num1 = row.num ? row.num : 0
            } else {
                row.num1 = row.num / this.paramentData.unitConversionRatio ? parseFloat((row.num / this.paramentData.unitConversionRatio).toFixed(5)) : 0
            }
            if (row.dw == 1) {
                row.num2 = row.num * this.paramentData.unitConversionRatio ? row.num * this.paramentData.unitConversionRatio : 0
            } else {
                row.num2 = row.num ? row.num : 0
            }
            if (this.paramentData.unitConversionRatio && this.paramentData.unitConversionRatio != 1) {
                row.content = row.num1 + this.filterName(this.paramentData.measurementUnit) + " / " + row.num2 + this.filterName(this.paramentData.minMeasurementUnit)
            } else {
                row.content = row.num1 + this.filterName(this.paramentData.measurementUnit)
            }

            if (row.dw == 1) {
                row.lossNum = parseFloat((row.num * this.paramentData.unitConversionRatio).toFixed(5))
            } else {
                row.lossNum = row.num
            }
            this.$set(this.dataTable, index, row)
        },
        filterName(e) {
            if (this.dicData) {
                if (e && e != '') {
                    return this.dicData.filter(i => {
                        return i.code == e
                    })[0].name
                } else {
                    return "-"
                }
            }
        },
        getTableData() {
            this.$store
                .dispatch("authGetRequest", {
                    url: this.$path.get_medicine_detail,
                    params: { id: this.detailId },
                }).then(res => {
                    if (res.success) {
                        this.paramentData = res.data
                        this.paramentData.content = "-"
                        this.paramentData.dw = 1
                        this.paramentData.num = 0
                        this.paramentData.dwList = [
                            {
                                title: this.filterName(this.paramentData.measurementUnit) + "（计量单位）",
                                value: 1
                            },
                            {
                                title: this.filterName(this.paramentData.minMeasurementUnit) + "（最小计量单位）",
                                value: 2
                            }
                        ]

                        this.dataTable.push(this.paramentData)
                    } else {

                    }
                })
        },

        showError(msg) {
            this.$Notice.error({
                title: "错误提示",
                desc: msg,
            });
        },

        handleSubmit() {
            this.dataTable.forEach(e => {
                e.medicineId = e.id
                e.id = ""
            });
            let params = {
                id: this.detailId,
                medicineLossList: this.dataTable
            }

            let rulesList = []
            this.dataTable.forEach(e => {
                if (!e.medicineInId || e.medicineInId == '') {
                    rulesList.push(e)
                    this.showError('药品批次不能为空')
                } else if (!e.lossNum || e.lossNum == '' || e.lossNum == 0) {
                    rulesList.push(e)
                    this.showError('报损数量不能为空')
                } else if (!e.lossTime || e.lossTime == '') {
                    rulesList.push(e)
                    this.showError('报损时间不能为空')
                } else if (!e.lossReason || e.lossReason == '') {
                    rulesList.push(e)
                    this.showError('报损原因不能为空')
                }
            })
            if (rulesList.length > 0) {
                return
            }


            this.$store
                .dispatch("authPostRequest", {
                    url: this.$path.add_medicine_loss,
                    params,
                })
                .then((resp) => {
                    if (resp.code == 0) {
                        this.spinShow = false;
                        this.$Notice.success({
                            title: "提示",
                            desc: resp.msg || "保存成功",
                        });
                        this.on_return_table();
                    } else {
                        this.spinShow = false;
                        this.$Notice.error({
                            title: "错误提示",
                            desc: resp.msg || "保存失败",
                        });
                    }
                });
        },
        addRow() {
            this.dataTable.push(this.paramentData);
        },

        on_return_table(isRefreshTable) {
            this.$emit("on_show_table", isRefreshTable);
        },
        remove(row, index, tag) {
            this.$Modal.confirm({
                title: "是否确认删除该条数据？",
                loading: true,
                onOk: async () => {
                    this.dataTable.splice(index, 1);
                    this.$Modal.remove();
                },
            });
        },
    },
};
</script>

<style>
.ypcgFlex {
    display: flex;
    align-content: center;
    justify-content: space-between;
}

.ypcg-header {
    padding: 16px;
    font-size: 16px;
}

i {
    font-style: normal;
}

.total {
    font-weight: bold;
    padding: 0 5px;
}

.editBox .ivu-input {
    border: none;
    border-bottom: 1px solid rgb(127, 127, 238);
    background: transparent !important;
    border-radius: 0 !important;
    text-align: center;
}

.editBox:focus-visible {
    outline: none !important;
}
</style>