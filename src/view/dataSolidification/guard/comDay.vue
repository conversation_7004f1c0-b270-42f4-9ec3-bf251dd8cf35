<template>
  <div class="bsp-base-form">

    <div class="bsp-base-subtit">
      <p class="subtit">
        <Icon type="md-list-box" size="24" color="#2b5fda"/>
        监所基本情况
      </p>
      <div class="form">
        <div class="data-item color-0">
          <Icon type="ios-contacts-outline" class="data-icon"/>
          <div class="data">
            <div class="title">被监管人员总数</div>
            <div class="num">{{ msgData?.bjgry || '0' }}</div>
          </div>
        </div>
        <div class="data-item color-2">
          <Icon type="ios-man-outline" class="data-icon"/>
          <div class="data">
            <div class="title">男性被监管人员数</div>
            <div class="num">{{ msgData?.bjgryMale || '0' }}</div>
          </div>
        </div>
        <div class="data-item color-1">
          <Icon type="ios-woman-outline" class="data-icon"/>
          <div class="data">
            <div class="title">女性被监管人员数</div>
            <div class="num">{{ msgData?.bjgryFemale || '0' }}</div>
          </div>
        </div>
        <div class="data-item color-4">
          <Icon type="ios-body-outline" class="data-icon"/>
          <div class="data">
            <div class="title">未成年被监管人员数</div>
            <div class="num">{{ msgData?.bjgryMinor || '0' }}</div>
          </div>
        </div>
        <div class="data-item color-0">
          <Icon type="ios-warning-outline" class="data-icon"/>
          <div class="data">
            <div class="title">一级重大风险</div>
            <div class="num">{{ msgData?.yjzdfx || '0' }}</div>
          </div>
        </div>
        <div class="data-item color-3">
          <Icon type="ios-warning" class="data-icon"/>
          <div class="data">
            <div class="title">二级重大风险</div>
            <div class="num">{{ msgData?.ejzdfx || '0' }}</div>
          </div>
        </div>
        <div class="data-item color-4">
          <Icon type="md-warning" class="data-icon"/>
          <div class="data">
            <div class="title">三级重大风险</div>
            <div class="num">{{ msgData?.sjzdfx || '0' }}</div>
          </div>
        </div>
        <div class="data-item color-2">
          <Icon type="ios-construct-outline" class="data-icon"/>
          <div class="data">
            <div class="title">戒具使用</div>
            <div class="num">{{ msgData?.jgsy || '0' }}</div>
          </div>
        </div>
        <div class="data-item color-3">
          <Icon type="md-git-compare" class="data-icon"/>
          <div class="data">
            <div class="title">精神异常</div>
            <div class="num">{{ msgData?.jsyc || '0' }}</div>
          </div>
        </div>
        <div class="data-item color-1">
          <Icon type="ios-barcode-outline" class="data-icon"/>
          <div class="data">
            <div class="title">等待精神鉴定人数</div>
            <div class="num">{{ msgData?.ddjsjd || '0' }}</div>
          </div>
        </div>
        <div class="data-item color-0">
          <Icon type="ios-mic-outline" class="data-icon"/>
          <div class="data">
            <div class="title">谈话教育人数</div>
            <div class="num">{{ msgData?.thjy || '0' }}</div>
          </div>
        </div>
        <div class="data-item color-1">
          <Icon type="md-contact" class="data-icon"/>
          <div class="data">
            <div class="title">调整监室人数</div>
            <div class="num">{{ msgData?.jstz || '0' }}</div>
          </div>
        </div>
        <div class="data-item color-0">
          <Icon type="md-person" class="data-icon"/>
          <div class="data">
            <div class="title">特殊身份人数</div>
            <div class="num">{{ msgData?.tssf || '0' }}</div>
          </div>
        </div>
        <div class="data-item color-2">
          <Icon type="md-globe" class="data-icon" />
          <div class="data">
            <div class="title">外籍人员</div>
            <div class="num">{{ msgData?.wjry || '0' }}</div>
          </div>
        </div>
        <div class="data-item color-3">
          <Icon type="md-time" class="data-icon" />
          <div class="data">
            <div class="title">65岁以上人员数</div>
            <div class="num">{{ msgData?.oldry || '0' }}</div>
          </div>
        </div>
        <div class="data-item color-4">
          <Icon type="md-log-in" class="data-icon" />
          <div class="data">
            <div class="title">每日入所人数</div>
            <div class="num">{{ msgData?.mrrs || '0' }}</div>
          </div>
        </div>
        <div class="data-item color-0">
          <Icon type="md-log-out" class="data-icon" />
          <div class="data">
            <div class="title">每日出所人数</div>
            <div class="num">{{ msgData?.mrcs || '0' }}</div>
          </div>
        </div>
      </div>
      <p class="subtit">
        <Icon type="md-list-box" size="24" color="#2b5fda"/>
        诉讼阶段
      </p>
      <div class="form">
        <div class="data-item color-2">
          <Icon type="md-list" class="data-icon"/>
          <div class="data">
            <div class="title">侦查阶段人数</div>
            <div class="num">{{ msgData?.zcjd || '0' }}</div>
          </div>
        </div>
        <div class="data-item color-3">
          <Icon type="md-git-network" class="data-icon"/>
          <div class="data">
            <div class="title">审查阶段人数</div>
            <div class="num">{{ msgData?.scjd || '0' }}</div>
          </div>
        </div>
        <div class="data-item color-4">
          <Icon type="md-cash" class="data-icon"/>
          <div class="data">
            <div class="title">审判阶段人数</div>
            <div class="num">{{ msgData?.spjd || '0' }}</div>
          </div>
        </div>
      </div>
      <p class="subtit">
        <Icon type="md-list-box" size="24" color="#2b5fda"/>
        执行阶段
      </p>
      <div class="form">
        <div class="data-item color-0">
          <Icon type="ios-cube-outline" class="data-icon"/>
          <div class="data">
            <div class="title">待交付执行人数</div>
            <div class="num">{{ msgData?.djfzx || '0' }}</div>
          </div>
        </div>
        <div class="data-item color-1">
          <Icon type="ios-podium-outline" class="data-icon"/>
          <div class="data">
            <div class="title">留所服刑人数</div>
            <div class="num">{{ msgData?.lsfx || '0' }}</div>
          </div>
        </div>
        <div class="data-item color-2">
          <Icon type="ios-list-box-outline" class="data-icon"/>
          <div class="data">
            <div class="title">亲属会见人数</div>
            <div class="num">{{ msgData?.qshj || '0' }}</div>
          </div>
        </div>
      </div>
      <p class="subtit">
        <Icon type="md-list-box" size="24" color="#2b5fda"/>
        强制措施
      </p>
      <div class="form">
        <div class="data-item color-3">

          <Icon type="ios-options-outline" class="data-icon"/>
          <div class="data">
            <div class="title">刑事拘留人数</div>
            <div class="num">{{ msgData?.xsjl || '0' }}</div>
          </div>
        </div>
        <div class="data-item color-4">

          <Icon type="logo-instagram" class="data-icon"/>
          <div class="data">
            <div class="title">逮捕人数</div>
            <div class="num">{{ msgData?.db || '0' }}</div>
          </div>
        </div>
      </div>
      <p class="subtit">
        <Icon type="md-list-box" size="24" color="#2b5fda"/>
        变更强制措施
      </p>
      <div class="form">
        <div class="data-item color-0">
          <Icon type="ios-git-merge" class="data-icon"/>
          <div class="data">
            <div class="title">取保候审人数</div>
            <div class="num">{{ msgData?.qbhs || '0' }}</div>
          </div>
        </div>
        <div class="data-item color-1">
          <Icon type="ios-home-outline" class="data-icon"/>
          <div class="data">
            <div class="title">监室居住人数</div>
            <div class="num">{{ msgData?.jsjz || '0' }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>


<script>


export default {
  name: 'jztj',
  props: {
    msgData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {}
  },
  methods: {}
}
</script>


<style scoped lang="less">
.bsp-base-form {
  position: relative;
}

.form {
  padding: 10px;
  // display: flex;
  gap: 15px 15px;
  // flex-wrap: wrap;
  display: grid;
  grid-template-columns: repeat(auto-fill, 320px);

  .data-item {
    display: flex;
    align-items: center;
    padding: 20px;
    border: 1px solid #2db7f5;
    background: linear-gradient(45deg, #0081ff, #1cbbb4);
    border-radius: 6px;


    .data-icon {
      font-size: 30px;
      color: #fff;
    }

    .data {
      margin-left: 20px;
      color: #fff;

      .title {
        font-size: 24px;
      }

      .num {
        font-size: 22px;

      }
    }


  }

  .color-0 {
    background: linear-gradient(45deg, #0081ff, #1cbbb4);
  }

  .color-1 {
    background: linear-gradient(135deg, #fda085, #f6d365);
  }

  .color-2 {
    background: linear-gradient(135deg, #35bef7, #5bc9f8, #80d4f9, #a6defa, #c7e9fb);
  }

  .color-3 {
    background: linear-gradient(135deg, #001f3f, #0088a9, #00c9a7, #92d5c6, #b2e4c1);
  }

  .color-4 {
    background: linear-gradient(110.6deg, rgb(179, 157, 219) 7%, rgb(150, 159, 222) 47.7%, rgb(24, 255, 255) 100.6%);
  }
}
</style>
