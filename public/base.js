// eslint-disable-next-line no-unused-vars
let bucketName = 'bjsdskss'
const serverConfig = {
  // 应用ID
  APP_ID: '1934985431515009024', //'1901545604329377792',
  APP_PREFIX: true,
  APP_MARK: bucketName, // 'bjsdskss',
  APP_CODE: bucketName, // 'bjsdskss',
  APP_NAME: '北京市第三看守所实战平台',
  // 版本号
  version: '1.0.0',
  // 版权
  copyRight: 'copyright © 2025 高新兴科技集团股份有限公司 版权所有',
  severHttp: '', // http://192.168.3.251/proxy-image/
  minioHttp: 'http://192.168.3.251:9010/',
  systemId: '1934985431515009024', //'1906902626012893184',  //获取当前系统下的应用
  taskMark: 'yrydrwrk', //任务入口标识
  businessMark: 'yrydywfz', //辅助业务标识
  performance: 'lzqk', //履职情况标识
  isShowDmsSmsc: true, //DMS是否显示扫描上传
  //硬件相关配置
  scanType: 'none', // 扫描仪类型: dyname-大名扫描仪 winMage-影源扫描仪 none-无扫描仪
  bmzjShowPDFUpload: true, // 编目组卷本地上传，true：图片、pdf，false：图片
  uploadType: 1,
  // 桶名
  // BUCKET_NAME: 'acp', /// bucket  暂时没用到 不确定bsp是否使用到
  bucketName: bucketName, // 'bjsdskss',
  OSS_SERVICE_MARK: bucketName, // 'bjsdskss',
  menuMode: 'side', //top 是一级在顶部  二级在左侧   side表示全部在左侧
  svrPath: "http://192.168.3.219:8000/automationsvr/v1/", //信息采集服务地址
}
var ImsServerConfig = serverConfig // 给案管组件使用
